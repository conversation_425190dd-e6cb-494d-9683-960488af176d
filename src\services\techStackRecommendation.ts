// Tech stack recommendation engine
import { ProjectType, Complexity, TechStack } from '../types/project';
import { Platform } from '../types/api';

export interface TechStackRecommendation {
  stack: TechStack;
  reasoning: string[];
  alternatives: TechStack[];
  pros: string[];
  cons: string[];
  learningCurve: 'easy' | 'moderate' | 'steep';
  popularity: number; // 1-10 scale
  jobMarket: number; // 1-10 scale
}

export class TechStackRecommendationEngine {
  private static stackDatabase = {
    frontend: {
      'React': {
        popularity: 9,
        learningCurve: 'moderate',
        pros: ['Large ecosystem', 'Great community', 'Flexible'],
        cons: ['Steep learning curve', 'Frequent updates'],
        bestFor: ['web', 'mobile']
      },
      'Vue.js': {
        popularity: 7,
        learningCurve: 'easy',
        pros: ['Easy to learn', 'Great documentation', 'Progressive'],
        cons: ['Smaller ecosystem', 'Less job opportunities'],
        bestFor: ['web']
      },
      'Angular': {
        popularity: 6,
        learningCurve: 'steep',
        pros: ['Full framework', 'TypeScript first', 'Enterprise ready'],
        cons: ['Complex', 'Opinionated', 'Large bundle size'],
        bestFor: ['web']
      },
      'React Native': {
        popularity: 8,
        learningCurve: 'moderate',
        pros: ['Cross-platform', 'Code reuse', 'Native performance'],
        cons: ['Platform-specific bugs', 'Bridge overhead'],
        bestFor: ['mobile']
      },
      'Flutter': {
        popularity: 7,
        learningCurve: 'moderate',
        pros: ['Fast development', 'Single codebase', 'Great UI'],
        cons: ['Dart language', 'Large app size'],
        bestFor: ['mobile']
      }
    },
    backend: {
      'Node.js': {
        popularity: 9,
        learningCurve: 'easy',
        pros: ['JavaScript everywhere', 'Fast development', 'Great for APIs'],
        cons: ['Single-threaded', 'Callback hell'],
        bestFor: ['web', 'mobile', 'api']
      },
      'Python': {
        popularity: 8,
        learningCurve: 'easy',
        pros: ['Easy to learn', 'Great for AI/ML', 'Rich libraries'],
        cons: ['Slower execution', 'GIL limitations'],
        bestFor: ['ai', 'web']
      },
      'Java': {
        popularity: 7,
        learningCurve: 'moderate',
        pros: ['Enterprise ready', 'Stable', 'Great performance'],
        cons: ['Verbose', 'Slower development'],
        bestFor: ['web', 'enterprise']
      },
      'Go': {
        popularity: 6,
        learningCurve: 'moderate',
        pros: ['Fast compilation', 'Great concurrency', 'Simple'],
        cons: ['Limited libraries', 'Young ecosystem'],
        bestFor: ['api', 'microservices']
      }
    },
    database: {
      'PostgreSQL': {
        popularity: 8,
        learningCurve: 'moderate',
        pros: ['ACID compliant', 'Feature rich', 'Open source'],
        cons: ['Complex setup', 'Resource intensive'],
        bestFor: ['web', 'enterprise']
      },
      'MongoDB': {
        popularity: 7,
        learningCurve: 'easy',
        pros: ['Flexible schema', 'Easy to scale', 'JSON-like'],
        cons: ['No ACID', 'Memory intensive'],
        bestFor: ['web', 'mobile']
      },
      'Redis': {
        popularity: 8,
        learningCurve: 'easy',
        pros: ['Very fast', 'In-memory', 'Multiple data types'],
        cons: ['Memory limited', 'Data persistence'],
        bestFor: ['caching', 'sessions']
      },
      'SQLite': {
        popularity: 6,
        learningCurve: 'easy',
        pros: ['Serverless', 'Zero config', 'Lightweight'],
        cons: ['Limited concurrency', 'No network access'],
        bestFor: ['simple', 'prototyping']
      }
    }
  };

  static recommendTechStack(
    projectType: ProjectType,
    complexity: Complexity,
    platforms: Platform[],
    requirements?: string[]
  ): TechStackRecommendation {
    const recommendation = this.generateRecommendation(projectType, complexity, platforms, requirements);
    return recommendation;
  }

  private static generateRecommendation(
    projectType: ProjectType,
    complexity: Complexity,
    platforms: Platform[],
    requirements?: string[]
  ): TechStackRecommendation {
    const stack = this.selectOptimalStack(projectType, complexity, platforms);
    const reasoning = this.generateReasoning(projectType, complexity, platforms, stack);
    const alternatives = this.generateAlternatives(stack, projectType);
    const { pros, cons } = this.analyzeStackProsAndCons(stack);
    const learningCurve = this.assessLearningCurve(stack);
    const popularity = this.calculatePopularity(stack);
    const jobMarket = this.assessJobMarket(stack);

    return {
      stack,
      reasoning,
      alternatives,
      pros,
      cons,
      learningCurve,
      popularity,
      jobMarket
    };
  }

  private static selectOptimalStack(
    projectType: ProjectType,
    complexity: Complexity,
    platforms: Platform[]
  ): TechStack {
    const stacks: Record<ProjectType, Record<Complexity, TechStack>> = {
      web: {
        simple: {
          frontend: ['React', 'TypeScript', 'Tailwind CSS'],
          backend: ['Next.js', 'API Routes'],
          database: ['SQLite', 'Prisma'],
          deployment: ['Vercel', 'Netlify'],
          tools: ['ESLint', 'Prettier', 'Git']
        },
        medium: {
          frontend: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS'],
          backend: ['Node.js', 'Express', 'JWT'],
          database: ['PostgreSQL', 'Prisma'],
          deployment: ['AWS', 'Docker'],
          tools: ['Jest', 'Cypress', 'GitHub Actions']
        },
        complex: {
          frontend: ['React', 'TypeScript', 'Next.js', 'PWA'],
          backend: ['Node.js', 'GraphQL', 'Microservices'],
          database: ['PostgreSQL', 'Redis', 'MongoDB'],
          deployment: ['AWS', 'Kubernetes', 'CloudFront'],
          tools: ['Docker', 'Terraform', 'Monitoring Stack']
        }
      },
      mobile: {
        simple: {
          frontend: ['React Native', 'TypeScript', 'Expo'],
          backend: ['Firebase', 'Node.js'],
          database: ['Firebase Firestore'],
          deployment: ['App Store', 'Google Play'],
          tools: ['Expo CLI', 'Firebase Console']
        },
        medium: {
          frontend: ['React Native', 'TypeScript', 'Redux Toolkit'],
          backend: ['Node.js', 'Express', 'Socket.io'],
          database: ['MongoDB', 'Redis'],
          deployment: ['AWS', 'App Store', 'Google Play'],
          tools: ['Expo', 'Flipper', 'Detox']
        },
        complex: {
          frontend: ['React Native', 'TypeScript', 'MobX'],
          backend: ['Node.js', 'GraphQL', 'Microservices'],
          database: ['PostgreSQL', 'Redis', 'Elasticsearch'],
          deployment: ['Kubernetes', 'AWS', 'CI/CD'],
          tools: ['Docker', 'Terraform', 'Monitoring']
        }
      },
      ai: {
        simple: {
          frontend: ['React', 'TypeScript', 'Chart.js'],
          backend: ['Python', 'FastAPI', 'OpenAI API'],
          database: ['SQLite', 'Vector DB'],
          deployment: ['Heroku', 'Vercel'],
          tools: ['Jupyter', 'Pandas', 'NumPy']
        },
        medium: {
          frontend: ['React', 'TypeScript', 'D3.js'],
          backend: ['Python', 'FastAPI', 'TensorFlow'],
          database: ['PostgreSQL', 'Vector DB', 'S3'],
          deployment: ['AWS', 'Docker'],
          tools: ['MLflow', 'Jupyter', 'Weights & Biases']
        },
        complex: {
          frontend: ['React', 'TypeScript', 'WebGL'],
          backend: ['Python', 'PyTorch', 'Kubernetes'],
          database: ['PostgreSQL', 'Redis', 'Vector DB'],
          deployment: ['AWS', 'Kubernetes', 'MLOps'],
          tools: ['Kubeflow', 'Airflow', 'Prometheus']
        }
      },
      ecommerce: {
        simple: {
          frontend: ['React', 'TypeScript', 'Tailwind CSS'],
          backend: ['Next.js', 'Stripe API'],
          database: ['PostgreSQL', 'Prisma'],
          deployment: ['Vercel', 'Stripe'],
          tools: ['Stripe Dashboard', 'Analytics']
        },
        medium: {
          frontend: ['React', 'TypeScript', 'Next.js'],
          backend: ['Node.js', 'Express', 'Stripe'],
          database: ['PostgreSQL', 'Redis'],
          deployment: ['AWS', 'Docker'],
          tools: ['Stripe', 'Mailchimp', 'Analytics']
        },
        complex: {
          frontend: ['React', 'TypeScript', 'Next.js', 'React Native'],
          backend: ['Node.js', 'GraphQL', 'Microservices'],
          database: ['PostgreSQL', 'Redis', 'Elasticsearch'],
          deployment: ['AWS', 'Kubernetes', 'CDN'],
          tools: ['Stripe', 'PayPal', 'Analytics Suite']
        }
      },
      form: {
        simple: {
          frontend: ['React', 'TypeScript', 'React Hook Form'],
          backend: ['Next.js', 'API Routes'],
          database: ['SQLite', 'Prisma'],
          deployment: ['Vercel', 'Netlify'],
          tools: ['Nodemailer', 'CSV Export']
        },
        medium: {
          frontend: ['React', 'TypeScript', 'Formik'],
          backend: ['Node.js', 'Express', 'Multer'],
          database: ['PostgreSQL', 'S3'],
          deployment: ['AWS', 'Docker'],
          tools: ['Zapier', 'Google Analytics', 'AWS S3']
        },
        complex: {
          frontend: ['React', 'TypeScript', 'Next.js'],
          backend: ['Node.js', 'GraphQL', 'Workflow Engine'],
          database: ['PostgreSQL', 'Redis', 'S3'],
          deployment: ['AWS', 'Kubernetes'],
          tools: ['Stripe', 'Zapier', 'Workflow Tools']
        }
      }
    };

    return stacks[projectType][complexity];
  }

  private static generateReasoning(
    projectType: ProjectType,
    complexity: Complexity,
    platforms: Platform[],
    stack: TechStack
  ): string[] {
    const reasoning: string[] = [];

    // Frontend reasoning
    if (stack.frontend.includes('React')) {
      reasoning.push('React chosen for its large ecosystem and flexibility');
    }
    if (stack.frontend.includes('React Native')) {
      reasoning.push('React Native enables cross-platform mobile development');
    }
    if (stack.frontend.includes('TypeScript')) {
      reasoning.push('TypeScript provides type safety and better developer experience');
    }

    // Backend reasoning
    if (stack.backend.includes('Node.js')) {
      reasoning.push('Node.js allows JavaScript across the full stack');
    }
    if (stack.backend.includes('Python')) {
      reasoning.push('Python excels in AI/ML applications with rich libraries');
    }
    if (stack.backend.includes('GraphQL')) {
      reasoning.push('GraphQL provides efficient data fetching for complex applications');
    }

    // Database reasoning
    if (stack.database.includes('PostgreSQL')) {
      reasoning.push('PostgreSQL offers robust ACID compliance and advanced features');
    }
    if (stack.database.includes('MongoDB')) {
      reasoning.push('MongoDB provides flexible schema for rapid development');
    }
    if (stack.database.includes('Redis')) {
      reasoning.push('Redis enables high-performance caching and session management');
    }

    // Complexity-based reasoning
    if (complexity === 'simple') {
      reasoning.push('Simple stack chosen for rapid prototyping and quick deployment');
    } else if (complexity === 'complex') {
      reasoning.push('Enterprise-grade technologies selected for scalability and maintainability');
    }

    return reasoning;
  }

  private static generateAlternatives(stack: TechStack, projectType: ProjectType): TechStack[] {
    const alternatives: TechStack[] = [];

    // Generate 2-3 alternative stacks
    if (projectType === 'web') {
      alternatives.push({
        frontend: ['Vue.js', 'TypeScript', 'Nuxt.js'],
        backend: ['Python', 'Django', 'DRF'],
        database: ['PostgreSQL', 'Redis'],
        deployment: ['AWS', 'Docker'],
        tools: ['Pytest', 'Celery', 'Nginx']
      });

      alternatives.push({
        frontend: ['Angular', 'TypeScript', 'Angular Material'],
        backend: ['Java', 'Spring Boot', 'Spring Security'],
        database: ['PostgreSQL', 'H2'],
        deployment: ['AWS', 'Docker'],
        tools: ['JUnit', 'Maven', 'Jenkins']
      });
    }

    return alternatives;
  }

  private static analyzeStackProsAndCons(stack: TechStack): { pros: string[]; cons: string[] } {
    const pros: string[] = [];
    const cons: string[] = [];

    // Analyze each technology in the stack
    [...stack.frontend, ...stack.backend, ...stack.database].forEach(tech => {
      const techData = this.findTechData(tech);
      if (techData) {
        pros.push(...techData.pros);
        cons.push(...techData.cons);
      }
    });

    // Remove duplicates and limit to most relevant
    return {
      pros: [...new Set(pros)].slice(0, 5),
      cons: [...new Set(cons)].slice(0, 3)
    };
  }

  private static findTechData(tech: string) {
    for (const category of Object.values(this.stackDatabase)) {
      if (category[tech]) {
        return category[tech];
      }
    }
    return null;
  }

  private static assessLearningCurve(stack: TechStack): 'easy' | 'moderate' | 'steep' {
    const allTechs = [...stack.frontend, ...stack.backend, ...stack.database];
    const curves = allTechs.map(tech => {
      const techData = this.findTechData(tech);
      return techData?.learningCurve || 'moderate';
    });

    const steepCount = curves.filter(c => c === 'steep').length;
    const easyCount = curves.filter(c => c === 'easy').length;

    if (steepCount > curves.length / 2) return 'steep';
    if (easyCount > curves.length / 2) return 'easy';
    return 'moderate';
  }

  private static calculatePopularity(stack: TechStack): number {
    const allTechs = [...stack.frontend, ...stack.backend, ...stack.database];
    const popularities = allTechs.map(tech => {
      const techData = this.findTechData(tech);
      return techData?.popularity || 5;
    });

    return Math.round(popularities.reduce((sum, p) => sum + p, 0) / popularities.length);
  }

  private static assessJobMarket(stack: TechStack): number {
    // Job market assessment based on technology popularity and demand
    const marketScores: Record<string, number> = {
      'React': 9,
      'Node.js': 8,
      'TypeScript': 9,
      'Python': 9,
      'PostgreSQL': 8,
      'AWS': 9,
      'Docker': 8,
      'Kubernetes': 7
    };

    const allTechs = [...stack.frontend, ...stack.backend, ...stack.database, ...stack.deployment];
    const scores = allTechs.map(tech => marketScores[tech] || 5);
    
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  }
}