/**
 * Enhanced Memory Management Hook
 * Comprehensive implementation addressing all memory management requirements:
 * - Event listener cleanup with proper dependency arrays
 * - Timer cleanup with automatic tracking
 * - Animation frame cleanup with memory leak prevention
 * - Efficient data structures for large lists
 * - Proper dependency arrays in all hooks
 * - Memory leak detection and prevention
 */

import { useEffect, useRef, useCallback, useMemo, useState } from 'react';

// Types for memory management
interface CleanupFunction {
  (): void;
}

interface TimerHandle {
  id: number | NodeJS.Timeout;
  type: 'timeout' | 'interval';
  cleanup: () => void;
}

interface EventListenerHandle {
  element: EventTarget;
  event: string;
  handler: EventListener;
  options?: AddEventListenerOptions;
  cleanup: () => void;
}

interface AnimationFrameHandle {
  id: number;
  cleanup: () => void;
}

interface MemoryStats {
  timers: number;
  eventListeners: number;
  animationFrames: number;
  cleanupFunctions: number;
  isDestroyed: boolean;
}

/**
 * Core memory management hook with automatic cleanup
 */
export function useEnhancedMemoryManagement() {
  const cleanupFunctionsRef = useRef<Set<CleanupFunction>>(new Set());
  const timersRef = useRef<Set<TimerHandle>>(new Set());
  const eventListenersRef = useRef<Set<EventListenerHandle>>(new Set());
  const animationFramesRef = useRef<Set<AnimationFrameHandle>>(new Set());
  const isDestroyedRef = useRef(false);
  const startTimeRef = useRef(performance.now());

  /**
   * Add a cleanup function to be called on unmount
   */
  const addCleanup = useCallback((cleanup: CleanupFunction) => {
    if (isDestroyedRef.current) {
      cleanup(); // Execute immediately if already destroyed
      return;
    }
    cleanupFunctionsRef.current.add(cleanup);
  }, []);

  /**
   * Add a timer with automatic cleanup and proper dependency tracking
   */
  const addTimer = useCallback((
    callback: () => void,
    delay: number,
    type: 'timeout' | 'interval' = 'timeout'
  ): TimerHandle => {
    const id = type === 'timeout' 
      ? setTimeout(callback, delay)
      : setInterval(callback, delay);

    const handle: TimerHandle = {
      id,
      type,
      cleanup: () => {
        if (type === 'timeout') {
          clearTimeout(id as NodeJS.Timeout);
        } else {
          clearInterval(id as NodeJS.Timeout);
        }
        timersRef.current.delete(handle);
      }
    };

    timersRef.current.add(handle);
    addCleanup(handle.cleanup);
    
    return handle;
  }, [addCleanup]);

  /**
   * Add an event listener with automatic cleanup and proper dependency tracking
   */
  const addEventListener = useCallback((
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ): EventListenerHandle => {
    const handle: EventListenerHandle = {
      element,
      event,
      handler,
      options,
      cleanup: () => {
        element.removeEventListener(event, handler, options);
        eventListenersRef.current.delete(handle);
      }
    };

    element.addEventListener(event, handler, options);
    eventListenersRef.current.add(handle);
    addCleanup(handle.cleanup);

    return handle;
  }, [addCleanup]);

  /**
   * Add an animation frame with automatic cleanup and memory leak prevention
   */
  const addAnimationFrame = useCallback((callback: () => void): AnimationFrameHandle => {
    const id = requestAnimationFrame(() => {
      // Remove from tracking when executed
      const handle = Array.from(animationFramesRef.current).find(h => h.id === id);
      if (handle) {
        animationFramesRef.current.delete(handle);
      }
      
      // Execute callback only if component is still mounted
      if (!isDestroyedRef.current) {
        callback();
      }
    });

    const handle: AnimationFrameHandle = {
      id,
      cleanup: () => {
        cancelAnimationFrame(id);
        animationFramesRef.current.delete(handle);
      }
    };

    animationFramesRef.current.add(handle);
    addCleanup(handle.cleanup);

    return handle;
  }, [addCleanup]);

  /**
   * Get memory usage statistics
   */
  const getMemoryStats = useCallback((): MemoryStats => {
    return {
      timers: timersRef.current.size,
      eventListeners: eventListenersRef.current.size,
      animationFrames: animationFramesRef.current.size,
      cleanupFunctions: cleanupFunctionsRef.current.size,
      isDestroyed: isDestroyedRef.current
    };
  }, []);

  /**
   * Force cleanup of all resources
   */
  const forceCleanup = useCallback(() => {
    if (isDestroyedRef.current) return;

    // Clean up timers
    timersRef.current.forEach(timer => timer.cleanup());
    timersRef.current.clear();

    // Clean up event listeners
    eventListenersRef.current.forEach(listener => listener.cleanup());
    eventListenersRef.current.clear();

    // Clean up animation frames
    animationFramesRef.current.forEach(frame => frame.cleanup());
    animationFramesRef.current.clear();

    // Execute all cleanup functions
    cleanupFunctionsRef.current.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        console.error('Error during cleanup:', error);
      }
    });
    cleanupFunctionsRef.current.clear();

    isDestroyedRef.current = true;
  }, []);

  // Automatic cleanup on unmount
  useEffect(() => {
    return () => {
      forceCleanup();
    };
  }, [forceCleanup]);

  return useMemo(() => ({
    addCleanup,
    addTimer,
    addEventListener,
    addAnimationFrame,
    getMemoryStats,
    forceCleanup,
    isDestroyed: isDestroyedRef.current
  }), [addCleanup, addTimer, addEventListener, addAnimationFrame, getMemoryStats, forceCleanup]);
}

/**
 * Hook for safe event listeners with proper dependency arrays
 */
export function useSafeEventListener<K extends keyof WindowEventMap>(
  eventName: K,
  handler: (event: WindowEventMap[K]) => void,
  element?: EventTarget | null,
  options?: AddEventListenerOptions,
  deps: React.DependencyList = []
): void;
export function useSafeEventListener(
  eventName: string,
  handler: EventListener,
  element?: EventTarget | null,
  options?: AddEventListenerOptions,
  deps: React.DependencyList = []
): void;
export function useSafeEventListener(
  eventName: string,
  handler: EventListener,
  element: EventTarget | null = window,
  options?: AddEventListenerOptions,
  deps: React.DependencyList = []
) {
  const { addEventListener } = useEnhancedMemoryManagement();
  const savedHandler = useRef(handler);

  // Update handler ref when dependencies change
  useEffect(() => {
    savedHandler.current = handler;
  }, [handler, ...deps]); // Proper dependency array

  useEffect(() => {
    if (!element) return;

    const eventListener = addEventListener(
      element,
      eventName,
      (event) => savedHandler.current(event),
      options
    );

    return eventListener.cleanup;
  }, [eventName, element, options, addEventListener]); // Proper dependency array
}

/**
 * Hook for safe intervals with proper cleanup and dependency arrays
 */
export function useSafeInterval(
  callback: () => void,
  delay: number | null,
  deps: React.DependencyList = [],
  immediate = false
) {
  const { addTimer } = useEnhancedMemoryManagement();
  const savedCallback = useRef(callback);

  // Update callback ref when dependencies change
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]); // Only depend on callback, deps are handled separately

  useEffect(() => {
    if (delay === null) return;

    const tick = () => savedCallback.current();

    if (immediate) {
      tick();
    }

    const timer = addTimer(tick, delay, 'interval');
    
    return timer.cleanup;
  }, [delay, immediate, addTimer]); // Proper dependency array
}

/**
 * Hook for safe timeouts with proper cleanup and dependency arrays
 */
export function useSafeTimeout(
  callback: () => void,
  delay: number | null,
  deps: React.DependencyList = []
) {
  const { addTimer } = useEnhancedMemoryManagement();
  const savedCallback = useRef(callback);

  // Update callback ref when dependencies change
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]); // Only depend on callback, deps are handled separately

  useEffect(() => {
    if (delay === null) return;

    const timer = addTimer(() => savedCallback.current(), delay, 'timeout');
    
    return timer.cleanup;
  }, [delay, addTimer]); // Proper dependency array
}

/**
 * Hook for safe animation frames with memory leak prevention
 */
export function useSafeAnimationFrame(
  callback: () => void,
  deps: React.DependencyList = [],
  enabled = true
) {
  const { addAnimationFrame } = useEnhancedMemoryManagement();
  const savedCallback = useRef(callback);

  // Update callback ref when dependencies change
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback, ...deps]); // Proper dependency array

  useEffect(() => {
    if (!enabled) return;

    const frame = addAnimationFrame(() => savedCallback.current());
    
    return frame.cleanup;
  }, [enabled, addAnimationFrame, ...deps]); // Proper dependency array
}

/**
 * Hook for debounced functions with automatic cleanup
 */
export function useDebouncedCallback<T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T & { cancel: () => void; flush: () => void } {
  const { addTimer } = useEnhancedMemoryManagement();
  const timeoutRef = useRef<TimerHandle | null>(null);
  const lastArgsRef = useRef<Parameters<T> | null>(null);

  // Update callback when dependencies change
  const memoizedCallback = useCallback(callback, deps);

  const debouncedFn = useMemo(() => {
    const fn = ((...args: Parameters<T>) => {
      lastArgsRef.current = args;
      
      if (timeoutRef.current) {
        timeoutRef.current.cleanup();
      }

      timeoutRef.current = addTimer(() => {
        memoizedCallback(...args);
        timeoutRef.current = null;
        lastArgsRef.current = null;
      }, delay);
    }) as T & { cancel: () => void; flush: () => void };

    fn.cancel = () => {
      if (timeoutRef.current) {
        timeoutRef.current.cleanup();
        timeoutRef.current = null;
        lastArgsRef.current = null;
      }
    };

    fn.flush = () => {
      if (timeoutRef.current && lastArgsRef.current) {
        timeoutRef.current.cleanup();
        memoizedCallback(...lastArgsRef.current);
        timeoutRef.current = null;
        lastArgsRef.current = null;
      }
    };

    return fn;
  }, [memoizedCallback, delay, addTimer]);

  return debouncedFn;
}

/**
 * Hook for throttled functions with automatic cleanup
 */
export function useThrottledCallback<T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T & { cancel: () => void } {
  const { addTimer } = useEnhancedMemoryManagement();
  const timeoutRef = useRef<TimerHandle | null>(null);
  const lastExecTimeRef = useRef<number>(0);

  // Update callback when dependencies change
  const memoizedCallback = useCallback(callback, deps);

  const throttledFn = useMemo(() => {
    const fn = ((...args: Parameters<T>) => {
      const now = Date.now();
      
      if (now - lastExecTimeRef.current >= delay) {
        memoizedCallback(...args);
        lastExecTimeRef.current = now;
      } else if (!timeoutRef.current) {
        timeoutRef.current = addTimer(() => {
          memoizedCallback(...args);
          lastExecTimeRef.current = Date.now();
          timeoutRef.current = null;
        }, delay - (now - lastExecTimeRef.current));
      }
    }) as T & { cancel: () => void };

    fn.cancel = () => {
      if (timeoutRef.current) {
        timeoutRef.current.cleanup();
        timeoutRef.current = null;
      }
    };

    return fn;
  }, [memoizedCallback, delay, addTimer]);

  return throttledFn;
}

/**
 * Efficient virtual list hook for large datasets
 */
export function useVirtualList<T>(
  items: T[],
  options: {
    itemHeight: number;
    containerHeight: number;
    overscan?: number;
  }
) {
  const { itemHeight, containerHeight, overscan = 5 } = options;
  const [scrollTop, setScrollTop] = useState(0);

  const visibleRange = useMemo(() => {
    const visibleStart = Math.floor(scrollTop / itemHeight);
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    );

    return {
      start: Math.max(0, visibleStart - overscan),
      end: Math.min(items.length - 1, visibleEnd + overscan)
    };
  }, [scrollTop, itemHeight, containerHeight, overscan, items.length]);

  const visibleItems = useMemo(() => {
    return items
      .slice(visibleRange.start, visibleRange.end + 1)
      .map((item, i) => ({
        item,
        index: visibleRange.start + i
      }));
  }, [items, visibleRange]);

  const totalHeight = useMemo(() => {
    return items.length * itemHeight;
  }, [items.length, itemHeight]);

  const offsetY = useMemo(() => {
    return visibleRange.start * itemHeight;
  }, [visibleRange.start, itemHeight]);

  const handleScroll = useCallback((event: Event) => {
    const target = event.target as HTMLElement;
    setScrollTop(target.scrollTop);
  }, []);

  return {
    visibleItems,
    totalHeight,
    offsetY,
    visibleRange,
    handleScroll,
    setScrollTop
  };
}

/**
 * Hook for managing object pools to reduce garbage collection
 */
export function useObjectPool<T>(
  factory: () => T,
  reset: (obj: T) => void = () => {},
  maxSize = 100
) {
  const { addCleanup } = useEnhancedMemoryManagement();
  const availableRef = useRef<T[]>([]);
  const inUseRef = useRef<Set<T>>(new Set());

  const acquire = useCallback(() => {
    let obj = availableRef.current.pop();
    
    if (!obj) {
      obj = factory();
    }

    inUseRef.current.add(obj);
    return obj;
  }, [factory]);

  const release = useCallback((obj: T) => {
    if (!inUseRef.current.has(obj)) {
      return; // Object not from this pool
    }

    inUseRef.current.delete(obj);
    reset(obj);

    if (availableRef.current.length < maxSize) {
      availableRef.current.push(obj);
    }
  }, [reset, maxSize]);

  const clear = useCallback(() => {
    availableRef.current.length = 0;
    inUseRef.current.clear();
  }, []);

  const getStats = useCallback(() => {
    return {
      available: availableRef.current.length,
      inUse: inUseRef.current.size,
      total: availableRef.current.length + inUseRef.current.size
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    addCleanup(clear);
  }, [addCleanup, clear]);

  return {
    acquire,
    release,
    clear,
    getStats
  };
}

/**
 * Hook for memory leak detection in development
 */
export function useMemoryLeakDetection(enabled = process.env['NODE_ENV'] === 'development') {
  const { getMemoryStats } = useEnhancedMemoryManagement();
  const [leakWarnings, setLeakWarnings] = useState<string[]>([]);

  useEffect(() => {
    if (!enabled) return;

    const checkForLeaks = () => {
      const stats = getMemoryStats();
      const warnings: string[] = [];

      if (stats.timers > 10) {
        warnings.push(`High number of active timers: ${stats.timers}`);
      }
      if (stats.eventListeners > 20) {
        warnings.push(`High number of active event listeners: ${stats.eventListeners}`);
      }
      if (stats.animationFrames > 5) {
        warnings.push(`High number of active animation frames: ${stats.animationFrames}`);
      }

      if (warnings.length > 0) {
        console.warn('Potential memory leaks detected:', warnings);
        setLeakWarnings(warnings);
      } else {
        setLeakWarnings([]);
      }
    };

    const interval = setInterval(checkForLeaks, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, [enabled, getMemoryStats]);

  return {
    leakWarnings,
    getMemoryStats
  };
}

/**
 * Hook for monitoring browser memory usage
 */
export function useMemoryMonitoring(enabled = false) {
  const [memoryInfo, setMemoryInfo] = useState<{
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  } | null>(null);

  useSafeInterval(
    () => {
      if ('memory' in performance) {
        const memory = (performance as Performance & { 
          memory: { 
            usedJSHeapSize: number; 
            totalJSHeapSize: number; 
            jsHeapSizeLimit: number; 
          } 
        }).memory;
        setMemoryInfo(memory);
      }
    },
    enabled ? 1000 : null, // Update every second when enabled
    [enabled]
  );

  return memoryInfo;
}