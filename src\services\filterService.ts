// Comprehensive filtering service for projects

import { Project, ProjectType, ProjectStatus, Priority, Complexity } from '../types/project';

export interface DateRange {
  start: Date | null;
  end: Date | null;
}

export interface FilterCriteria {
  status: ProjectStatus[];
  type: ProjectType[];
  priority: Priority[];
  complexity: Complexity[];
  tags: string[];
  dateRange: DateRange;
  progressRange: { min: number; max: number };
  isStarred?: boolean;
  hasRoute?: boolean;
}

export interface SortCriteria {
  field: keyof Project | 'relevance';
  direction: 'asc' | 'desc';
}

export interface FilterPreset {
  id: string;
  name: string;
  description: string;
  criteria: FilterCriteria;
  isDefault?: boolean;
  createdAt: string;
  lastUsed: string;
}

export interface FilterStats {
  totalProjects: number;
  filteredProjects: number;
  statusCounts: Record<ProjectStatus, number>;
  typeCounts: Record<ProjectType, number>;
  priorityCounts: Record<Priority, number>;
  complexityCounts: Record<Complexity, number>;
  tagCounts: Record<string, number>;
}

class FilterService {
  private static instance: FilterService;
  private presets: FilterPreset[] = [];
  private readonly STORAGE_KEY = 'rork-filter-presets';

  private constructor() {
    this.loadPresets();
    this.initializeDefaultPresets();
  }

  static getInstance(): FilterService {
    if (!FilterService.instance) {
      FilterService.instance = new FilterService();
    }
    return FilterService.instance;
  }

  /**
   * Apply filters to project list
   */
  filterProjects(projects: Project[], criteria: FilterCriteria): Project[] {
    return projects.filter(project => {
      // Status filter
      if (criteria.status.length > 0 && !criteria.status.includes(project.status)) {
        return false;
      }

      // Type filter
      if (criteria.type.length > 0 && !criteria.type.includes(project.type)) {
        return false;
      }

      // Priority filter
      if (criteria.priority.length > 0 && !criteria.priority.includes(project.priority)) {
        return false;
      }

      // Complexity filter
      if (criteria.complexity.length > 0 && !criteria.complexity.includes(project.complexity)) {
        return false;
      }

      // Tags filter (project must have at least one of the selected tags)
      if (criteria.tags.length > 0) {
        const hasMatchingTag = criteria.tags.some(tag => 
          project.tags.some(projectTag => 
            projectTag.toLowerCase().includes(tag.toLowerCase())
          )
        );
        if (!hasMatchingTag) {
          return false;
        }
      }

      // Date range filter
      if (criteria.dateRange.start || criteria.dateRange.end) {
        const projectDate = new Date(project.lastModified);
        
        if (criteria.dateRange.start && projectDate < criteria.dateRange.start) {
          return false;
        }
        
        if (criteria.dateRange.end && projectDate > criteria.dateRange.end) {
          return false;
        }
      }

      // Progress range filter
      if (project.progress < criteria.progressRange.min || project.progress > criteria.progressRange.max) {
        return false;
      }

      // Starred filter
      if (criteria.isStarred !== undefined && project.isStarred !== criteria.isStarred) {
        return false;
      }

      // Has route filter
      if (criteria.hasRoute !== undefined) {
        const hasRoute = Boolean(project.route);
        if (hasRoute !== criteria.hasRoute) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Sort projects based on criteria
   */
  sortProjects(projects: Project[], criteria: SortCriteria): Project[] {
    const sorted = [...projects];
    
    sorted.sort((a, b) => {
      let comparison = 0;
      
      switch (criteria.field) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'lastModified':
          comparison = new Date(a.lastModified).getTime() - new Date(b.lastModified).getTime();
          break;
        case 'createdAt':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'progress':
          comparison = a.progress - b.progress;
          break;
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'complexity':
          const complexityOrder = { complex: 3, medium: 2, simple: 1 };
          comparison = complexityOrder[a.complexity] - complexityOrder[b.complexity];
          break;
        default:
          comparison = 0;
      }
      
      return criteria.direction === 'desc' ? -comparison : comparison;
    });
    
    return sorted;
  }

  /**
   * Get filter statistics
   */
  getFilterStats(projects: Project[], filteredProjects: Project[]): FilterStats {
    const stats: FilterStats = {
      totalProjects: projects.length,
      filteredProjects: filteredProjects.length,
      statusCounts: {} as Record<ProjectStatus, number>,
      typeCounts: {} as Record<ProjectType, number>,
      priorityCounts: {} as Record<Priority, number>,
      complexityCounts: {} as Record<Complexity, number>,
      tagCounts: {}
    };

    // Count occurrences in all projects for filter options
    projects.forEach(project => {
      // Status counts
      stats.statusCounts[project.status] = (stats.statusCounts[project.status] || 0) + 1;
      
      // Type counts
      stats.typeCounts[project.type] = (stats.typeCounts[project.type] || 0) + 1;
      
      // Priority counts
      stats.priorityCounts[project.priority] = (stats.priorityCounts[project.priority] || 0) + 1;
      
      // Complexity counts
      stats.complexityCounts[project.complexity] = (stats.complexityCounts[project.complexity] || 0) + 1;
      
      // Tag counts
      project.tags.forEach(tag => {
        stats.tagCounts[tag] = (stats.tagCounts[tag] || 0) + 1;
      });
    });

    return stats;
  }

  /**
   * Create empty filter criteria
   */
  createEmptyFilter(): FilterCriteria {
    return {
      status: [],
      type: [],
      priority: [],
      complexity: [],
      tags: [],
      dateRange: { start: null, end: null },
      progressRange: { min: 0, max: 100 },
      isStarred: undefined,
      hasRoute: undefined
    };
  }

  /**
   * Check if filter criteria is empty
   */
  isFilterEmpty(criteria: FilterCriteria): boolean {
    return (
      criteria.status.length === 0 &&
      criteria.type.length === 0 &&
      criteria.priority.length === 0 &&
      criteria.complexity.length === 0 &&
      criteria.tags.length === 0 &&
      !criteria.dateRange.start &&
      !criteria.dateRange.end &&
      criteria.progressRange.min === 0 &&
      criteria.progressRange.max === 100 &&
      criteria.isStarred === undefined &&
      criteria.hasRoute === undefined
    );
  }

  /**
   * Save filter preset
   */
  savePreset(name: string, description: string, criteria: FilterCriteria): FilterPreset {
    const preset: FilterPreset = {
      id: Date.now().toString(),
      name,
      description,
      criteria: { ...criteria },
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString()
    };

    this.presets.push(preset);
    this.savePresets();
    
    return preset;
  }

  /**
   * Update filter preset
   */
  updatePreset(id: string, updates: Partial<Omit<FilterPreset, 'id' | 'createdAt'>>): FilterPreset | null {
    const index = this.presets.findIndex(p => p.id === id);
    if (index === -1) return null;

    this.presets[index] = {
      ...this.presets[index],
      ...updates,
      lastUsed: new Date().toISOString()
    };

    this.savePresets();
    return this.presets[index];
  }

  /**
   * Delete filter preset
   */
  deletePreset(id: string): boolean {
    const index = this.presets.findIndex(p => p.id === id);
    if (index === -1) return false;

    this.presets.splice(index, 1);
    this.savePresets();
    return true;
  }

  /**
   * Get all filter presets
   */
  getPresets(): FilterPreset[] {
    return [...this.presets].sort((a, b) => {
      // Default presets first, then by last used
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;
      return new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime();
    });
  }

  /**
   * Get preset by ID
   */
  getPreset(id: string): FilterPreset | null {
    return this.presets.find(p => p.id === id) || null;
  }

  /**
   * Use preset (update last used time)
   */
  usePreset(id: string): FilterPreset | null {
    const preset = this.getPreset(id);
    if (!preset) return null;

    preset.lastUsed = new Date().toISOString();
    this.savePresets();
    
    return preset;
  }

  /**
   * Initialize default presets
   */
  private initializeDefaultPresets(): void {
    const defaultPresets: Omit<FilterPreset, 'id' | 'createdAt' | 'lastUsed'>[] = [
      {
        name: 'Active Projects',
        description: 'Projects currently in progress',
        criteria: {
          ...this.createEmptyFilter(),
          status: ['active']
        },
        isDefault: true
      },
      {
        name: 'High Priority',
        description: 'High priority projects',
        criteria: {
          ...this.createEmptyFilter(),
          priority: ['high']
        },
        isDefault: true
      },
      {
        name: 'Starred Projects',
        description: 'Your favorite projects',
        criteria: {
          ...this.createEmptyFilter(),
          isStarred: true
        },
        isDefault: true
      },
      {
        name: 'Recent Projects',
        description: 'Projects modified in the last 7 days',
        criteria: {
          ...this.createEmptyFilter(),
          dateRange: {
            start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            end: null
          }
        },
        isDefault: true
      }
    ];

    // Add default presets if they don't exist
    defaultPresets.forEach(presetData => {
      const exists = this.presets.some(p => p.name === presetData.name && p.isDefault);
      if (!exists) {
        const preset: FilterPreset = {
          ...presetData,
          id: `default-${Date.now()}-${Math.random()}`,
          createdAt: new Date().toISOString(),
          lastUsed: new Date().toISOString()
        };
        this.presets.push(preset);
      }
    });

    this.savePresets();
  }

  /**
   * Load presets from localStorage
   */
  private loadPresets(): void {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        if (Array.isArray(parsed)) {
          this.presets = parsed.filter(this.isValidPreset);
        }
      }
    } catch (error) {
      console.error('Failed to load filter presets:', error);
      this.presets = [];
    }
  }

  /**
   * Save presets to localStorage
   */
  private savePresets(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.presets));
    } catch (error) {
      console.error('Failed to save filter presets:', error);
    }
  }

  /**
   * Validate preset structure
   */
  private isValidPreset(preset: any): preset is FilterPreset {
    return (
      preset &&
      typeof preset === 'object' &&
      typeof preset.id === 'string' &&
      typeof preset.name === 'string' &&
      typeof preset.description === 'string' &&
      preset.criteria &&
      typeof preset.criteria === 'object' &&
      typeof preset.createdAt === 'string' &&
      typeof preset.lastUsed === 'string'
    );
  }
}

export default FilterService;