import React, { forwardRef, useState } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { Eye, EyeOff } from 'lucide-react';
import { cn } from '../../utils/helpers';

interface InputProps extends Omit<HTMLMotionProps<'input'>, 'size'> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outline';
  showPasswordToggle?: boolean;
}

const inputVariants = {
  idle: { scale: 1, borderColor: 'rgb(209 213 219)' },
  focus: { 
    scale: 1.01, 
    borderColor: 'rgb(59 130 246)',
    boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)'
  },
  error: { 
    borderColor: 'rgb(239 68 68)',
    boxShadow: '0 0 0 3px rgba(239, 68, 68, 0.1)'
  }
};

const labelVariants = {
  idle: { y: 0, scale: 1, color: 'rgb(107 114 128)' },
  focus: { y: -2, scale: 0.95, color: 'rgb(59 130 246)' },
  error: { color: 'rgb(239 68 68)' }
};

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  size = 'md',
  variant = 'default',
  showPasswordToggle = false,
  type = 'text',
  className,
  onFocus,
  onBlur,
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [hasValue, setHasValue] = useState(false);

  const inputType = showPasswordToggle && type === 'password' 
    ? (showPassword ? 'text' : 'password') 
    : type;

  const baseClasses = 'w-full rounded-lg border transition-colors focus:outline-none disabled:cursor-not-allowed disabled:opacity-50';
  
  const variantClasses = {
    default: 'border-gray-300 bg-white',
    filled: 'border-transparent bg-gray-100',
    outline: 'border-2 border-gray-300 bg-transparent'
  };
  
  const sizeClasses = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4 text-sm',
    lg: 'h-12 px-4 text-base'
  };

  const iconSizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    setHasValue(e.target.value.length > 0);
    onBlur?.(e);
  };

  const getAnimationState = () => {
    if (error) return 'error';
    if (isFocused) return 'focus';
    return 'idle';
  };

  return (
    <div className="w-full">
      {label && (
        <motion.label
          variants={labelVariants}
          animate={isFocused || hasValue ? 'focus' : error ? 'error' : 'idle'}
          className="block text-sm font-medium mb-1"
        >
          {label}
        </motion.label>
      )}
      
      <div className="relative">
        {leftIcon && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
            <div className={iconSizeClasses[size]}>
              {leftIcon}
            </div>
          </div>
        )}
        
        <motion.input
          ref={ref}
          type={inputType}
          variants={inputVariants}
          animate={getAnimationState()}
          className={cn(
            baseClasses,
            variantClasses[variant],
            sizeClasses[size],
            leftIcon && 'pl-10',
            (rightIcon || showPasswordToggle) && 'pr-10',
            error && 'border-red-300 focus:border-red-500',
            className
          )}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...props}
        />
        
        {(rightIcon || showPasswordToggle) && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            {showPasswordToggle && type === 'password' ? (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                {showPassword ? (
                  <EyeOff className={iconSizeClasses[size]} />
                ) : (
                  <Eye className={iconSizeClasses[size]} />
                )}
              </button>
            ) : (
              <div className={cn("text-gray-400", iconSizeClasses[size])}>
                {rightIcon}
              </div>
            )}
          </div>
        )}
      </div>
      
      {(error || helperText) && (
        <motion.p
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          className={cn(
            "mt-1 text-xs",
            error ? "text-red-600" : "text-gray-500"
          )}
        >
          {error || helperText}
        </motion.p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;