import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { mobileAnimations, useMobileAnimations } from '../../utils/mobileAnimations';

interface MobileAnimatedContainerProps {
  children: React.ReactNode;
  animation?: keyof typeof mobileAnimations;
  className?: string;
  delay?: number;
  onAnimationComplete?: () => void;
  isVisible?: boolean;
}

export const MobileAnimatedContainer: React.FC<MobileAnimatedContainerProps> = ({
  children,
  animation = 'fadeIn',
  className = '',
  delay = 0,
  onAnimationComplete,
  isVisible = true
}) => {
  const { getAnimation, reducedMotion } = useMobileAnimations();
  const animationVariant = getAnimation(animation);

  if (reducedMotion) {
    return <div className={className}>{children}</div>;
  }

  return (
    <AnimatePresence mode="wait">
      {isVisible && (
        <motion.div
          className={className}
          variants={animationVariant}
          initial="initial"
          animate="animate"
          exit="exit"
          transition={{ delay }}
          onAnimationComplete={onAnimationComplete}
          // Optimize for mobile performance
          style={{
            willChange: 'transform, opacity',
            backfaceVisibility: 'hidden',
            perspective: 1000
          }}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Staggered animation container for lists
export const MobileStaggerContainer: React.FC<{
  children: React.ReactNode;
  className?: string;
  staggerDelay?: number;
}> = ({ children, className = '', staggerDelay = 0.05 }) => {
  const { reducedMotion } = useMobileAnimations();

  if (reducedMotion) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      className={className}
      variants={mobileAnimations.mobileStagger}
      initial="initial"
      animate="animate"
      style={{
        willChange: 'transform',
        backfaceVisibility: 'hidden'
      }}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          variants={mobileAnimations.fadeIn}
          transition={{ delay: index * staggerDelay }}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};