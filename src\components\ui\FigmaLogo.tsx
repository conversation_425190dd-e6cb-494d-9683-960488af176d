import React from 'react';

interface FigmaLogoProps {
  className?: string;
  size?: number;
}

const FigmaLogo: React.FC<FigmaLogoProps> = ({ className = '', size = 20 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Orange/Red top rectangle */}
      <path
        d="M8 2C6.34315 2 5 3.34315 5 5C5 6.65685 6.34315 8 8 8H11V2H8Z"
        fill="#FF7262"
      />
      
      {/* Purple left circle */}
      <path
        d="M8 8C6.34315 8 5 9.34315 5 11C5 12.6569 6.34315 14 8 14C9.65685 14 11 12.6569 11 11C11 9.34315 9.65685 8 8 8Z"
        fill="#A259FF"
      />
      
      {/* Green bottom circle */}
      <path
        d="M8 14C6.34315 14 5 15.3431 5 17C5 18.6569 6.34315 20 8 20C9.65685 20 11 18.6569 11 17V14H8Z"
        fill="#1BD96A"
      />
      
      {/* Cyan right circle */}
      <path
        d="M16 8C17.6569 8 19 9.34315 19 11C19 12.6569 17.6569 14 16 14C14.3431 14 13 12.6569 13 11C13 9.34315 14.3431 8 16 8Z"
        fill="#0ACF83"
      />
      
      {/* Pink/Red top right */}
      <path
        d="M13 2V8H16C17.6569 8 19 6.65685 19 5C19 3.34315 17.6569 2 16 2H13Z"
        fill="#F24E1E"
      />
    </svg>
  );
};

export default FigmaLogo; 