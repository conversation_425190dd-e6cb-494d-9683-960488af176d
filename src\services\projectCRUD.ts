import { Project, ProjectType, ProjectStatus, Priority, Complexity } from '../types/project';
import { generateId } from '../utils/helpers';

export interface CreateProjectData {
  name: string;
  description: string;
  type: ProjectType;
  priority?: Priority;
  complexity?: Complexity;
  tags?: string[];
}

export interface UpdateProjectData extends Partial<Project> {
  id: string;
}

export class ProjectCRUDService {
  // Create project with immediate UI feedback
  static async createProject(
    data: CreateProjectData,
    onOptimisticUpdate: (project: Project) => void,
    onSuccess: (project: Project) => void,
    onError: (error: string) => void
  ): Promise<void> {
    // Create optimistic project
    const optimisticProject: Project = {
      id: generateId(),
      name: data.name,
      description: data.description,
      type: data.type,
      status: 'draft' as ProjectStatus,
      progress: 0,
      lastModified: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      priority: data.priority || 'medium',
      complexity: data.complexity || 'medium',
      tags: data.tags || [],
      isStarred: false
    };

    // Apply optimistic update immediately
    onOptimisticUpdate(optimisticProject);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // In a real app, this would be an API call
      // const response = await api.createProject(data);
      
      // For now, we'll just confirm the optimistic update
      onSuccess(optimisticProject);
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to create project');
    }
  }

  // Update project with real-time validation
  static async updateProject(
    data: UpdateProjectData,
    onOptimisticUpdate: (id: string, updates: Partial<Project>) => void,
    onSuccess: (project: Project) => void,
    onError: (error: string) => void
  ): Promise<void> {
    const updates = {
      ...data,
      lastModified: new Date().toISOString()
    };

    // Apply optimistic update immediately
    onOptimisticUpdate(data.id, updates);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // In a real app, this would be an API call
      // const response = await api.updateProject(data.id, updates);
      
      // For now, we'll just confirm the optimistic update
      onSuccess(updates as Project);
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to update project');
    }
  }

  // Delete with confirmation and undo functionality
  static async deleteProject(
    projectId: string,
    project: Project,
    onOptimisticUpdate: (id: string) => void,
    onSuccess: () => void,
    onError: (error: string) => void,
    onUndo?: (project: Project) => void
  ): Promise<void> {
    // Apply optimistic update immediately (remove from UI)
    onOptimisticUpdate(projectId);

    // Show undo notification if undo function provided
    if (onUndo) {
      // This would typically show a toast notification with undo button
      console.log('Project deleted. Undo available for 5 seconds.');
      
      // Set up undo timeout
      const undoTimeout = setTimeout(() => {
        // Permanent delete after timeout
        this.permanentDelete(projectId, onSuccess, onError);
      }, 5000);

      // Store undo function with timeout for potential use
      (window as any).__projectUndoData = {
        project,
        timeout: undoTimeout,
        undo: () => {
          clearTimeout(undoTimeout);
          onUndo(project);
        }
      };
    } else {
      // Immediate permanent delete
      this.permanentDelete(projectId, onSuccess, onError);
    }
  }

  private static async permanentDelete(
    projectId: string,
    onSuccess: () => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // In a real app, this would be an API call
      // await api.deleteProject(projectId);
      
      onSuccess();
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to delete project');
    }
  }

  // Duplicate project feature
  static async duplicateProject(
    originalProject: Project,
    onOptimisticUpdate: (project: Project) => void,
    onSuccess: (project: Project) => void,
    onError: (error: string) => void
  ): Promise<void> {
    // Create duplicate with new ID and modified name
    const duplicateProject: Project = {
      ...originalProject,
      id: generateId(),
      name: `${originalProject.name} (Copy)`,
      status: 'draft' as ProjectStatus,
      progress: 0,
      lastModified: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      isStarred: false
    };

    // Apply optimistic update immediately
    onOptimisticUpdate(duplicateProject);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 400));
      
      // In a real app, this would be an API call
      // const response = await api.createProject(duplicateProject);
      
      onSuccess(duplicateProject);
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to duplicate project');
    }
  }

  // Validation functions
  static validateProjectData(data: Partial<CreateProjectData>): string[] {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Project name is required');
    }

    if (data.name && data.name.length > 100) {
      errors.push('Project name must be less than 100 characters');
    }

    if (!data.description || data.description.trim().length === 0) {
      errors.push('Project description is required');
    }

    if (data.description && data.description.length > 500) {
      errors.push('Project description must be less than 500 characters');
    }

    if (!data.type) {
      errors.push('Project type is required');
    }

    return errors;
  }

  // Real-time validation for forms
  static validateField(field: keyof CreateProjectData, value: any): string | null {
    switch (field) {
      case 'name':
        if (!value || value.trim().length === 0) {
          return 'Project name is required';
        }
        if (value.length > 100) {
          return 'Project name must be less than 100 characters';
        }
        return null;

      case 'description':
        if (!value || value.trim().length === 0) {
          return 'Project description is required';
        }
        if (value.length > 500) {
          return 'Project description must be less than 500 characters';
        }
        return null;

      case 'type':
        if (!value) {
          return 'Project type is required';
        }
        return null;

      default:
        return null;
    }
  }
}

// Undo functionality helper
export const undoLastDelete = (): boolean => {
  const undoData = (window as any).__projectUndoData;
  if (undoData && undoData.undo) {
    undoData.undo();
    delete (window as any).__projectUndoData;
    return true;
  }
  return false;
};