// Custom hook for comprehensive filtering system

import { useState, useEffect, useMemo, useCallback } from 'react';
import { Project } from '../types/project';
import FilterService, { 
  FilterCriteria, 
  FilterPreset, 
  FilterStats, 
  SortCriteria 
} from '../services/filterService';

export interface UseFiltersOptions {
  enablePresets?: boolean;
  autoSave?: boolean;
}

export interface UseFiltersReturn {
  // Filter state
  criteria: FilterCriteria;
  setCriteria: (criteria: FilterCriteria) => void;
  sortCriteria: SortCriteria;
  setSortCriteria: (sort: SortCriteria) => void;
  
  // Filtered results
  filteredProjects: Project[];
  stats: FilterStats;
  
  // Filter actions
  resetFilters: () => void;
  isFilterEmpty: boolean;
  
  // Presets
  presets: FilterPreset[];
  savePreset: (name: string, description: string) => FilterPreset;
  loadPreset: (preset: FilterPreset) => void;
  deletePreset: (id: string) => boolean;
  
  // Utilities
  applyFilters: (projects: Project[]) => Project[];
  getFilterSummary: () => string;
}

export function useFilters(
  projects: Project[],
  options: UseFiltersOptions = {}
): UseFiltersReturn {
  const { enablePresets = true, autoSave = true } = options;
  
  // Filter service instance
  const filterService = useMemo(() => FilterService.getInstance(), []);
  
  // State
  const [criteria, setCriteria] = useState<FilterCriteria>(() => {
    if (autoSave) {
      const saved = localStorage.getItem('rork-filter-criteria');
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          return { ...filterService.createEmptyFilter(), ...parsed };
        } catch (error) {
          console.error('Failed to parse saved filter criteria:', error);
        }
      }
    }
    return filterService.createEmptyFilter();
  });
  
  const [sortCriteria, setSortCriteria] = useState<SortCriteria>(() => {
    if (autoSave) {
      const saved = localStorage.getItem('rork-sort-criteria');
      if (saved) {
        try {
          return JSON.parse(saved);
        } catch (error) {
          console.error('Failed to parse saved sort criteria:', error);
        }
      }
    }
    return { field: 'lastModified', direction: 'desc' };
  });
  
  const [presets, setPresets] = useState<FilterPreset[]>([]);
  
  // Load presets on mount
  useEffect(() => {
    if (enablePresets) {
      setPresets(filterService.getPresets());
    }
  }, [filterService, enablePresets]);
  
  // Auto-save criteria and sort
  useEffect(() => {
    if (autoSave) {
      try {
        localStorage.setItem('rork-filter-criteria', JSON.stringify(criteria));
      } catch (error) {
        console.error('Failed to save filter criteria:', error);
      }
    }
  }, [criteria, autoSave]);
  
  useEffect(() => {
    if (autoSave) {
      try {
        localStorage.setItem('rork-sort-criteria', JSON.stringify(sortCriteria));
      } catch (error) {
        console.error('Failed to save sort criteria:', error);
      }
    }
  }, [sortCriteria, autoSave]);
  
  // Apply filters and sorting
  const filteredProjects = useMemo(() => {
    let filtered = filterService.filterProjects(projects, criteria);
    filtered = filterService.sortProjects(filtered, sortCriteria);
    return filtered;
  }, [projects, criteria, sortCriteria, filterService]);
  
  // Calculate statistics
  const stats = useMemo(() => {
    return filterService.getFilterStats(projects, filteredProjects);
  }, [projects, filteredProjects, filterService]);
  
  // Check if filter is empty
  const isFilterEmpty = useMemo(() => {
    return filterService.isFilterEmpty(criteria);
  }, [criteria, filterService]);
  
  // Reset filters
  const resetFilters = useCallback(() => {
    setCriteria(filterService.createEmptyFilter());
    setSortCriteria({ field: 'lastModified', direction: 'desc' });
  }, [filterService]);
  
  // Save preset
  const savePreset = useCallback((name: string, description: string): FilterPreset => {
    const preset = filterService.savePreset(name, description, criteria);
    setPresets(filterService.getPresets());
    return preset;
  }, [criteria, filterService]);
  
  // Load preset
  const loadPreset = useCallback((preset: FilterPreset) => {
    setCriteria({ ...preset.criteria });
    filterService.usePreset(preset.id);
    setPresets(filterService.getPresets());
  }, [filterService]);
  
  // Delete preset
  const deletePreset = useCallback((id: string): boolean => {
    const success = filterService.deletePreset(id);
    if (success) {
      setPresets(filterService.getPresets());
    }
    return success;
  }, [filterService]);
  
  // Apply filters function (for external use)
  const applyFilters = useCallback((projectsToFilter: Project[]): Project[] => {
    let filtered = filterService.filterProjects(projectsToFilter, criteria);
    filtered = filterService.sortProjects(filtered, sortCriteria);
    return filtered;
  }, [criteria, sortCriteria, filterService]);
  
  // Get filter summary
  const getFilterSummary = useCallback((): string => {
    const parts: string[] = [];
    
    if (criteria.status.length > 0) {
      parts.push(`Status: ${criteria.status.join(', ')}`);
    }
    
    if (criteria.type.length > 0) {
      parts.push(`Type: ${criteria.type.join(', ')}`);
    }
    
    if (criteria.priority.length > 0) {
      parts.push(`Priority: ${criteria.priority.join(', ')}`);
    }
    
    if (criteria.complexity.length > 0) {
      parts.push(`Complexity: ${criteria.complexity.join(', ')}`);
    }
    
    if (criteria.tags.length > 0) {
      parts.push(`Tags: ${criteria.tags.join(', ')}`);
    }
    
    if (criteria.dateRange.start || criteria.dateRange.end) {
      const start = criteria.dateRange.start?.toLocaleDateString() || 'any';
      const end = criteria.dateRange.end?.toLocaleDateString() || 'any';
      parts.push(`Date: ${start} - ${end}`);
    }
    
    if (criteria.progressRange.min > 0 || criteria.progressRange.max < 100) {
      parts.push(`Progress: ${criteria.progressRange.min}% - ${criteria.progressRange.max}%`);
    }
    
    if (criteria.isStarred) {
      parts.push('Starred only');
    }
    
    if (criteria.hasRoute) {
      parts.push('Has route');
    }
    
    if (parts.length === 0) {
      return 'No filters applied';
    }
    
    return parts.join(' • ');
  }, [criteria]);
  
  return {
    // Filter state
    criteria,
    setCriteria,
    sortCriteria,
    setSortCriteria,
    
    // Filtered results
    filteredProjects,
    stats,
    
    // Filter actions
    resetFilters,
    isFilterEmpty,
    
    // Presets
    presets,
    savePreset,
    loadPreset,
    deletePreset,
    
    // Utilities
    applyFilters,
    getFilterSummary
  };
}