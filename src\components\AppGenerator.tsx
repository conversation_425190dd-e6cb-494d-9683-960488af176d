import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Link, Image, Github, ArrowRight, Code, Globe, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import { useApp } from '../context/AppContext';
import { useToast } from './ui/ToastContainer';
import { generateId } from '../utils/helpers';
import AnimatedBackground from './AnimatedBackground';
import Button from './ui/Button';
import Badge from './ui/Badge';
import FigmaLogo from './ui/FigmaLogo';
import ImportExportModal from './ImportExportModal';
import { FeatureErrorBoundary } from './error/FeatureErrorBoundary';
// Simplified - removed complex generation service imports
import { ProjectType, Complexity } from '../types/project';

interface GenerationFormData {
  description: string;
  type: ProjectType;
  complexity: Complexity;
  features: string[];
  targetPlatform: string[];
}

interface GenerationError {
  message: string;
  details?: string;
  recoverable: boolean;
}

const AppGenerator: React.FC = () => {
  // Form state
  const [formData, setFormData] = useState<GenerationFormData>({
    description: '',
    type: 'web',
    complexity: 'medium',
    features: [],
    targetPlatform: ['web']
  });

  // Generation state
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState<{ stage: string; progress: number } | null>(null);
  const [generatedApp, setGeneratedApp] = useState<GeneratedApp | null>(null);
  const [generationError, setGenerationError] = useState<GenerationError | null>(null);

  // UI state
  const [showImportExportModal, setShowImportExportModal] = useState(false);
  const [importExportMode, setImportExportMode] = useState<'import' | 'export'>('export');

  const { projects, addProject, setLoading } = useApp();
  const { showSuccess, showError } = useToast();
  const navigate = useNavigate();

  // Get recent projects from context
  const recentProjects = projects.slice(0, 4);

  const updateFormData = (updates: Partial<GenerationFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const generateApp = async () => {
    if (!formData.description.trim()) {
      showError('Please provide a description for your app');
      return;
    }

    setIsGenerating(true);
    setGenerationError(null);
    setLoading(true);

    try {
      // Generate a simple app name from the description
      const words = formData.description.split(' ').filter(word => word.length > 3);
      const appName = words.length > 0 
        ? `${words[0].charAt(0).toUpperCase() + words[0].slice(1)}App` 
        : 'MyApp';

      // Generate project ID
      const projectId = generateId();

      // Create project data
      const projectData = {
        id: projectId,
        name: appName,
        description: formData.description,
        type: formData.type,
        complexity: formData.complexity,
        status: 'active' as const,
        tags: formData.features,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Navigate with state containing project data
      navigate(`/chat/${projectId}`, { 
        state: { projectData }
      });
      
      showSuccess(`Starting to build ${appName}!`);

    } catch (error) {
      console.error('Navigation failed:', error);
      showError('Failed to start app generation. Please try again.');
    } finally {
      setIsGenerating(false);
      setLoading(false);
    }
  };

  const retryGeneration = () => {
    setGenerationError(null);
    generateApp();
  };

  const openImportModal = () => {
    setImportExportMode('import');
    setShowImportExportModal(true);
  };

  const openExportModal = () => {
    setImportExportMode('export');
    setShowImportExportModal(true);
  };

  const quickSuggestions = [
    "Make a visual novel game",
    "Make an Airbnb-style app", 
    "Make an Instagram-style app",
    "Make a meditation timer",
    "Create a habit tracker",
    "Build a calorie tracker",
    "Create a todo list",
    "Design a weather dashboard",
    "Design a fitness tracker"
  ];

  return (
    <FeatureErrorBoundary featureName="App Generator">
      <div className="min-h-screen relative">
        <AnimatedBackground />
        
        <div className="relative z-10 safe-area-inset">
          {/* Header Section */}
          <div className="text-center pt-16 pb-12">
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6 tracking-tight">
              Build native mobile apps, fast.
            </h1>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed px-6">
              Rork builds complete, cross-platform mobile apps using AI and React Native.
            </p>
          </div>

          {/* Main Input Section */}
          <div className="max-w-4xl mx-auto px-6 mb-16">
            <div className="bg-gray-900/40 backdrop-blur-2xl border border-gray-700/50 rounded-3xl p-8 shadow-2xl">
              {/* Description Input */}
              <div className="mb-6">
                <textarea
                  value={formData.description}
                  onChange={(e) => updateFormData({ description: e.target.value })}
                  placeholder="Describe the app you want to build..."
                  className="w-full h-32 bg-transparent border-none outline-none text-white placeholder-gray-400 text-lg resize-none selection:bg-blue-500/30"
                  disabled={isGenerating}
                />
              </div>



              {/* Action Bar */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <button 
                    className="group p-2 text-gray-500 hover:text-gray-300 hover:bg-gray-800/40 rounded-lg transition-all duration-200"
                    disabled={isGenerating}
                  >
                    <Link className="w-4 h-4 transition-transform group-hover:scale-110" />
                  </button>
                  <button 
                    className="group p-2 text-gray-500 hover:text-gray-300 hover:bg-gray-800/40 rounded-lg transition-all duration-200"
                    disabled={isGenerating}
                  >
                    <Image className="w-4 h-4 transition-transform group-hover:scale-110" />
                  </button>
                </div>
                
                <button
                  onClick={generateApp}
                  disabled={!formData.description.trim() || isGenerating}
                  className="p-2.5 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg text-white transition-colors duration-200"
                >
                  {isGenerating ? (
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  ) : (
                    <svg 
                      className="w-4 h-4" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path 
                        d="M2 12L22 2L13 21L11 13L2 12Z" 
                        fill="currentColor"
                      />
                    </svg>
                  )}
                </button>
              </div>
            </div>

            {/* Generation Progress */}
            {isGenerating && generationProgress && (
              <div className="mt-6 max-w-md mx-auto">
                <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
                  <div className="text-center mb-4">
                    <div className="text-white font-medium mb-2">{generationProgress.stage}</div>
                    <div className="w-full bg-gray-700/50 rounded-full h-2 overflow-hidden">
                      <div 
                        className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-500"
                        style={{ width: `${generationProgress.progress}%` }}
                      />
                    </div>
                    <div className="text-gray-400 text-sm mt-2">{generationProgress.progress}%</div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Quick Suggestions */}
          {!generatedApp && (
            <div className="max-w-6xl mx-auto px-6 mb-16">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {quickSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => updateFormData({ description: suggestion })}
                    className="p-4 bg-gray-900/30 backdrop-blur-xl border border-gray-700/30 rounded-2xl text-left text-gray-300 hover:bg-gray-800/40 hover:border-gray-600/50 hover:text-white transition-all duration-200 group"
                    disabled={isGenerating}
                  >
                    <span className="block text-sm">{suggestion}</span>
                    <ArrowRight className="w-4 h-4 mt-2 text-gray-500 group-hover:text-blue-400 group-hover:translate-x-1 transition-all duration-200" />
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Import Section */}
          {!generatedApp && (
            <div className="text-center mb-16 px-6">
              <p className="text-gray-500 text-sm mb-6">or import from</p>
              <div className="flex items-center justify-center gap-4 flex-wrap">
                <button className="flex items-center gap-3 px-6 py-3 bg-gray-900/30 backdrop-blur-xl border border-gray-700/30 rounded-2xl hover:bg-gray-800/40 hover:border-gray-600/50 transition-all duration-200">
                  <FigmaLogo size={20} />
                  <span className="text-gray-300 text-sm">Figma</span>
                </button>
                <button className="flex items-center gap-3 px-6 py-3 bg-gray-900/30 backdrop-blur-xl border border-gray-700/30 rounded-2xl hover:bg-gray-800/40 hover:border-gray-600/50 transition-all duration-200">
                  <Github className="w-5 h-5 text-gray-300" />
                  <span className="text-gray-300 text-sm">GitHub</span>
                </button>
              </div>
            </div>
          )}

          {/* Your Projects Section */}
          {recentProjects.length > 0 && !generatedApp && (
            <div className="max-w-6xl mx-auto px-6 pb-16">
              <h2 className="text-2xl font-bold text-white mb-8">Your Projects</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {recentProjects.map((project) => (
                  <div
                    key={project.id}
                    className="bg-gray-900/40 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 hover:bg-gray-800/40 hover:border-gray-600/50 transition-all duration-200 cursor-pointer group"
                    onClick={() => navigate(`/project/${project.id}`)}
                  >
                    <div className="aspect-video bg-gray-800/60 rounded-lg mb-4 flex items-center justify-center">
                      <Code className="w-8 h-8 text-gray-500" />
                    </div>
                    <h3 className="text-white font-medium mb-2 group-hover:text-blue-400 transition-colors">{project.name}</h3>
                    <p className="text-gray-400 text-sm line-clamp-2">{project.description}</p>
                    <div className="flex items-center gap-2 mt-4">
                      <Badge variant="secondary" size="sm">{project.type}</Badge>
                      <Badge variant="outline" size="sm">{project.status}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Generated App Display */}
          {generatedApp && (
            <div className="max-w-4xl mx-auto px-6 pb-16">
              <div className="bg-gray-900/60 backdrop-blur-2xl border border-gray-700/50 rounded-3xl p-8 shadow-2xl">
                <div className="text-center mb-8">
                  <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
                  <h2 className="text-3xl font-bold text-white mb-2">App Generated Successfully!</h2>
                  <p className="text-gray-400">Your app <strong>{generatedApp.name}</strong> is ready</p>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-4">Generated Features</h3>
                    <ul className="space-y-2 text-gray-300">
                      {generatedApp.features?.map((feature, index) => (
                        <li key={index} className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-4">Tech Stack</h3>
                    <div className="flex flex-wrap gap-2">
                      {generatedApp.techStack?.map((tech, index) => (
                        <Badge key={index} variant="secondary">{tech}</Badge>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-center gap-4 mt-8">
                  <Button 
                    onClick={() => window.open(generatedApp.previewUrl, '_blank')}
                    leftIcon={<Globe className="w-4 h-4" />}
                  >
                    Preview App
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => navigate(`/project/${generatedApp.id}`)}
                    leftIcon={<Code className="w-4 h-4" />}
                  >
                    View Code
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {generationError && (
            <div className="max-w-2xl mx-auto px-6 mb-16">
              <div className="bg-red-900/20 backdrop-blur-xl border border-red-500/30 rounded-2xl p-6">
                <div className="flex items-start gap-3">
                  <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <h3 className="text-red-300 font-medium mb-2">{generationError.message}</h3>
                    {generationError.details && (
                      <p className="text-red-400/80 text-sm mb-4">{generationError.details}</p>
                    )}
                    {generationError.recoverable && (
                      <Button
                        onClick={retryGeneration}
                        variant="outline"
                        size="sm"
                        leftIcon={<RefreshCw className="w-4 h-4" />}
                      >
                        Retry Generation
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Import/Export Modal */}
        {showImportExportModal && (
          <ImportExportModal
            mode={importExportMode}
            isOpen={showImportExportModal}
            onClose={() => setShowImportExportModal(false)}
            onImport={(data) => {
              // Handle import logic
              setShowImportExportModal(false);
            }}
            onExport={() => {
              // Handle export logic
              setShowImportExportModal(false);
            }}
          />
        )}
      </div>
    </FeatureErrorBoundary>
  );
};

export default AppGenerator;
