import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';

// Mock all the complex providers for testing
vi.mock('../context/AppContext', () => ({
  AppProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useApp: () => ({
    projects: [],
    currentProject: null,
    addProject: vi.fn(),
    updateProject: vi.fn(),
    deleteProject: vi.fn(),
  }),
}));

vi.mock('../context/PerformanceContext', () => ({
  PerformanceProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  usePerformance: () => ({
    measureRender: vi.fn(),
    measureAsync: vi.fn(),
    recordMetric: vi.fn(),
  }),
}));

vi.mock('../components/ui/ToastContainer', () => ({
  ToastProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useToast: () => ({
    showToast: vi.fn(),
    showSuccess: vi.fn(),
    showError: vi.fn(),
  }),
}));

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <BrowserRouter>
      {children}
    </BrowserRouter>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Mock project data for testing
export const mockProject = {
  id: 'test-project-1',
  name: 'Test Project',
  description: 'A test project for unit testing',
  type: 'web' as const,
  status: 'active' as const,
  complexity: 'medium' as const,
  priority: 'high' as const,
  progress: 75,
  createdAt: '2024-01-01T00:00:00.000Z',
  lastModified: '2024-01-02T00:00:00.000Z',
  tags: ['test', 'react'],
  isStarred: true,
};

export const mockProjects = [
  mockProject,
  {
    ...mockProject,
    id: 'test-project-2',
    name: 'Another Test Project',
    status: 'completed' as const,
    progress: 100,
    isStarred: false,
  },
];

// Mock user data
export const mockUser = {
  id: 'test-user-1',
  name: 'Test User',
  email: '<EMAIL>',
  plan: 'pro' as const,
};

// Mock app context value
export const mockAppContextValue = {
  user: mockUser,
  projects: mockProjects,
  currentProject: mockProject,
  projectsLoading: false,
  isOnline: true,
  searchQuery: '',
  searchResults: [],
  searchLoading: false,
  activeFilters: {
    status: [],
    type: [],
    priority: [],
    complexity: [],
    tags: [],
  },
  notifications: [],
  unreadCount: 0,
  sidebarCollapsed: false,
  currentView: 'grid' as const,
  sortBy: 'lastModified' as const,
  sortOrder: 'desc' as const,
  addProject: vi.fn(),
  updateProject: vi.fn(),
  deleteProject: vi.fn(),
  setCurrentProject: vi.fn(),
  setSearchQuery: vi.fn(),
  setActiveFilters: vi.fn(),
  clearNotifications: vi.fn(),
  toggleSidebar: vi.fn(),
  setCurrentView: vi.fn(),
  setSortBy: vi.fn(),
  setSortOrder: vi.fn(),
};

// Test utilities
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};

export const createMockEvent = (type: string, properties = {}) => {
  const event = new Event(type, { bubbles: true, cancelable: true });
  Object.assign(event, properties);
  return event;
};

// Re-export everything from testing-library
export * from '@testing-library/react';
export { customRender as render };
export { vi } from 'vitest';
