import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Save, 
  X, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  Smartphone,
  Globe,
  FileText,
  Zap,
  ShoppingCart
} from 'lucide-react';
import { Project, ProjectType, Priority, Complexity } from '../types/project';
import { CreateProjectData, ProjectCRUDService } from '../services/projectCRUD';
import { useApp } from '../context/AppContext';
import { useDebounce } from '../hooks/useDebounce';
import Button from './ui/Button';
import Input from './ui/Input';
import Modal from './ui/Modal';
import { cn } from '../utils/helpers';

interface ProjectFormProps {
  isOpen: boolean;
  onClose: () => void;
  project?: Project | null;
  mode: 'create' | 'edit';
}

const projectTypes: { value: ProjectType; label: string; icon: React.ReactNode; color: string }[] = [
  { value: 'mobile', label: 'Mobile App', icon: <Smartphone className="w-4 h-4" />, color: 'bg-green-100 text-green-700 border-green-200' },
  { value: 'web', label: 'Web App', icon: <Globe className="w-4 h-4" />, color: 'bg-blue-100 text-blue-700 border-blue-200' },
  { value: 'form', label: 'Form', icon: <FileText className="w-4 h-4" />, color: 'bg-purple-100 text-purple-700 border-purple-200' },
  { value: 'ai', label: 'AI App', icon: <Zap className="w-4 h-4" />, color: 'bg-orange-100 text-orange-700 border-orange-200' },
  { value: 'ecommerce', label: 'E-commerce', icon: <ShoppingCart className="w-4 h-4" />, color: 'bg-pink-100 text-pink-700 border-pink-200' }
];

const priorities: { value: Priority; label: string; color: string }[] = [
  { value: 'high', label: 'High', color: 'text-red-600' },
  { value: 'medium', label: 'Medium', color: 'text-yellow-600' },
  { value: 'low', label: 'Low', color: 'text-green-600' }
];

const complexities: { value: Complexity; label: string }[] = [
  { value: 'simple', label: 'Simple' },
  { value: 'medium', label: 'Medium' },
  { value: 'complex', label: 'Complex' }
];

const ProjectForm: React.FC<ProjectFormProps> = ({
  isOpen,
  onClose,
  project,
  mode
}) => {
  const { addProject, updateProject, addOptimisticUpdate, removeOptimisticUpdate } = useApp();
  
  // Form state
  const [formData, setFormData] = useState<CreateProjectData>({
    name: '',
    description: '',
    type: 'web',
    priority: 'medium',
    complexity: 'medium',
    tags: []
  });

  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [tagInput, setTagInput] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);

  // Debounced validation
  const debouncedFormData = useDebounce(formData, 300);

  // Initialize form data when project changes
  useEffect(() => {
    if (project && mode === 'edit') {
      setFormData({
        name: project.name,
        description: project.description,
        type: project.type,
        priority: project.priority,
        complexity: project.complexity,
        tags: project.tags || []
      });
    } else {
      setFormData({
        name: '',
        description: '',
        type: 'web',
        priority: 'medium',
        complexity: 'medium',
        tags: []
      });
    }
    setErrors({});
    setFieldErrors({});
    setShowSuccess(false);
  }, [project, mode, isOpen]);

  // Real-time validation
  useEffect(() => {
    const newFieldErrors: Record<string, string> = {};
    
    Object.keys(debouncedFormData).forEach(key => {
      const error = ProjectCRUDService.validateField(
        key as keyof CreateProjectData, 
        debouncedFormData[key as keyof CreateProjectData]
      );
      if (error) {
        newFieldErrors[key] = error;
      }
    });

    setFieldErrors(newFieldErrors);
  }, [debouncedFormData]);

  // Handle input changes
  const handleInputChange = (field: keyof CreateProjectData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Handle tag management
  const addTag = () => {
    if (tagInput.trim() && !formData.tags?.includes(tagInput.trim())) {
      handleInputChange('tags', [...(formData.tags || []), tagInput.trim()]);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    handleInputChange('tags', formData.tags?.filter(tag => tag !== tagToRemove) || []);
  };

  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const validationErrors = ProjectCRUDService.validateProjectData(formData);
    if (validationErrors.length > 0) {
      setErrors({ general: validationErrors.join(', ') });
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      if (mode === 'create') {
        await ProjectCRUDService.createProject(
          formData,
          // Optimistic update
          (optimisticProject) => {
            addOptimisticUpdate(optimisticProject.id, optimisticProject);
            addProject(optimisticProject);
          },
          // Success
          (createdProject) => {
            removeOptimisticUpdate(createdProject.id);
            setShowSuccess(true);
            setTimeout(() => {
              onClose();
            }, 1500);
          },
          // Error
          (error) => {
            setErrors({ general: error });
            // Remove optimistic update on error
            // This would need the optimistic project ID
          }
        );
      } else if (project) {
        await ProjectCRUDService.updateProject(
          { ...formData, id: project.id },
          // Optimistic update
          (id, updates) => {
            addOptimisticUpdate(id, updates);
            updateProject(id, updates);
          },
          // Success
          (updatedProject) => {
            removeOptimisticUpdate(project.id);
            setShowSuccess(true);
            setTimeout(() => {
              onClose();
            }, 1500);
          },
          // Error
          (error) => {
            setErrors({ general: error });
          }
        );
      }
    } catch (error) {
      setErrors({ general: 'An unexpected error occurred' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid = Object.keys(fieldErrors).length === 0 && 
                     formData.name.trim() && 
                     formData.description.trim();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Create New Project' : 'Edit Project'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Success Message */}
        <AnimatePresence>
          {showSuccess && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700"
            >
              <CheckCircle className="w-5 h-5" />
              <span>Project {mode === 'create' ? 'created' : 'updated'} successfully!</span>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Error Message */}
        <AnimatePresence>
          {errors.general && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700"
            >
              <AlertCircle className="w-5 h-5" />
              <span>{errors.general}</span>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Project Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Project Name *
          </label>
          <Input
            id="name"
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter project name"
            error={fieldErrors.name}
            disabled={isSubmitting}
          />
        </div>

        {/* Project Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Describe your project"
            rows={3}
            disabled={isSubmitting}
            className={cn(
              'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              fieldErrors.description && 'border-red-300 focus:ring-red-500'
            )}
          />
          {fieldErrors.description && (
            <p className="mt-1 text-sm text-red-600">{fieldErrors.description}</p>
          )}
        </div>

        {/* Project Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Project Type *
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {projectTypes.map((type) => (
              <motion.button
                key={type.value}
                type="button"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => handleInputChange('type', type.value)}
                disabled={isSubmitting}
                className={cn(
                  'flex items-center gap-2 p-3 border-2 rounded-lg transition-all duration-200',
                  'disabled:opacity-50 disabled:cursor-not-allowed',
                  formData.type === type.value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                )}
              >
                {type.icon}
                <span className="text-sm font-medium">{type.label}</span>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Priority and Complexity */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Priority */}
          <div>
            <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-2">
              Priority
            </label>
            <select
              id="priority"
              value={formData.priority}
              onChange={(e) => handleInputChange('priority', e.target.value as Priority)}
              disabled={isSubmitting}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {priorities.map((priority) => (
                <option key={priority.value} value={priority.value}>
                  {priority.label}
                </option>
              ))}
            </select>
          </div>

          {/* Complexity */}
          <div>
            <label htmlFor="complexity" className="block text-sm font-medium text-gray-700 mb-2">
              Complexity
            </label>
            <select
              id="complexity"
              value={formData.complexity}
              onChange={(e) => handleInputChange('complexity', e.target.value as Complexity)}
              disabled={isSubmitting}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {complexities.map((complexity) => (
                <option key={complexity.value} value={complexity.value}>
                  {complexity.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Tags */}
        <div>
          <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
            Tags
          </label>
          <div className="space-y-2">
            <div className="flex gap-2">
              <Input
                id="tags"
                type="text"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleTagKeyPress}
                placeholder="Add a tag"
                disabled={isSubmitting}
              />
              <Button
                type="button"
                variant="outline"
                onClick={addTag}
                disabled={!tagInput.trim() || isSubmitting}
              >
                Add
              </Button>
            </div>
            
            {formData.tags && formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <motion.span
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      disabled={isSubmitting}
                      className="hover:text-blue-600 disabled:opacity-50"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </motion.span>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={!isFormValid || isSubmitting}
            leftIcon={isSubmitting ? <Loader2 className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />}
          >
            {isSubmitting 
              ? (mode === 'create' ? 'Creating...' : 'Updating...') 
              : (mode === 'create' ? 'Create Project' : 'Update Project')
            }
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default ProjectForm;