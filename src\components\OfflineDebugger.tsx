/**
 * Offline Debugger Component
 * Helps debug and manage offline functionality
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Wifi, WifiOff, RefreshCw, Settings, X } from 'lucide-react';
import { useOffline, useServiceWorker } from '../hooks/useOffline';

const OfflineDebugger: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [swRegistrations, setSwRegistrations] = useState<ServiceWorkerRegistration[]>([]);
  const { isOnline, offlineActions, getPendingActions } = useOffline();
  const { isRegistered, registerServiceWorker, unregisterServiceWorker } = useServiceWorker();

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then(setSwRegistrations);
    }
  }, [isRegistered]);

  const handleUnregisterAll = async () => {
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();
      for (const registration of registrations) {
        await registration.unregister();
      }
      setSwRegistrations([]);
      window.location.reload();
    }
  };

  const handleClearCache = async () => {
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
      console.log('All caches cleared');
    }
  };

  const handleClearIndexedDB = async () => {
    if ('indexedDB' in window) {
      try {
        const deleteDB = indexedDB.deleteDatabase('AppGeneratorDB');
        deleteDB.onsuccess = () => console.log('IndexedDB cleared');
        deleteDB.onerror = () => console.error('Failed to clear IndexedDB');
      } catch (error) {
        console.error('Error clearing IndexedDB:', error);
      }
    }
  };

  const handleForceOnline = () => {
    // Temporarily override navigator.onLine for testing
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true
    });
    window.dispatchEvent(new Event('online'));
  };

  const handleForceOffline = () => {
    // Temporarily override navigator.onLine for testing
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: false
    });
    window.dispatchEvent(new Event('offline'));
  };

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      {/* Debug Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-4 right-4 z-50 bg-gray-800 text-white p-3 rounded-full shadow-lg hover:bg-gray-700 transition-colors"
        title="Offline Debugger"
      >
        <Settings size={20} />
      </button>

      {/* Debug Panel */}
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="fixed bottom-20 right-4 z-50 bg-white border border-gray-200 rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Offline Debugger</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={20} />
            </button>
          </div>

          {/* Status */}
          <div className="space-y-2 mb-4">
            <div className="flex items-center gap-2">
              {isOnline ? <Wifi size={16} className="text-green-500" /> : <WifiOff size={16} className="text-red-500" />}
              <span className="text-sm">
                Status: {isOnline ? 'Online' : 'Offline'}
              </span>
            </div>
            <div className="text-sm text-gray-600">
              Service Worker: {isRegistered ? 'Registered' : 'Not Registered'}
            </div>
            <div className="text-sm text-gray-600">
              Pending Actions: {offlineActions.length}
            </div>
            <div className="text-sm text-gray-600">
              SW Registrations: {swRegistrations.length}
            </div>
          </div>

          {/* Controls */}
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={handleForceOnline}
                className="px-3 py-2 bg-green-500 text-white rounded text-xs hover:bg-green-600"
              >
                Force Online
              </button>
              <button
                onClick={handleForceOffline}
                className="px-3 py-2 bg-red-500 text-white rounded text-xs hover:bg-red-600"
              >
                Force Offline
              </button>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={registerServiceWorker}
                className="px-3 py-2 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
              >
                Register SW
              </button>
              <button
                onClick={unregisterServiceWorker}
                className="px-3 py-2 bg-orange-500 text-white rounded text-xs hover:bg-orange-600"
              >
                Unregister SW
              </button>
            </div>

            <button
              onClick={handleUnregisterAll}
              className="w-full px-3 py-2 bg-red-600 text-white rounded text-xs hover:bg-red-700"
            >
              Unregister All SWs
            </button>

            <button
              onClick={handleClearCache}
              className="w-full px-3 py-2 bg-purple-500 text-white rounded text-xs hover:bg-purple-600"
            >
              Clear All Caches
            </button>

            <button
              onClick={handleClearIndexedDB}
              className="w-full px-3 py-2 bg-indigo-500 text-white rounded text-xs hover:bg-indigo-600"
            >
              Clear IndexedDB
            </button>

            <button
              onClick={() => window.location.reload()}
              className="w-full px-3 py-2 bg-gray-500 text-white rounded text-xs hover:bg-gray-600 flex items-center justify-center gap-1"
            >
              <RefreshCw size={12} />
              Reload Page
            </button>
          </div>

          {/* Service Worker Registrations */}
          {swRegistrations.length > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <h4 className="text-sm font-medium mb-2">Active Service Workers:</h4>
              <div className="space-y-1">
                {swRegistrations.map((reg, index) => (
                  <div key={index} className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                    <div>Scope: {reg.scope}</div>
                    <div>State: {reg.active?.state || 'Unknown'}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Quick Fix */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium mb-2 text-red-600">Quick Fix for Offline Issue:</h4>
            <button
              onClick={() => {
                handleUnregisterAll();
                handleClearCache();
                handleClearIndexedDB();
              }}
              className="w-full px-3 py-2 bg-red-600 text-white rounded text-xs hover:bg-red-700 font-medium"
            >
              🚨 Reset Everything & Reload
            </button>
          </div>
        </motion.div>
      )}
    </>
  );
};

export default OfflineDebugger;