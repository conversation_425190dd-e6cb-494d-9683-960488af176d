// Script to unregister service workers that might be causing offline issues
(function() {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
      for(let registration of registrations) {
        registration.unregister().then(function(boolean) {
          console.log('Service Worker unregistered:', boolean);
        });
      }
      // Reload the page after unregistering
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    });
  }
})();