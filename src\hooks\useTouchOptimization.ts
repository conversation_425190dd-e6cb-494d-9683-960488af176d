import { useEffect, useRef, useCallback } from 'react';

interface TouchOptimizationOptions {
  passive?: boolean;
  capture?: boolean;
  preventDefault?: boolean;
}

export function useTouchOptimization() {
  // Add passive event listeners for better scroll performance
  useEffect(() => {
    const addPassiveEventListener = (
      element: Element | Window,
      event: string,
      handler: EventListener,
      options: AddEventListenerOptions = {}
    ) => {
      element.addEventListener(event, handler, {
        passive: true,
        ...options
      });
      
      return () => element.removeEventListener(event, handler);
    };

    // Optimize common touch events
    const cleanupFunctions: (() => void)[] = [];

    // Passive touch events for better scroll performance
    cleanupFunctions.push(
      addPassiveEventListener(window, 'touchstart', () => {}, { passive: true }),
      addPassiveEventListener(window, 'touchmove', () => {}, { passive: true }),
      addPassiveEventListener(window, 'touchend', () => {}, { passive: true })
    );

    // Optimize wheel events
    cleanupFunctions.push(
      addPassiveEventListener(window, 'wheel', () => {}, { passive: true })
    );

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, []);

  // Optimized touch handler factory
  const createTouchHandler = useCallback(
    <T extends HTMLElement>(
      onTouch: (event: TouchEvent, element: T) => void,
      options: TouchOptimizationOptions = {}
    ) => {
      return (element: T | null) => {
        if (!element) return;

        const handler = (event: TouchEvent) => {
          if (options.preventDefault) {
            event.preventDefault();
          }
          onTouch(event, element);
        };

        element.addEventListener('touchstart', handler, {
          passive: !options.preventDefault,
          capture: options.capture
        });

        return () => {
          element.removeEventListener('touchstart', handler);
        };
      };
    },
    []
  );

  // Debounced touch handler for performance
  const createDebouncedTouchHandler = useCallback(
    <T extends HTMLElement>(
      onTouch: (event: TouchEvent, element: T) => void,
      delay = 16, // ~60fps
      options: TouchOptimizationOptions = {}
    ) => {
      let timeoutId: NodeJS.Timeout;
      
      return (element: T | null) => {
        if (!element) return;

        const handler = (event: TouchEvent) => {
          if (options.preventDefault) {
            event.preventDefault();
          }
          
          clearTimeout(timeoutId);
          timeoutId = setTimeout(() => {
            onTouch(event, element);
          }, delay);
        };

        element.addEventListener('touchmove', handler, {
          passive: !options.preventDefault,
          capture: options.capture
        });

        return () => {
          element.removeEventListener('touchmove', handler);
          clearTimeout(timeoutId);
        };
      };
    },
    []
  );

  return {
    createTouchHandler,
    createDebouncedTouchHandler
  };
}

// Hook for optimized scroll handling
export function useOptimizedScroll(
  onScroll: (event: Event) => void,
  dependencies: any[] = []
) {
  const rafId = useRef<number>();
  const isScrolling = useRef(false);

  const handleScroll = useCallback((event: Event) => {
    if (!isScrolling.current) {
      isScrolling.current = true;
      
      rafId.current = requestAnimationFrame(() => {
        onScroll(event);
        isScrolling.current = false;
      });
    }
  }, dependencies);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, [handleScroll]);

  return handleScroll;
}

// Hook for touch feedback optimization
export function useOptimizedTouchFeedback() {
  const feedbackTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const addTouchFeedback = useCallback((
    element: HTMLElement,
    feedbackClass = 'touch-feedback',
    duration = 150
  ) => {
    const key = element.id || Math.random().toString();
    
    // Clear existing timeout
    const existingTimeout = feedbackTimeouts.current.get(key);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Add feedback class
    element.classList.add(feedbackClass);

    // Remove feedback class after duration
    const timeout = setTimeout(() => {
      element.classList.remove(feedbackClass);
      feedbackTimeouts.current.delete(key);
    }, duration);

    feedbackTimeouts.current.set(key, timeout);
  }, []);

  useEffect(() => {
    return () => {
      // Cleanup all timeouts
      feedbackTimeouts.current.forEach(timeout => clearTimeout(timeout));
      feedbackTimeouts.current.clear();
    };
  }, []);

  return { addTouchFeedback };
}