// Project-related type definitions

export type ProjectType = 'mobile' | 'web' | 'form' | 'ai' | 'ecommerce';
export type ProjectStatus = 'active' | 'completed' | 'draft' | 'archived' | 'error';
export type Priority = 'high' | 'medium' | 'low';
export type Complexity = 'simple' | 'medium' | 'complex';

export interface Feature {
  id: string;
  name: string;
  description: string;
  complexity: Complexity;
  estimatedHours: number;
}

export interface TechStack {
  frontend: string[];
  backend: string[];
  database: string[];
  deployment: string[];
  tools: string[];
}

export interface MarketAnalysis {
  targetAudience: string;
  marketSize: string;
  competitors: string[];
  uniqueSellingPoints: string[];
  revenueModel: string;
  estimatedRevenue: string;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  type: ProjectType;
  status: ProjectStatus;
  progress: number;
  lastModified: string;
  createdAt: string;
  route?: string;
  priority: Priority;
  isStarred?: boolean;
  tags: string[];
  complexity: Complexity;
  features?: Feature[];
  techStack?: TechStack;
  marketAnalysis?: MarketAnalysis;
}