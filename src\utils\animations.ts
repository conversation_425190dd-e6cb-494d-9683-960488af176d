// Animation utilities and configurations for Framer Motion

import { Variants, Transition } from 'framer-motion';
import { AnimationConfig } from '../types';

// Standard animation variants
export const animationVariants: Record<string, Variants> = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  },
  slideUp: {
    initial: { y: 20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: -20, opacity: 0 }
  },
  slideDown: {
    initial: { y: -20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: 20, opacity: 0 }
  },
  slideLeft: {
    initial: { x: 20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: -20, opacity: 0 }
  },
  slideRight: {
    initial: { x: -20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: 20, opacity: 0 }
  },
  scale: {
    initial: { scale: 0.9, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.9, opacity: 0 }
  },
  scaleIn: {
    initial: { scale: 0, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0, opacity: 0 }
  },
  stagger: {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  },
  staggerFast: {
    animate: {
      transition: {
        staggerChildren: 0.05
      }
    }
  },
  staggerSlow: {
    animate: {
      transition: {
        staggerChildren: 0.2
      }
    }
  },
  hover: {
    initial: { scale: 1 },
    animate: { scale: 1.05 },
    exit: { scale: 1 }
  },
  tap: {
    initial: { scale: 1 },
    animate: { scale: 0.95 },
    exit: { scale: 1 }
  },
  bounce: {
    initial: { y: 0 },
    animate: { 
      y: [0, -10, 0],
      transition: {
        duration: 0.6,
        ease: "easeInOut",
        times: [0, 0.5, 1]
      }
    }
  },
  shake: {
    initial: { x: 0 },
    animate: { 
      x: [-5, 5, -5, 5, 0],
      transition: {
        duration: 0.5,
        ease: "easeInOut"
      }
    }
  },
  pulse: {
    initial: { scale: 1 },
    animate: { 
      scale: [1, 1.05, 1],
      transition: {
        duration: 1,
        ease: "easeInOut",
        repeat: Infinity
      }
    }
  }
};

// Standard animation configurations
export const animationConfig: Record<string, AnimationConfig> = {
  fast: {
    duration: 0.2,
    easing: 'easeOut'
  },
  normal: {
    duration: 0.3,
    easing: 'easeInOut'
  },
  slow: {
    duration: 0.5,
    easing: 'easeInOut'
  },
  bounce: {
    duration: 0.6,
    easing: 'easeOut'
  },
  spring: {
    duration: 0.4,
    easing: 'easeOut'
  }
};

// Transition presets
export const transitions: Record<string, Transition> = {
  spring: {
    type: 'spring',
    stiffness: 300,
    damping: 30
  },
  springBouncy: {
    type: 'spring',
    stiffness: 400,
    damping: 20
  },
  springGentle: {
    type: 'spring',
    stiffness: 200,
    damping: 40
  },
  smooth: {
    type: 'tween',
    ease: 'easeInOut',
    duration: 0.3
  },
  quick: {
    type: 'tween',
    ease: 'easeOut',
    duration: 0.2
  },
  slow: {
    type: 'tween',
    ease: 'easeInOut',
    duration: 0.5
  }
};

// Utility functions for creating custom animations
export const createFadeVariant = (duration = 0.3, delay = 0): Variants => ({
  initial: { opacity: 0 },
  animate: { 
    opacity: 1,
    transition: { duration, delay }
  },
  exit: { 
    opacity: 0,
    transition: { duration: duration * 0.5 }
  }
});

export const createSlideVariant = (
  direction: 'up' | 'down' | 'left' | 'right' = 'up',
  distance = 20,
  duration = 0.3,
  delay = 0
): Variants => {
  const axis = direction === 'left' || direction === 'right' ? 'x' : 'y';
  const value = direction === 'down' || direction === 'right' ? distance : -distance;
  
  return {
    initial: { [axis]: value, opacity: 0 },
    animate: { 
      [axis]: 0, 
      opacity: 1,
      transition: { duration, delay }
    },
    exit: { 
      [axis]: value, 
      opacity: 0,
      transition: { duration: duration * 0.5 }
    }
  };
};

export const createScaleVariant = (
  initialScale = 0.9,
  duration = 0.3,
  delay = 0
): Variants => ({
  initial: { scale: initialScale, opacity: 0 },
  animate: { 
    scale: 1, 
    opacity: 1,
    transition: { duration, delay }
  },
  exit: { 
    scale: initialScale, 
    opacity: 0,
    transition: { duration: duration * 0.5 }
  }
});

export const createStaggerVariant = (staggerDelay = 0.1): Variants => ({
  animate: {
    transition: {
      staggerChildren: staggerDelay
    }
  }
});

// Common animation combinations
export const pageTransition: Variants = {
  initial: { opacity: 0, y: 20 },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.4, ease: 'easeOut' }
  },
  exit: { 
    opacity: 0, 
    y: -20,
    transition: { duration: 0.3, ease: 'easeIn' }
  }
};

export const modalTransition: Variants = {
  initial: { opacity: 0, scale: 0.9 },
  animate: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  exit: { 
    opacity: 0, 
    scale: 0.9,
    transition: { duration: 0.2, ease: 'easeIn' }
  }
};

export const cardTransition: Variants = {
  initial: { opacity: 0, y: 10 },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  exit: { 
    opacity: 0, 
    y: -10,
    transition: { duration: 0.2, ease: 'easeIn' }
  }
};