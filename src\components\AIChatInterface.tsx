import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { 
  ArrowLeft, 
  Send, 
  Code2, 
  Smartphone, 
  Download, 
  RotateCcw,
  Maximize2,
  MessageSquare,
  Loader2
} from 'lucide-react';
import { useApp } from '../context/AppContext';

interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  isTyping?: boolean;
}

interface CodeProgress {
  stage: string;
  progress: number;
  isComplete: boolean;
  files: string[];
  currentFile?: string;
}

const AIChatInterface: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { projects, addProjectWithId } = useApp();
  const chatEndRef = useRef<HTMLDivElement>(null);
  
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      text: "Hi! I'm your AI assistant. I'll help you build your app step by step. Tell me more about what you'd like to create!",
      sender: 'ai',
      timestamp: new Date()
    }
  ]);
  
  const [inputMessage, setInputMessage] = useState('');
  const [isAITyping, setIsAITyping] = useState(false);
  const [showCodeProgress, setShowCodeProgress] = useState(true);
  const [showMobilePreview, setShowMobilePreview] = useState(true);
  
  const [codeProgress, setCodeProgress] = useState<CodeProgress>({
    stage: 'Setting up project structure...',
    progress: 25,
    isComplete: false,
    files: ['App.tsx', 'components/Header.tsx', 'components/PropertyCard.tsx'],
    currentFile: 'App.tsx'
  });

  let project = projectId ? projects.find(p => p.id === projectId) : undefined;

  // If project doesn't exist but we have project data in location state, create it
  useEffect(() => {
    if (!project && projectId && location.state?.projectData) {
      const projectData = location.state.projectData;
      
      // Create the project with the exact ID from the URL to match
      const projectWithCorrectId = {
        ...projectData,
        id: projectId!, // Use the ID from URL (we know it exists due to the condition)
        createdAt: new Date().toISOString(),
        lastModified: new Date().toISOString()
      };
      
      // Use the new action that accepts a complete project with ID
      addProjectWithId(projectWithCorrectId);
    }
  }, [project, projectId, location.state, addProjectWithId]);

  // Update project reference after potential creation
  project = projectId ? projects.find(p => p.id === projectId) : undefined;

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsAITyping(true);

    // Simulate AI thinking and response
    setTimeout(() => {
      const aiResponses = [
        "Great idea! I'm working on implementing that feature for you. Let me update the code structure...",
        "Perfect! I'm adding that to your app. You can see the progress in the code panel.",
        "Excellent choice! I'm building that component now. Check the mobile preview to see it in action.",
        "That's a fantastic feature! I'm coding it up right now. The mobile preview will update as I work.",
        "Nice! I'm implementing that functionality. You can track my progress in the code generation panel."
      ];
      
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: aiResponses[Math.floor(Math.random() * aiResponses.length)],
        sender: 'ai',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsAITyping(false);

      // Update progress
      setCodeProgress(prev => ({
        ...prev,
        progress: Math.min(prev.progress + 15, 100),
        stage: prev.progress > 80 ? 'Finalizing app...' : 'Building components...',
        files: [...prev.files, `components/Feature${Math.floor(Math.random() * 100)}.tsx`]
      }));
    }, 1500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-white text-xl mb-4">Project not found</h2>
          <button 
            onClick={() => navigate('/')}
            className="text-blue-400 hover:text-blue-300"
          >
            Go back to generator
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-900 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800 bg-gray-900/95 backdrop-blur-sm">
        <div className="flex items-center gap-4">
          <button 
            onClick={() => navigate('/')}
            className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back</span>
          </button>
          <div className="h-4 w-px bg-gray-700"></div>
          <div className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5 text-blue-400" />
            <h1 className="text-white font-semibold">{project?.name || 'Loading...'} - AI Assistant</h1>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button 
            onClick={() => setShowCodeProgress(!showCodeProgress)}
            className={`flex items-center gap-2 px-3 py-1.5 rounded-lg transition-colors ${
              showCodeProgress ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            <Code2 className="w-4 h-4" />
            <span className="text-sm">Code Progress</span>
          </button>
          <button 
            onClick={() => setShowMobilePreview(!showMobilePreview)}
            className={`flex items-center gap-2 px-3 py-1.5 rounded-lg transition-colors ${
              showMobilePreview ? 'bg-green-600 text-white' : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            <Smartphone className="w-4 h-4" />
            <span className="text-sm">Preview</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Chat Panel */}
        <div className="flex-1 flex flex-col border-r border-gray-800">
          {/* Chat Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div 
                  className={`max-w-[70%] p-3 rounded-lg ${
                    message.sender === 'user' 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-800 text-gray-100'
                  }`}
                >
                  <p className="text-sm">{message.text}</p>
                  <span className="text-xs opacity-70 mt-1 block">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                </div>
              </div>
            ))}
            
            {isAITyping && (
              <div className="flex justify-start">
                <div className="bg-gray-800 text-gray-100 p-3 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm">AI is typing...</span>
                  </div>
                </div>
              </div>
            )}
            <div ref={chatEndRef} />
          </div>

          {/* Chat Input */}
          <div className="p-4 border-t border-gray-800">
            <div className="flex gap-3">
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me anything about your app..."
                className="flex-1 bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                disabled={isAITyping}
              />
              <button
                onClick={sendMessage}
                disabled={!inputMessage.trim() || isAITyping}
                className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white p-2 rounded-lg transition-colors"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Code Progress Panel */}
        {showCodeProgress && (
          <div className="w-80 bg-gray-800/50 border-r border-gray-800 flex flex-col">
            <div className="p-4 border-b border-gray-700">
              <h3 className="text-white font-semibold mb-2">Code Progress</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">{codeProgress.stage}</span>
                  <span className="text-blue-400">{codeProgress.progress}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${codeProgress.progress}%` }}
                  ></div>
                </div>
              </div>
            </div>
            
            <div className="flex-1 p-4 overflow-y-auto">
              <h4 className="text-white text-sm font-medium mb-3">Generated Files</h4>
              <div className="space-y-2">
                {codeProgress.files.map((file, index) => (
                  <div 
                    key={index}
                    className={`flex items-center gap-2 p-2 rounded text-sm ${
                      file === codeProgress.currentFile 
                        ? 'bg-blue-600/20 text-blue-400' 
                        : 'text-gray-400 hover:text-gray-300'
                    }`}
                  >
                    <Code2 className="w-3 h-3" />
                    <span>{file}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Mobile Preview Panel */}
        {showMobilePreview && (
          <div className="w-96 bg-gray-900 flex flex-col items-center justify-center p-8">
            <div className="text-center mb-6">
              <h3 className="text-white font-semibold mb-2">Live Preview</h3>
              <p className="text-gray-400 text-sm">Android • Real-time updates</p>
            </div>
            
            {/* Mobile Phone Frame */}
            <div className="relative">
              <div className="w-64 h-[500px] bg-black rounded-[2.5rem] p-2 shadow-2xl">
                {/* Phone Screen */}
                <div className="w-full h-full bg-white rounded-[2rem] overflow-hidden relative">
                  {/* Status Bar */}
                  <div className="h-6 bg-gray-100 flex items-center justify-between px-4 text-xs text-gray-600">
                    <span>9:41</span>
                    <div className="flex items-center gap-1">
                      <div className="w-1 h-1 bg-gray-600 rounded-full"></div>
                      <div className="w-1 h-1 bg-gray-600 rounded-full"></div>
                      <div className="w-1 h-1 bg-gray-600 rounded-full"></div>
                      <div className="w-4 h-2 border border-gray-600 rounded-sm">
                        <div className="w-3 h-1 bg-green-500 rounded-sm"></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* App Content */}
                  <div className="flex-1 bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
                    <div className="mb-4">
                      <h1 className="text-xl font-bold text-gray-800">Property Hunt</h1>
                      <p className="text-gray-600 text-sm">Find your dream home</p>
                    </div>
                    
                    {/* Search Bar */}
                    <div className="bg-white rounded-lg p-3 mb-4 shadow-sm">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-gray-300 rounded"></div>
                        <span className="text-gray-500 text-sm">Search locations...</span>
                      </div>
                    </div>
                    
                    {/* Property Cards */}
                    <div className="space-y-3">
                      <div className="bg-white rounded-lg p-3 shadow-sm">
                        <div className="w-full h-20 bg-gradient-to-r from-orange-200 to-pink-200 rounded mb-2"></div>
                        <h3 className="font-semibold text-sm">Modern Loft with City View</h3>
                        <p className="text-xs text-gray-600">New York, NY</p>
                        <p className="text-sm font-bold text-green-600">$2,500/mo</p>
                      </div>
                      
                      <div className="bg-white rounded-lg p-3 shadow-sm">
                        <div className="w-full h-20 bg-gradient-to-r from-green-200 to-blue-200 rounded mb-2"></div>
                        <h3 className="font-semibold text-sm">Cozy Studio Apartment</h3>
                        <p className="text-xs text-gray-600">Brooklyn, NY</p>
                        <p className="text-sm font-bold text-green-600">$1,800/mo</p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Bottom Navigation */}
                  <div className="h-16 bg-white border-t border-gray-200 flex items-center justify-around">
                    <div className="w-6 h-6 bg-blue-500 rounded"></div>
                    <div className="w-6 h-6 bg-gray-300 rounded"></div>
                    <div className="w-6 h-6 bg-gray-300 rounded"></div>
                    <div className="w-6 h-6 bg-gray-300 rounded"></div>
                  </div>
                </div>
              </div>
              
              {/* Phone Controls */}
              <div className="absolute -right-16 top-1/2 transform -translate-y-1/2 space-y-2">
                <button className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                  <RotateCcw className="w-4 h-4" />
                </button>
                <button className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                  <Maximize2 className="w-4 h-4" />
                </button>
                <button className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                  <Download className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIChatInterface; 