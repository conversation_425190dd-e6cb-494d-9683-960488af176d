/**
 * Memory Management Demo Component
 * Demonstrates all memory management features and best practices
 */

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  useEnhancedMemoryManagement,
  useSafeEventListener,
  useSafeInterval,
  useSafeTimeout,
  useSafeAnimationFrame,
  useDebouncedCallback,
  useThrottledCallback,
  useVirtualList,
  useObjectPool,
  useMemoryLeakDetection,
  useMemoryMonitoring
} from '../hooks/useEnhancedMemoryManagement';

interface DemoItem {
  id: number;
  name: string;
  value: number;
}

const MemoryManagementDemo: React.FC = () => {
  const [count, setCount] = useState(0);
  const [items, setItems] = useState<DemoItem[]>(() => 
    Array.from({ length: 1000 }, (_, i) => ({
      id: i,
      name: `Item ${i}`,
      value: Math.random() * 100
    }))
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [isAnimating, setIsAnimating] = useState(false);

  // Core memory management
  const memoryManager = useEnhancedMemoryManagement();

  // Memory leak detection
  const { leakWarnings, getMemoryStats } = useMemoryLeakDetection(true);

  // Memory monitoring
  const memoryInfo = useMemoryMonitoring(true);

  // Object pool for reusable objects
  const objectPool = useObjectPool(
    () => ({ temp: 0, processed: false }),
    (obj) => { obj.temp = 0; obj.processed = false; }
  );

  // Virtual list for efficient rendering of large datasets
  const virtualList = useVirtualList(items, {
    itemHeight: 50,
    containerHeight: 300,
    overscan: 5
  });

  // Safe event listeners with proper cleanup
  useSafeEventListener(
    'resize',
    useCallback(() => {
      console.log('Window resized');
    }, []),
    window,
    undefined,
    [] // Proper dependency array
  );

  useSafeEventListener(
    'keydown',
    useCallback((event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsAnimating(false);
      }
    }, []),
    document,
    undefined,
    [] // Proper dependency array
  );

  // Safe interval with proper cleanup
  useSafeInterval(
    useCallback(() => {
      setCount(prev => prev + 1);
    }, []),
    1000, // Update every second
    [], // Proper dependency array
    false // Don't execute immediately
  );

  // Safe timeout with proper cleanup
  useSafeTimeout(
    useCallback(() => {
      console.log('Timeout executed after 5 seconds');
    }, []),
    5000, // 5 seconds
    [] // Proper dependency array
  );

  // Safe animation frame with memory leak prevention
  useSafeAnimationFrame(
    useCallback(() => {
      if (isAnimating) {
        // Perform animation logic
        console.log('Animation frame executed');
      }
    }, [isAnimating]),
    [isAnimating], // Proper dependency array
    isAnimating // Only run when animating
  );

  // Debounced search with proper cleanup
  const debouncedSearch = useDebouncedCallback(
    useCallback((query: string) => {
      console.log('Searching for:', query);
      // Perform search logic here
    }, []),
    300, // 300ms delay
    [] // Proper dependency array
  );

  // Throttled scroll handler with proper cleanup
  const throttledScroll = useThrottledCallback(
    useCallback((scrollTop: number) => {
      virtualList.setScrollTop(scrollTop);
    }, [virtualList]),
    16, // ~60fps
    [virtualList] // Proper dependency array
  );

  // Handle search input change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const query = event.target.value;
    setSearchQuery(query);
    debouncedSearch(query);
  }, [debouncedSearch]);

  // Handle virtual list scroll
  const handleVirtualListScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = event.currentTarget.scrollTop;
    throttledScroll(scrollTop);
  }, [throttledScroll]);

  // Add new items using object pool
  const addItems = useCallback(() => {
    const newItems: DemoItem[] = [];
    for (let i = 0; i < 100; i++) {
      const pooledObj = objectPool.acquire();
      pooledObj.temp = items.length + i;
      pooledObj.processed = true;
      
      newItems.push({
        id: items.length + i,
        name: `Item ${items.length + i}`,
        value: Math.random() * 100
      });
      
      objectPool.release(pooledObj);
    }
    setItems(prev => [...prev, ...newItems]);
  }, [items.length, objectPool]);

  // Toggle animation
  const toggleAnimation = useCallback(() => {
    setIsAnimating(prev => !prev);
  }, []);

  // Force cleanup for demonstration
  const forceCleanup = useCallback(() => {
    memoryManager.forceCleanup();
  }, [memoryManager]);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Memory Management Demo</h1>
      
      {/* Memory Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-2">Memory Manager Stats</h2>
          <pre className="text-sm bg-gray-100 p-2 rounded">
            {JSON.stringify(getMemoryStats(), null, 2)}
          </pre>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-2">Browser Memory</h2>
          {memoryInfo ? (
            <pre className="text-sm bg-gray-100 p-2 rounded">
              {JSON.stringify({
                used: `${Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024)}MB`,
                total: `${Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024)}MB`,
                limit: `${Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024)}MB`
              }, null, 2)}
            </pre>
          ) : (
            <p className="text-gray-500">Memory info not available</p>
          )}
        </div>
      </div>

      {/* Memory Leak Warnings */}
      {leakWarnings.length > 0 && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          <h3 className="font-semibold">Memory Leak Warnings:</h3>
          <ul className="list-disc list-inside">
            {leakWarnings.map((warning, index) => (
              <li key={index}>{warning}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Controls */}
      <div className="flex flex-wrap gap-4 mb-6">
        <button
          onClick={toggleAnimation}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          {isAnimating ? 'Stop Animation' : 'Start Animation'}
        </button>
        
        <button
          onClick={addItems}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Add 100 Items
        </button>
        
        <button
          onClick={forceCleanup}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Force Cleanup
        </button>
        
        <div className="flex items-center gap-2">
          <span>Counter: {count}</span>
        </div>
      </div>

      {/* Search Input (Debounced) */}
      <div className="mb-6">
        <input
          type="text"
          value={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search items (debounced)..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Virtual List Demo */}
      <div className="bg-white p-4 rounded-lg shadow">
        <h2 className="text-lg font-semibold mb-4">
          Virtual List Demo ({items.length} items)
        </h2>
        
        <div
          className="border border-gray-200 rounded overflow-auto"
          style={{ height: '300px' }}
          onScroll={handleVirtualListScroll}
        >
          <div style={{ height: virtualList.totalHeight, position: 'relative' }}>
            <div style={{ transform: `translateY(${virtualList.offsetY}px)` }}>
              {virtualList.visibleItems.map(({ item, index }) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center justify-between p-3 border-b border-gray-100"
                  style={{ height: '50px' }}
                >
                  <span>{item.name}</span>
                  <span className="text-gray-500">
                    Value: {item.value.toFixed(2)}
                  </span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="mt-2 text-sm text-gray-600">
          Visible range: {virtualList.visibleRange.start} - {virtualList.visibleRange.end}
        </div>
      </div>

      {/* Object Pool Stats */}
      <div className="mt-6 bg-white p-4 rounded-lg shadow">
        <h2 className="text-lg font-semibold mb-2">Object Pool Stats</h2>
        <pre className="text-sm bg-gray-100 p-2 rounded">
          {JSON.stringify(objectPool.getStats(), null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default MemoryManagementDemo;