/**
 * Memory Management Best Practices and Guidelines
 * 
 * This file contains utilities and guidelines for proper memory management
 * in React applications to prevent memory leaks and optimize performance.
 */

import { useEffect, useCallback, useMemo } from 'react';

/**
 * Best Practices for Memory Management:
 * 
 * 1. ALWAYS clean up event listeners
 * 2. ALWAYS clear timers and intervals
 * 3. ALWAYS cancel animation frames
 * 4. Use proper dependency arrays in hooks
 * 5. Avoid creating functions in render
 * 6. Use object pooling for frequently created objects
 * 7. Implement virtual scrolling for large lists
 * 8. Monitor memory usage in development
 */

// Common memory leak patterns to avoid:

/**
 * ❌ BAD: Event listener without cleanup
 */
export const badEventListenerExample = () => {
  useEffect(() => {
    const handleResize = () => console.log('resize');
    window.addEventListener('resize', handleResize);
    // Missing cleanup!
  }, []);
};

/**
 * ✅ GOOD: Event listener with proper cleanup
 */
export const goodEventListenerExample = () => {
  useEffect(() => {
    const handleResize = () => console.log('resize');
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
};

/**
 * ❌ BAD: Timer without cleanup
 */
export const badTimerExample = () => {
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('tick');
    }, 1000);
    // Missing cleanup!
  }, []);
};

/**
 * ✅ GOOD: Timer with proper cleanup
 */
export const goodTimerExample = () => {
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('tick');
    }, 1000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);
};

/**
 * ❌ BAD: Animation frame without cleanup
 */
export const badAnimationFrameExample = () => {
  useEffect(() => {
    const animate = () => {
      console.log('animate');
      requestAnimationFrame(animate);
    };
    requestAnimationFrame(animate);
    // Missing cleanup!
  }, []);
};

/**
 * ✅ GOOD: Animation frame with proper cleanup
 */
export const goodAnimationFrameExample = () => {
  useEffect(() => {
    let frameId: number;
    
    const animate = () => {
      console.log('animate');
      frameId = requestAnimationFrame(animate);
    };
    
    frameId = requestAnimationFrame(animate);
    
    return () => {
      cancelAnimationFrame(frameId);
    };
  }, []);
};

/**
 * ❌ BAD: Missing dependencies in useCallback
 */
export const badDependencyArrayExample = (value: string) => {
  const handleClick = useCallback(() => {
    console.log(value); // Uses 'value' but not in deps
  }, []); // Missing 'value' in dependency array
  
  return handleClick;
};

/**
 * ✅ GOOD: Proper dependencies in useCallback
 */
export const goodDependencyArrayExample = (value: string) => {
  const handleClick = useCallback(() => {
    console.log(value);
  }, [value]); // Proper dependency array
  
  return handleClick;
};

/**
 * ❌ BAD: Creating functions in render
 */
export const badFunctionCreationExample = ({ items }: { items: string[] }) => {
  return (
    <div>
      {items.map(item => (
        <button
          key={item}
          onClick={() => console.log(item)} // New function on every render
        >
          {item}
        </button>
      ))}
    </div>
  );
};

/**
 * ✅ GOOD: Memoized function creation
 */
export const goodFunctionCreationExample = ({ items }: { items: string[] }) => {
  const handleClick = useCallback((item: string) => {
    console.log(item);
  }, []);
  
  return (
    <div>
      {items.map(item => (
        <button
          key={item}
          onClick={() => handleClick(item)}
        >
          {item}
        </button>
      ))}
    </div>
  );
};

/**
 * Memory Management Checklist for Components:
 */
export const memoryManagementChecklist = {
  eventListeners: [
    'Are all event listeners removed in cleanup?',
    'Are event listeners using proper dependency arrays?',
    'Are event listeners avoiding memory leaks with stale closures?'
  ],
  timers: [
    'Are all setTimeout calls cleared?',
    'Are all setInterval calls cleared?',
    'Are timer IDs properly tracked?'
  ],
  animationFrames: [
    'Are all requestAnimationFrame calls cancelled?',
    'Are animation loops properly terminated?',
    'Are animation frame IDs tracked for cleanup?'
  ],
  dependencies: [
    'Do all useCallback hooks have proper dependency arrays?',
    'Do all useMemo hooks have proper dependency arrays?',
    'Do all useEffect hooks have proper dependency arrays?'
  ],
  dataStructures: [
    'Are large lists using virtual scrolling?',
    'Are frequently created objects using object pooling?',
    'Are data structures cleaned up when no longer needed?'
  ],
  monitoring: [
    'Is memory usage being monitored in development?',
    'Are memory leaks being detected and fixed?',
    'Are performance metrics being tracked?'
  ]
};

/**
 * Utility function to check for common memory leak patterns
 */
export const detectMemoryLeakPatterns = (componentCode: string): string[] => {
  const issues: string[] = [];
  
  // Check for event listeners without cleanup
  if (componentCode.includes('addEventListener') && !componentCode.includes('removeEventListener')) {
    issues.push('Event listener without cleanup detected');
  }
  
  // Check for timers without cleanup
  if (componentCode.includes('setInterval') && !componentCode.includes('clearInterval')) {
    issues.push('setInterval without cleanup detected');
  }
  
  if (componentCode.includes('setTimeout') && !componentCode.includes('clearTimeout')) {
    issues.push('setTimeout without cleanup detected');
  }
  
  // Check for animation frames without cleanup
  if (componentCode.includes('requestAnimationFrame') && !componentCode.includes('cancelAnimationFrame')) {
    issues.push('requestAnimationFrame without cleanup detected');
  }
  
  return issues;
};

/**
 * Performance optimization utilities
 */
export const performanceUtils = {
  /**
   * Debounce function with automatic cleanup
   */
  debounce: <T extends (...args: unknown[]) => unknown>(
    func: T,
    delay: number
  ): T & { cancel: () => void } => {
    let timeoutId: NodeJS.Timeout | null = null;
    
    const debouncedFn = ((...args: Parameters<T>) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      
      timeoutId = setTimeout(() => {
        func(...args);
        timeoutId = null;
      }, delay);
    }) as T & { cancel: () => void };
    
    debouncedFn.cancel = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };
    
    return debouncedFn;
  },

  /**
   * Throttle function with automatic cleanup
   */
  throttle: <T extends (...args: unknown[]) => unknown>(
    func: T,
    delay: number
  ): T & { cancel: () => void } => {
    let timeoutId: NodeJS.Timeout | null = null;
    let lastExecTime = 0;
    
    const throttledFn = ((...args: Parameters<T>) => {
      const now = Date.now();
      
      if (now - lastExecTime >= delay) {
        func(...args);
        lastExecTime = now;
      } else if (!timeoutId) {
        timeoutId = setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
          timeoutId = null;
        }, delay - (now - lastExecTime));
      }
    }) as T & { cancel: () => void };
    
    throttledFn.cancel = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };
    
    return throttledFn;
  },

  /**
   * Object pool for reducing garbage collection
   */
  createObjectPool: <T>(
    factory: () => T,
    reset: (obj: T) => void = () => {},
    maxSize = 100
  ) => {
    const available: T[] = [];
    const inUse = new Set<T>();
    
    return {
      acquire: (): T => {
        let obj = available.pop();
        if (!obj) {
          obj = factory();
        }
        inUse.add(obj);
        return obj;
      },
      
      release: (obj: T): void => {
        if (!inUse.has(obj)) return;
        
        inUse.delete(obj);
        reset(obj);
        
        if (available.length < maxSize) {
          available.push(obj);
        }
      },
      
      clear: (): void => {
        available.length = 0;
        inUse.clear();
      },
      
      getStats: () => ({
        available: available.length,
        inUse: inUse.size,
        total: available.length + inUse.size
      })
    };
  }
};

/**
 * Memory monitoring utilities
 */
export const memoryMonitoring = {
  /**
   * Get current memory usage (if available)
   */
  getMemoryUsage: (): {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  } | null => {
    if ('memory' in performance) {
      return (performance as Performance & {
        memory: {
          usedJSHeapSize: number;
          totalJSHeapSize: number;
          jsHeapSizeLimit: number;
        };
      }).memory;
    }
    return null;
  },

  /**
   * Force garbage collection (if available)
   */
  forceGC: (): void => {
    if ('gc' in window) {
      (window as Window & { gc?: () => void }).gc?.();
    }
  },

  /**
   * Monitor memory usage over time
   */
  startMemoryMonitoring: (
    callback: (usage: {
      usedJSHeapSize: number;
      totalJSHeapSize: number;
      jsHeapSizeLimit: number;
    }) => void,
    interval = 5000
  ): (() => void) => {
    const monitor = () => {
      const usage = memoryMonitoring.getMemoryUsage();
      if (usage) {
        callback(usage);
      }
    };

    const intervalId = setInterval(monitor, interval);
    
    return () => clearInterval(intervalId);
  }
};

/**
 * Development-only memory leak detection
 */
export const developmentMemoryTools = {
  /**
   * Log memory statistics
   */
  logMemoryStats: (): void => {
    if (process.env['NODE_ENV'] !== 'development') return;
    
    const usage = memoryMonitoring.getMemoryUsage();
    if (usage) {
      console.group('Memory Usage');
      console.log(`Used: ${Math.round(usage.usedJSHeapSize / 1024 / 1024)}MB`);
      console.log(`Total: ${Math.round(usage.totalJSHeapSize / 1024 / 1024)}MB`);
      console.log(`Limit: ${Math.round(usage.jsHeapSizeLimit / 1024 / 1024)}MB`);
      console.log(`Usage: ${Math.round((usage.usedJSHeapSize / usage.jsHeapSizeLimit) * 100)}%`);
      console.groupEnd();
    }
  },

  /**
   * Warn about high memory usage
   */
  warnHighMemoryUsage: (threshold = 0.8): void => {
    if (process.env['NODE_ENV'] !== 'development') return;
    
    const usage = memoryMonitoring.getMemoryUsage();
    if (usage && usage.usedJSHeapSize / usage.jsHeapSizeLimit > threshold) {
      console.warn(
        `High memory usage detected: ${Math.round((usage.usedJSHeapSize / usage.jsHeapSizeLimit) * 100)}%`
      );
    }
  }
};

export default {
  checklist: memoryManagementChecklist,
  detectPatterns: detectMemoryLeakPatterns,
  performance: performanceUtils,
  monitoring: memoryMonitoring,
  development: developmentMemoryTools
};