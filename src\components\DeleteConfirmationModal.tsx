import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, Trash2, Undo, X } from 'lucide-react';
import { Project } from '../types/project';
import { ProjectCRUDService, undoLastDelete } from '../services/projectCRUD';
import { useApp } from '../context/AppContext';
import Button from './ui/Button';
import Modal from './ui/Modal';

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
}

interface UndoNotificationProps {
  isVisible: boolean;
  onUndo: () => void;
  onDismiss: () => void;
  timeLeft: number;
}

const UndoNotification: React.FC<UndoNotificationProps> = ({
  isVisible,
  onUndo,
  onDismiss,
  timeLeft
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 50, scale: 0.9 }}
          className="fixed bottom-4 right-4 z-50 bg-gray-900 text-white rounded-lg shadow-2xl border border-gray-700 p-4 max-w-sm"
        >
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center">
                <Trash2 className="w-4 h-4 text-red-400" />
              </div>
            </div>
            
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white mb-1">
                Project deleted
              </p>
              <p className="text-xs text-gray-300">
                Undo in {timeLeft} seconds
              </p>
              
              {/* Progress bar */}
              <div className="mt-2 w-full h-1 bg-gray-700 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-red-500"
                  initial={{ width: '100%' }}
                  animate={{ width: '0%' }}
                  transition={{ duration: 5, ease: 'linear' }}
                />
              </div>
            </div>
            
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={onUndo}
                leftIcon={<Undo className="w-3 h-3" />}
                className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/20"
              >
                Undo
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                leftIcon={<X className="w-3 h-3" />}
                className="text-gray-400 hover:text-gray-300 hover:bg-gray-700"
              />
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  isOpen,
  onClose,
  project
}) => {
  const { deleteProject, addProject } = useApp();
  const [isDeleting, setIsDeleting] = useState(false);
  const [showUndoNotification, setShowUndoNotification] = useState(false);
  const [undoTimeLeft, setUndoTimeLeft] = useState(5);
  const [deletedProject, setDeletedProject] = useState<Project | null>(null);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setIsDeleting(false);
      setShowUndoNotification(false);
      setUndoTimeLeft(5);
      setDeletedProject(null);
    }
  }, [isOpen]);

  // Countdown timer for undo notification
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (showUndoNotification && undoTimeLeft > 0) {
      interval = setInterval(() => {
        setUndoTimeLeft(prev => {
          if (prev <= 1) {
            setShowUndoNotification(false);
            return 5;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [showUndoNotification, undoTimeLeft]);

  const handleDelete = async () => {
    if (!project) return;

    setIsDeleting(true);

    try {
      await ProjectCRUDService.deleteProject(
        project.id,
        project,
        // Optimistic update - remove from UI immediately
        (projectId) => {
          deleteProject(projectId);
          setDeletedProject(project);
          onClose();
          setShowUndoNotification(true);
          setUndoTimeLeft(5);
        },
        // Success - permanent deletion
        () => {
          console.log('Project permanently deleted');
        },
        // Error
        (error) => {
          console.error('Delete failed:', error);
          // Restore project if delete failed
          if (deletedProject) {
            addProject(deletedProject);
          }
        },
        // Undo function
        (restoredProject) => {
          addProject(restoredProject);
          setShowUndoNotification(false);
          setDeletedProject(null);
        }
      );
    } catch (error) {
      console.error('Delete operation failed:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleUndo = () => {
    const success = undoLastDelete();
    if (success) {
      setShowUndoNotification(false);
      setDeletedProject(null);
    }
  };

  const handleDismissUndo = () => {
    setShowUndoNotification(false);
    setDeletedProject(null);
  };

  if (!project) return null;

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title="Delete Project"
        size="md"
      >
        <div className="space-y-4">
          {/* Warning Icon and Message */}
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
            </div>
            
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Are you sure you want to delete this project?
              </h3>
              <p className="text-gray-600 mb-4">
                This action will delete "<strong>{project.name}</strong>" and all its associated data. 
                You'll have 5 seconds to undo this action after deletion.
              </p>
            </div>
          </div>

          {/* Project Info */}
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <div className="w-5 h-5 bg-gray-400 rounded" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 truncate">{project.name}</h4>
                <p className="text-sm text-gray-500 truncate">{project.description}</p>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-xs text-gray-400 capitalize">{project.type}</span>
                  <span className="text-xs text-gray-400">•</span>
                  <span className="text-xs text-gray-400 capitalize">{project.status}</span>
                  <span className="text-xs text-gray-400">•</span>
                  <span className="text-xs text-gray-400">{project.progress}% complete</span>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
              loading={isDeleting}
              leftIcon={<Trash2 className="w-4 h-4" />}
            >
              {isDeleting ? 'Deleting...' : 'Delete Project'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Undo Notification */}
      <UndoNotification
        isVisible={showUndoNotification}
        onUndo={handleUndo}
        onDismiss={handleDismissUndo}
        timeLeft={undoTimeLeft}
      />
    </>
  );
};

export default DeleteConfirmationModal;