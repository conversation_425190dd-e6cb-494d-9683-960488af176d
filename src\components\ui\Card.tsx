import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  glass?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  children,
  className = '',
  hover = false,
  glass = true,
  padding = 'md',
  onClick
}) => {
  const baseClasses = 'rounded-2xl border transition-all duration-300';
  
  const glassClasses = glass 
    ? 'backdrop-blur-xl bg-white/[0.08] border-white/20 shadow-[0_8px_32px_0_rgba(31,38,135,0.37)]'
    : 'bg-gray-800 border-gray-700';

  const hoverClasses = hover 
    ? 'hover:bg-white/[0.12] hover:border-white/30 hover:shadow-[0_12px_40px_0_rgba(31,38,135,0.5)] hover:scale-105'
    : '';

  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  const interactiveClasses = onClick ? 'cursor-pointer focus-visible-ring' : '';

  const classes = `${baseClasses} ${glassClasses} ${hoverClasses} ${paddingClasses[padding]} ${interactiveClasses} ${className}`;

  const Component = onClick ? 'button' : 'div';
  const interactiveProps = onClick ? { onClick, type: 'button' as const } : {};

  return (
    <Component className={classes} {...interactiveProps}>
      {children}
    </Component>
  );
};

export default Card;