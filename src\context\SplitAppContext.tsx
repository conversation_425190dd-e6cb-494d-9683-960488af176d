import React, { createContext, useContext, useMemo, useCallback } from 'react';
import { useApp } from './AppContext';
import { Project, User, FilterOptions } from '../types/project';

// Split contexts to minimize re-renders

// User Context - rarely changes
interface UserContextValue {
  user: User | null;
  updateUser: (updates: Partial<User>) => void;
}

const UserContext = createContext<UserContextValue | null>(null);

export function UserProvider({ children }: { children: React.ReactNode }) {
  const { user, updateUser } = useApp();
  
  const value = useMemo<UserContextValue>(() => ({
    user,
    updateUser,
  }), [user, updateUser]);

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}

// Projects Context - changes frequently
interface ProjectsContextValue {
  projects: Project[];
  addProject: (project: Omit<Project, 'id' | 'createdAt' | 'lastModified'>) => void;
  updateProject: (id: string, updates: Partial<Project>) => void;
  deleteProject: (id: string) => void;
  duplicateProject: (id: string) => void;
}

const ProjectsContext = createContext<ProjectsContextValue | null>(null);

export function ProjectsProvider({ children }: { children: React.ReactNode }) {
  const { projects, addProject, updateProject, deleteProject, duplicateProject } = useApp();
  
  const value = useMemo<ProjectsContextValue>(() => ({
    projects,
    addProject,
    updateProject,
    deleteProject,
    duplicateProject,
  }), [projects, addProject, updateProject, deleteProject, duplicateProject]);

  return (
    <ProjectsContext.Provider value={value}>
      {children}
    </ProjectsContext.Provider>
  );
}

export function useProjects() {
  const context = useContext(ProjectsContext);
  if (!context) {
    throw new Error('useProjects must be used within a ProjectsProvider');
  }
  return context;
}

// UI State Context - changes frequently but separate from data
interface UIStateContextValue {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  activeModal: string | null;
  setActiveModal: (modal: string | null) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  activeFilters: FilterOptions;
  setActiveFilters: (filters: FilterOptions) => void;
}

const UIStateContext = createContext<UIStateContextValue | null>(null);

export function UIStateProvider({ children }: { children: React.ReactNode }) {
  const { 
    sidebarOpen, 
    setSidebarOpen, 
    activeModal, 
    setActiveModal,
    searchQuery,
    setSearchQuery,
    activeFilters,
    setActiveFilters
  } = useApp();
  
  const value = useMemo<UIStateContextValue>(() => ({
    sidebarOpen,
    setSidebarOpen,
    activeModal,
    setActiveModal,
    searchQuery,
    setSearchQuery,
    activeFilters,
    setActiveFilters,
  }), [
    sidebarOpen, 
    setSidebarOpen, 
    activeModal, 
    setActiveModal,
    searchQuery,
    setSearchQuery,
    activeFilters,
    setActiveFilters
  ]);

  return (
    <UIStateContext.Provider value={value}>
      {children}
    </UIStateContext.Provider>
  );
}

export function useUIState() {
  const context = useContext(UIStateContext);
  if (!context) {
    throw new Error('useUIState must be used within a UIStateProvider');
  }
  return context;
}

// Generation State Context - for app generation specific state
interface GenerationStateContextValue {
  isGenerating: boolean;
  generationProgress: number;
  generationStatus: string;
  setGenerationState: (state: { isGenerating?: boolean; progress?: number; status?: string }) => void;
}

const GenerationStateContext = createContext<GenerationStateContextValue | null>(null);

export function GenerationStateProvider({ children }: { children: React.ReactNode }) {
  const { 
    isGenerating, 
    generationProgress, 
    generationStatus,
    setGenerationState
  } = useApp();
  
  const value = useMemo<GenerationStateContextValue>(() => ({
    isGenerating,
    generationProgress,
    generationStatus,
    setGenerationState,
  }), [isGenerating, generationProgress, generationStatus, setGenerationState]);

  return (
    <GenerationStateContext.Provider value={value}>
      {children}
    </GenerationStateContext.Provider>
  );
}

export function useGenerationState() {
  const context = useContext(GenerationStateContext);
  if (!context) {
    throw new Error('useGenerationState must be used within a GenerationStateProvider');
  }
  return context;
}

// Optimistic Updates Context - for handling optimistic updates
interface OptimisticUpdatesContextValue {
  optimisticUpdates: Map<string, Partial<Project>>;
  addOptimisticUpdate: (id: string, updates: Partial<Project>) => void;
  removeOptimisticUpdate: (id: string) => void;
  clearOptimisticUpdates: () => void;
}

const OptimisticUpdatesContext = createContext<OptimisticUpdatesContextValue | null>(null);

export function OptimisticUpdatesProvider({ children }: { children: React.ReactNode }) {
  const { 
    optimisticUpdates,
    addOptimisticUpdate,
    removeOptimisticUpdate,
    clearOptimisticUpdates
  } = useApp();
  
  const value = useMemo<OptimisticUpdatesContextValue>(() => ({
    optimisticUpdates,
    addOptimisticUpdate,
    removeOptimisticUpdate,
    clearOptimisticUpdates,
  }), [optimisticUpdates, addOptimisticUpdate, removeOptimisticUpdate, clearOptimisticUpdates]);

  return (
    <OptimisticUpdatesContext.Provider value={value}>
      {children}
    </OptimisticUpdatesContext.Provider>
  );
}

export function useOptimisticUpdates() {
  const context = useContext(OptimisticUpdatesContext);
  if (!context) {
    throw new Error('useOptimisticUpdates must be used within a OptimisticUpdatesProvider');
  }
  return context;
}

// Combined provider for all split contexts
export function SplitContextProvider({ children }: { children: React.ReactNode }) {
  return (
    <UserProvider>
      <ProjectsProvider>
        <UIStateProvider>
          <GenerationStateProvider>
            <OptimisticUpdatesProvider>
              {children}
            </OptimisticUpdatesProvider>
          </GenerationStateProvider>
        </UIStateProvider>
      </ProjectsProvider>
    </UserProvider>
  );
}

// Selector hooks for specific data slices
export function useProjectById(id: string) {
  const { projects } = useProjects();
  return useMemo(() => projects.find(p => p.id === id), [projects, id]);
}

export function useProjectsByStatus(status: Project['status']) {
  const { projects } = useProjects();
  return useMemo(() => projects.filter(p => p.status === status), [projects, status]);
}

export function useStarredProjects() {
  const { projects } = useProjects();
  return useMemo(() => projects.filter(p => p.isStarred), [projects]);
}

export function useFilteredProjects() {
  const { projects } = useProjects();
  const { searchQuery, activeFilters } = useUIState();
  
  return useMemo(() => {
    let filtered = projects;
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project => 
        project.name.toLowerCase().includes(query) ||
        project.description.toLowerCase().includes(query) ||
        project.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    // Apply status filter
    if (activeFilters.status && activeFilters.status.length > 0) {
      filtered = filtered.filter(project => activeFilters.status!.includes(project.status));
    }
    
    // Apply type filter
    if (activeFilters.type && activeFilters.type.length > 0) {
      filtered = filtered.filter(project => activeFilters.type!.includes(project.type));
    }
    
    // Apply priority filter
    if (activeFilters.priority && activeFilters.priority.length > 0) {
      filtered = filtered.filter(project => activeFilters.priority!.includes(project.priority));
    }
    
    // Apply starred filter
    if (activeFilters.starred) {
      filtered = filtered.filter(project => project.isStarred);
    }
    
    return filtered;
  }, [projects, searchQuery, activeFilters]);
}