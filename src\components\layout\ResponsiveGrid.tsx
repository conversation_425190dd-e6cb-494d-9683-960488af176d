import React from 'react';
import { cn } from '../../utils/helpers';

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  columns?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className = '',
  columns,
  gap = 'md'
}) => {
  
  const getGridClasses = () => {
    let classes = 'grid';
    
    // Use standard responsive grid if no custom columns specified
    if (!columns) {
      return 'grid-responsive';
    }
    
    // Custom column configuration
    const gapClasses = {
      sm: 'gap-3',
      md: 'gap-4 sm:gap-6',
      lg: 'gap-6 sm:gap-8'
    };
    
    classes += ` ${gapClasses[gap]}`;
    
    // Default to single column on mobile
    classes += ' grid-cols-1';
    
    if (columns.sm) {
      classes += ` sm:grid-cols-${columns.sm}`;
    }
    if (columns.md) {
      classes += ` md:grid-cols-${columns.md}`;
    }
    if (columns.lg) {
      classes += ` lg:grid-cols-${columns.lg}`;
    }
    if (columns.xl) {
      classes += ` xl:grid-cols-${columns.xl}`;
    }
    
    return classes;
  };

  return (
    <div className={cn(getGridClasses(), className)}>
      {children}
    </div>
  );
};

export default ResponsiveGrid;