// Storage utilities for local storage and IndexedDB

// Local Storage utilities
export const localStorage = {
  get: <T>(key: string, defaultValue: T): T => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error(`Error reading from localStorage key "${key}":`, error);
      return defaultValue;
    }
  },

  set: <T>(key: string, value: T): void => {
    try {
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error writing to localStorage key "${key}":`, error);
    }
  },

  remove: (key: string): void => {
    try {
      window.localStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  },

  clear: (): void => {
    try {
      window.localStorage.clear();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }
};

// Storage keys constants
export const STORAGE_KEYS = {
  PROJECTS: 'app_generator_projects',
  USER: 'app_generator_user',
  PREFERENCES: 'app_generator_preferences',
  FILTERS: 'app_generator_filters',
  SEARCH_HISTORY: 'app_generator_search_history'
} as const;

// IndexedDB utilities (for future implementation)
export const indexedDB = {
  // Placeholder for IndexedDB implementation
  isSupported: (): boolean => {
    return 'indexedDB' in window;
  }
};