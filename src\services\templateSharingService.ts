// Template sharing and collaboration service
import { AppTemplate } from './templateService';
import { Project } from '../types/project';
import { GeneratedApp } from '../types/api';

export interface SharedTemplate {
  id: string;
  template: AppTemplate;
  sharedBy: string;
  sharedAt: string;
  accessLevel: 'public' | 'private' | 'team';
  downloads: number;
  rating: number;
  reviews: TemplateReview[];
  tags: string[];
}

export interface TemplateReview {
  id: string;
  userId: string;
  userName: string;
  rating: number;
  comment: string;
  createdAt: string;
}

export interface ShareOptions {
  accessLevel: 'public' | 'private' | 'team';
  allowModifications: boolean;
  includeMarketAnalysis: boolean;
  includeTechStack: boolean;
  customMessage?: string;
}

export interface CollaborationInvite {
  id: string;
  projectId: string;
  invitedBy: string;
  invitedUser: string;
  role: 'viewer' | 'editor' | 'admin';
  status: 'pending' | 'accepted' | 'declined';
  createdAt: string;
  expiresAt: string;
}

export class TemplateSharingService {
  private static sharedTemplates: SharedTemplate[] = [];
  private static collaborationInvites: CollaborationInvite[] = [];

  // Template Sharing
  static async shareTemplate(template: AppTemplate, options: ShareOptions): Promise<string> {
    const shareId = this.generateShareId();
    const sharedTemplate: SharedTemplate = {
      id: shareId,
      template: { ...template },
      sharedBy: 'current-user', // In real app, get from auth context
      sharedAt: new Date().toISOString(),
      accessLevel: options.accessLevel,
      downloads: 0,
      rating: 0,
      reviews: [],
      tags: template.tags
    };

    // Remove sensitive data based on options
    if (!options.includeMarketAnalysis) {
      // Remove market analysis data
    }
    if (!options.includeTechStack) {
      // Remove tech stack details
    }

    this.sharedTemplates.push(sharedTemplate);
    return shareId;
  }

  static async shareProject(project: Project, options: ShareOptions): Promise<string> {
    // Convert project to template for sharing
    const template: AppTemplate = {
      id: `shared-${project.id}`,
      name: project.name,
      description: project.description,
      type: project.type,
      complexity: project.complexity,
      features: project.features || [],
      techStack: project.techStack || {
        frontend: [],
        backend: [],
        database: [],
        deployment: [],
        tools: []
      },
      estimatedHours: this.estimateHoursFromProject(project),
      tags: project.tags,
      customizable: true,
      popularity: 1
    };

    return this.shareTemplate(template, options);
  }

  static async shareGeneratedApp(app: GeneratedApp, options: ShareOptions): Promise<string> {
    // Convert generated app to template for sharing
    const template: AppTemplate = {
      id: `shared-app-${app.id}`,
      name: app.name,
      description: app.description,
      type: app.category as any,
      complexity: app.complexity,
      features: app.features,
      techStack: app.techStack,
      estimatedHours: this.parseEstimatedTime(app.estimatedTime),
      tags: ['Generated', 'AI', app.category],
      customizable: true,
      popularity: 1
    };

    return this.shareTemplate(template, options);
  }

  // Template Discovery
  static getPublicTemplates(): SharedTemplate[] {
    return this.sharedTemplates.filter(t => t.accessLevel === 'public');
  }

  static searchSharedTemplates(query: string): SharedTemplate[] {
    const searchTerm = query.toLowerCase();
    return this.sharedTemplates.filter(template =>
      template.accessLevel === 'public' &&
      (template.template.name.toLowerCase().includes(searchTerm) ||
       template.template.description.toLowerCase().includes(searchTerm) ||
       template.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
    );
  }

  static getTemplatesByCategory(category: string): SharedTemplate[] {
    return this.sharedTemplates.filter(template =>
      template.accessLevel === 'public' &&
      template.template.type === category
    );
  }

  static getPopularTemplates(limit: number = 10): SharedTemplate[] {
    return this.sharedTemplates
      .filter(t => t.accessLevel === 'public')
      .sort((a, b) => b.downloads - a.downloads)
      .slice(0, limit);
  }

  static getTopRatedTemplates(limit: number = 10): SharedTemplate[] {
    return this.sharedTemplates
      .filter(t => t.accessLevel === 'public' && t.rating > 0)
      .sort((a, b) => b.rating - a.rating)
      .slice(0, limit);
  }

  // Template Access
  static async getSharedTemplate(shareId: string): Promise<SharedTemplate | null> {
    const template = this.sharedTemplates.find(t => t.id === shareId);
    if (template) {
      // Increment download count
      template.downloads++;
    }
    return template || null;
  }

  static async downloadTemplate(shareId: string): Promise<AppTemplate | null> {
    const sharedTemplate = await this.getSharedTemplate(shareId);
    return sharedTemplate?.template || null;
  }

  // Template Reviews
  static async addReview(shareId: string, review: Omit<TemplateReview, 'id' | 'createdAt'>): Promise<boolean> {
    const template = this.sharedTemplates.find(t => t.id === shareId);
    if (!template) return false;

    const newReview: TemplateReview = {
      ...review,
      id: this.generateId(),
      createdAt: new Date().toISOString()
    };

    template.reviews.push(newReview);
    
    // Update average rating
    const totalRating = template.reviews.reduce((sum, r) => sum + r.rating, 0);
    template.rating = totalRating / template.reviews.length;

    return true;
  }

  static getTemplateReviews(shareId: string): TemplateReview[] {
    const template = this.sharedTemplates.find(t => t.id === shareId);
    return template?.reviews || [];
  }

  // Collaboration
  static async inviteCollaborator(
    projectId: string,
    userEmail: string,
    role: 'viewer' | 'editor' | 'admin'
  ): Promise<string> {
    const invite: CollaborationInvite = {
      id: this.generateId(),
      projectId,
      invitedBy: 'current-user',
      invitedUser: userEmail,
      role,
      status: 'pending',
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
    };

    this.collaborationInvites.push(invite);
    
    // In real app, send email notification
    this.sendCollaborationEmail(invite);
    
    return invite.id;
  }

  static async acceptInvite(inviteId: string): Promise<boolean> {
    const invite = this.collaborationInvites.find(i => i.id === inviteId);
    if (!invite || invite.status !== 'pending') return false;

    invite.status = 'accepted';
    
    // In real app, add user to project collaborators
    return true;
  }

  static async declineInvite(inviteId: string): Promise<boolean> {
    const invite = this.collaborationInvites.find(i => i.id === inviteId);
    if (!invite || invite.status !== 'pending') return false;

    invite.status = 'declined';
    return true;
  }

  static getPendingInvites(userId: string): CollaborationInvite[] {
    return this.collaborationInvites.filter(
      i => i.invitedUser === userId && i.status === 'pending'
    );
  }

  static getProjectCollaborators(projectId: string): CollaborationInvite[] {
    return this.collaborationInvites.filter(
      i => i.projectId === projectId && i.status === 'accepted'
    );
  }

  // Share Link Generation
  static generateShareLink(shareId: string): string {
    const baseUrl = window.location.origin;
    return `${baseUrl}/shared/template/${shareId}`;
  }

  static generateProjectShareLink(projectId: string): string {
    const baseUrl = window.location.origin;
    return `${baseUrl}/shared/project/${projectId}`;
  }

  // Template Collections
  static createCollection(name: string, description: string, templateIds: string[]): string {
    // Implementation for creating template collections
    const collectionId = this.generateId();
    // Store collection data
    return collectionId;
  }

  static addToCollection(collectionId: string, templateId: string): boolean {
    // Implementation for adding templates to collections
    return true;
  }

  // Analytics
  static getTemplateAnalytics(shareId: string) {
    const template = this.sharedTemplates.find(t => t.id === shareId);
    if (!template) return null;

    return {
      downloads: template.downloads,
      rating: template.rating,
      reviewCount: template.reviews.length,
      shareDate: template.sharedAt,
      popularity: this.calculatePopularity(template)
    };
  }

  static getUserSharingStats(userId: string) {
    const userTemplates = this.sharedTemplates.filter(t => t.sharedBy === userId);
    
    return {
      totalShared: userTemplates.length,
      totalDownloads: userTemplates.reduce((sum, t) => sum + t.downloads, 0),
      averageRating: userTemplates.reduce((sum, t) => sum + t.rating, 0) / userTemplates.length,
      mostPopular: userTemplates.sort((a, b) => b.downloads - a.downloads)[0]
    };
  }

  // Helper Methods
  private static generateShareId(): string {
    return 'share_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private static estimateHoursFromProject(project: Project): number {
    // Simple estimation based on complexity and features
    const baseHours = {
      simple: 80,
      medium: 200,
      complex: 400
    };

    const complexityHours = baseHours[project.complexity];
    const featureHours = (project.features?.length || 0) * 20;
    
    return complexityHours + featureHours;
  }

  private static parseEstimatedTime(timeString: string): number {
    // Parse strings like "4-6 weeks" into hours
    const match = timeString.match(/(\d+)(?:-(\d+))?\s*(week|month)/);
    if (!match) return 160; // Default 4 weeks

    const min = parseInt(match[1]);
    const max = match[2] ? parseInt(match[2]) : min;
    const unit = match[3];
    
    const average = (min + max) / 2;
    const multiplier = unit === 'week' ? 40 : 160; // 40 hours/week, 160 hours/month
    
    return Math.round(average * multiplier);
  }

  private static calculatePopularity(template: SharedTemplate): number {
    // Simple popularity calculation
    const downloadWeight = 0.6;
    const ratingWeight = 0.4;
    
    const normalizedDownloads = Math.min(template.downloads / 100, 1);
    const normalizedRating = template.rating / 5;
    
    return (normalizedDownloads * downloadWeight + normalizedRating * ratingWeight) * 10;
  }

  private static sendCollaborationEmail(invite: CollaborationInvite): void {
    // In real app, integrate with email service
    console.log(`Collaboration invite sent to ${invite.invitedUser} for project ${invite.projectId}`);
  }

  // Export/Import for sharing
  static exportForSharing(template: AppTemplate, options: ShareOptions): string {
    const shareData = {
      template,
      options,
      sharedAt: new Date().toISOString(),
      version: '1.0'
    };

    return JSON.stringify(shareData, null, 2);
  }

  static importFromShare(shareData: string): AppTemplate | null {
    try {
      const data = JSON.parse(shareData);
      return data.template || null;
    } catch (error) {
      console.error('Failed to import shared template:', error);
      return null;
    }
  }
}