import { useCallback, useMemo } from 'react';
import { useApp } from '../context/AppContext';
import { Project, ProjectStatus, ProjectType, Priority, Complexity } from '../types';
import { generateId } from '../utils/helpers';

/**
 * Custom hook for managing projects with CRUD operations and optimistic updates
 */
export function useProjectManager() {
  const {
    projects,
    currentProject,
    optimisticUpdates,
    addProject,
    updateProject,
    deleteProject,
    setCurrentProject,
    addOptimisticUpdate,
    removeOptimisticUpdate,
    clearOptimisticUpdates,
    addPendingChange,
    removePendingChange,
    setError
  } = useApp();

  // Get projects with optimistic updates applied
  const projectsWithOptimisticUpdates = useMemo(() => {
    return projects.map(project => {
      const optimisticUpdate = optimisticUpdates[project.id];
      return optimisticUpdate ? { ...project, ...optimisticUpdate } : project;
    });
  }, [projects, optimisticUpdates]);

  // Create a new project with optimistic update
  const createProject = useCallback(async (
    projectData: Omit<Project, 'id' | 'createdAt' | 'lastModified'>
  ) => {
    const tempId = generateId();
    const changeId = generateId();
    
    try {
      // Add optimistic update immediately
      const optimisticProject: Project = {
        ...projectData,
        id: tempId,
        createdAt: new Date().toISOString(),
        lastModified: new Date().toISOString()
      };
      
      addOptimisticUpdate(tempId, optimisticProject);
      addPendingChange(changeId);
      
      // Simulate async operation (replace with actual API call)
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Add the actual project
      addProject(projectData);
      
      // Clean up optimistic update
      removeOptimisticUpdate(tempId);
      removePendingChange(changeId);
      
      return true;
    } catch (error) {
      // Revert optimistic update on error
      removeOptimisticUpdate(tempId);
      removePendingChange(changeId);
      setError(error instanceof Error ? error.message : 'Failed to create project');
      return false;
    }
  }, [addProject, addOptimisticUpdate, removeOptimisticUpdate, addPendingChange, removePendingChange, setError]);

  // Update a project with optimistic update
  const updateProjectOptimistic = useCallback(async (
    id: string,
    updates: Partial<Project>
  ) => {
    const changeId = generateId();
    
    try {
      // Apply optimistic update immediately
      addOptimisticUpdate(id, updates);
      addPendingChange(changeId);
      
      // Simulate async operation (replace with actual API call)
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Apply the actual update
      updateProject(id, updates);
      
      // Clean up optimistic update
      removeOptimisticUpdate(id);
      removePendingChange(changeId);
      
      return true;
    } catch (error) {
      // Revert optimistic update on error
      removeOptimisticUpdate(id);
      removePendingChange(changeId);
      setError(error instanceof Error ? error.message : 'Failed to update project');
      return false;
    }
  }, [updateProject, addOptimisticUpdate, removeOptimisticUpdate, addPendingChange, removePendingChange, setError]);

  // Delete a project with optimistic update
  const deleteProjectOptimistic = useCallback(async (id: string) => {
    const changeId = generateId();
    const projectToDelete = projects.find(p => p.id === id);
    
    if (!projectToDelete) {
      setError('Project not found');
      return false;
    }
    
    try {
      // Apply optimistic update (hide the project)
      addOptimisticUpdate(id, { status: 'archived' as ProjectStatus });
      addPendingChange(changeId);
      
      // Simulate async operation (replace with actual API call)
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Actually delete the project
      deleteProject(id);
      
      // Clean up optimistic update
      removeOptimisticUpdate(id);
      removePendingChange(changeId);
      
      return true;
    } catch (error) {
      // Revert optimistic update on error
      removeOptimisticUpdate(id);
      removePendingChange(changeId);
      setError(error instanceof Error ? error.message : 'Failed to delete project');
      return false;
    }
  }, [projects, deleteProject, addOptimisticUpdate, removeOptimisticUpdate, addPendingChange, removePendingChange, setError]);

  // Toggle project star status
  const toggleProjectStar = useCallback(async (id: string) => {
    const project = projectsWithOptimisticUpdates.find(p => p.id === id);
    if (!project) return false;
    
    return updateProjectOptimistic(id, { isStarred: !project.isStarred });
  }, [projectsWithOptimisticUpdates, updateProjectOptimistic]);

  // Update project status
  const updateProjectStatus = useCallback(async (id: string, status: ProjectStatus) => {
    return updateProjectOptimistic(id, { status });
  }, [updateProjectOptimistic]);

  // Update project progress
  const updateProjectProgress = useCallback(async (id: string, progress: number) => {
    const clampedProgress = Math.max(0, Math.min(100, progress));
    return updateProjectOptimistic(id, { progress: clampedProgress });
  }, [updateProjectOptimistic]);

  // Duplicate a project
  const duplicateProject = useCallback(async (id: string) => {
    const project = projects.find(p => p.id === id);
    if (!project) {
      setError('Project not found');
      return false;
    }
    
    const duplicatedProject = {
      ...project,
      name: `${project.name} (Copy)`,
      status: 'draft' as ProjectStatus,
      progress: 0,
      isStarred: false
    };
    
    // Remove fields that shouldn't be duplicated
    const { id: _, createdAt: __, lastModified: ___, ...projectData } = duplicatedProject;
    
    return createProject(projectData);
  }, [projects, createProject, setError]);

  // Get projects by status
  const getProjectsByStatus = useCallback((status: ProjectStatus) => {
    return projectsWithOptimisticUpdates.filter(project => project.status === status);
  }, [projectsWithOptimisticUpdates]);

  // Get projects by type
  const getProjectsByType = useCallback((type: ProjectType) => {
    return projectsWithOptimisticUpdates.filter(project => project.type === type);
  }, [projectsWithOptimisticUpdates]);

  // Get starred projects
  const getStarredProjects = useCallback(() => {
    return projectsWithOptimisticUpdates.filter(project => project.isStarred);
  }, [projectsWithOptimisticUpdates]);

  // Get recent projects (modified in last 7 days)
  const getRecentProjects = useCallback(() => {
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    return projectsWithOptimisticUpdates.filter(project => 
      new Date(project.lastModified) > sevenDaysAgo
    );
  }, [projectsWithOptimisticUpdates]);

  // Search projects
  const searchProjects = useCallback((query: string) => {
    if (!query.trim()) return projectsWithOptimisticUpdates;
    
    const lowercaseQuery = query.toLowerCase();
    return projectsWithOptimisticUpdates.filter(project =>
      project.name.toLowerCase().includes(lowercaseQuery) ||
      project.description.toLowerCase().includes(lowercaseQuery) ||
      project.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  }, [projectsWithOptimisticUpdates]);

  // Sort projects
  const sortProjects = useCallback((
    projects: Project[],
    sortBy: 'name' | 'date' | 'status' | 'progress',
    order: 'asc' | 'desc' = 'desc'
  ) => {
    return [...projects].sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'date':
          comparison = new Date(a.lastModified).getTime() - new Date(b.lastModified).getTime();
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        case 'progress':
          comparison = a.progress - b.progress;
          break;
      }
      
      return order === 'asc' ? comparison : -comparison;
    });
  }, []); // Empty dependency array is correct as this is a pure function

  return {
    // Data
    projects: projectsWithOptimisticUpdates,
    currentProject,
    
    // CRUD operations
    createProject,
    updateProject: updateProjectOptimistic,
    deleteProject: deleteProjectOptimistic,
    duplicateProject,
    
    // Specific operations
    toggleProjectStar,
    updateProjectStatus,
    updateProjectProgress,
    setCurrentProject,
    
    // Query operations
    getProjectsByStatus,
    getProjectsByType,
    getStarredProjects,
    getRecentProjects,
    searchProjects,
    sortProjects,
    
    // Utility
    clearOptimisticUpdates
  };
}