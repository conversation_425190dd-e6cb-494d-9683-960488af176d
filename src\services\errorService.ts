import { ErrorType, <PERSON>rrorIn<PERSON>, <PERSON>rror<PERSON><PERSON><PERSON> } from '../types/error';

class ErrorService {
  private errorLog: ErrorInfo[] = [];
  private maxLogSize = 100;

  logError(error: Error, errorType: ErrorType, componentStack?: string): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type: errorType,
      message: error.message,
      stack: error.stack,
      componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    this.errorLog.push(errorInfo);
    
    // Keep log size manageable
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(-this.maxLogSize);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error logged:', errorInfo);
    }

    // In production, you might want to send to an error tracking service
    this.reportError(errorInfo);

    return errorInfo;
  }

  private reportError(errorInfo: ErrorInfo): void {
    // In a real app, send to error tracking service like Sentry
    // For now, just store in localStorage for debugging
    try {
      const existingErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      existingErrors.push(errorInfo);
      localStorage.setItem('app_errors', JSON.stringify(existingErrors.slice(-50)));

      // In production, you would do something like:
      // if (process.env.NODE_ENV === 'production') {
      //   fetch('/api/errors', {
      //     method: 'POST',
      //     headers: { 'Content-Type': 'application/json' },
      //     body: JSON.stringify(errorInfo)
      //   }).catch(() => {
      //     // Silently fail error reporting
      //   });
      // }
    } catch (e) {
      console.warn('Failed to store error in localStorage:', e);
    }
  }

  getErrorHandler(errorType: ErrorType, customMessage?: string): ErrorHandler {
    const handlers: Record<ErrorType, Omit<ErrorHandler, 'type'>> = {
      [ErrorType.GENERATION_FAILED]: {
        message: customMessage || 'Failed to generate app. Please try again.',
        canRecover: true,
        retry: () => {
          // Clear any cached data that might be causing issues
          sessionStorage.removeItem('generation_cache');
          window.location.reload();
        }
      },
      [ErrorType.STORAGE_ERROR]: {
        message: customMessage || 'Unable to save your data. Please check your browser settings.',
        canRecover: true,
        recovery: () => {
          // Try to backup data before clearing
          try {
            const backup = localStorage.getItem('projects');
            if (backup) {
              sessionStorage.setItem('projects_backup', backup);
            }
          } catch (e) {
            console.warn('Failed to backup data:', e);
          }
          localStorage.clear();
          window.location.reload();
        }
      },
      [ErrorType.VALIDATION_ERROR]: {
        message: customMessage || 'Please check your input and try again.',
        canRecover: true
      },
      [ErrorType.NETWORK_ERROR]: {
        message: customMessage || 'Network error. Please check your connection.',
        canRecover: true,
        retry: () => window.location.reload()
      },
      [ErrorType.COMPONENT_ERROR]: {
        message: customMessage || 'A component failed to load. Refreshing might help.',
        canRecover: true,
        retry: () => window.location.reload()
      },
      [ErrorType.CRITICAL_ERROR]: {
        message: customMessage || 'A critical error occurred. The app will restart.',
        canRecover: false,
        recovery: () => {
          localStorage.clear();
          window.location.href = '/';
        }
      }
    };

    return {
      type: errorType,
      ...handlers[errorType]
    };
  }

  getErrorLog(): ErrorInfo[] {
    return [...this.errorLog];
  }

  clearErrorLog(): void {
    this.errorLog = [];
    localStorage.removeItem('app_errors');
  }
}

export const errorService = new ErrorService();