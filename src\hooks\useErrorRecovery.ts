import { useCallback, useState } from 'react';
import { ErrorType } from '../types/error';
import { errorService } from '../services/errorService';

interface UseErrorRecoveryOptions {
  maxRetries?: number;
  retryDelay?: number;
  onMaxRetriesReached?: () => void;
}

interface UseErrorRecoveryReturn {
  retryCount: number;
  isRecovering: boolean;
  canRetry: boolean;
  executeWithRecovery: <T>(
    fn: () => Promise<T>,
    errorType?: ErrorType
  ) => Promise<T | null>;
  reset: () => void;
}

export const useErrorRecovery = (
  options: UseErrorRecoveryOptions = {}
): UseErrorRecoveryReturn => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    onMaxRetriesReached
  } = options;

  const [retryCount, setRetryCount] = useState(0);
  const [isRecovering, setIsRecovering] = useState(false);

  const canRetry = retryCount < maxRetries;

  const executeWithRecovery = useCallback(async <T>(
    fn: () => Promise<T>,
    errorType: ErrorType = ErrorType.COMPONENT_ERROR
  ): Promise<T | null> => {
    try {
      setIsRecovering(false);
      const result = await fn();
      setRetryCount(0); // Reset on success
      return result;
    } catch (error) {
      errorService.logError(error as Error, errorType);

      if (canRetry) {
        setIsRecovering(true);
        setRetryCount(prev => prev + 1);
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        
        // Recursive retry
        return executeWithRecovery(fn, errorType);
      } else {
        setIsRecovering(false);
        onMaxRetriesReached?.();
        throw error;
      }
    }
  }, [canRetry, retryDelay, onMaxRetriesReached]);

  const reset = useCallback(() => {
    setRetryCount(0);
    setIsRecovering(false);
  }, []);

  return {
    retryCount,
    isRecovering,
    canRetry,
    executeWithRecovery,
    reset
  };
};