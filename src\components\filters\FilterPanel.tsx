// Comprehensive filter panel component

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Filter, 
  X, 
  Calendar, 
  Star, 
  Tag, 
  BarChart3, 
  Clock, 
  Save, 
  Trash2,
  RotateCcw,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { FilterCriteria, FilterPreset, FilterStats, SortCriteria } from '../../services/filterService';
import { ProjectType, ProjectStatus, Priority, Complexity } from '../../types/project';

interface FilterPanelProps {
  isOpen: boolean;
  onClose: () => void;
  criteria: FilterCriteria;
  onCriteriaChange: (criteria: FilterCriteria) => void;
  sortCriteria: SortCriteria;
  onSortChange: (sort: SortCriteria) => void;
  stats: FilterStats;
  presets: FilterPreset[];
  onSavePreset: (name: string, description: string) => void;
  onLoadPreset: (preset: FilterPreset) => void;
  onDeletePreset: (id: string) => void;
  onResetFilters: () => void;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  isOpen,
  onClose,
  criteria,
  onCriteriaChange,
  sortCriteria,
  onSortChange,
  stats,
  presets,
  onSavePreset,
  onLoadPreset,
  onDeletePreset,
  onResetFilters
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['status', 'type', 'priority'])
  );
  const [showSavePreset, setShowSavePreset] = useState(false);
  const [presetName, setPresetName] = useState('');
  const [presetDescription, setPresetDescription] = useState('');

  // Toggle section expansion
  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  // Handle multi-select changes
  const handleMultiSelectChange = <T extends string>(
    field: keyof FilterCriteria,
    value: T,
    currentValues: T[]
  ) => {
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];
    
    onCriteriaChange({
      ...criteria,
      [field]: newValues
    });
  };

  // Handle date range changes
  const handleDateRangeChange = (field: 'start' | 'end', value: string) => {
    onCriteriaChange({
      ...criteria,
      dateRange: {
        ...criteria.dateRange,
        [field]: value ? new Date(value) : null
      }
    });
  };

  // Handle progress range changes
  const handleProgressRangeChange = (field: 'min' | 'max', value: number) => {
    onCriteriaChange({
      ...criteria,
      progressRange: {
        ...criteria.progressRange,
        [field]: value
      }
    });
  };

  // Save preset
  const handleSavePreset = () => {
    if (presetName.trim()) {
      onSavePreset(presetName.trim(), presetDescription.trim());
      setPresetName('');
      setPresetDescription('');
      setShowSavePreset(false);
    }
  };

  // Format date for input
  const formatDateForInput = (date: Date | null): string => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  // Get popular tags
  const popularTags = Object.entries(stats.tagCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([tag]) => tag);

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        onClick={(e) => e.stopPropagation()}
        className="bg-gray-900 border border-gray-700 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <Filter className="w-6 h-6 text-blue-400" />
            <div>
              <h2 className="text-xl font-semibold text-white">Advanced Filters</h2>
              <p className="text-sm text-gray-400">
                {stats.filteredProjects} of {stats.totalProjects} projects shown
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={onResetFilters}
              className="flex items-center gap-2 px-3 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              <RotateCcw className="w-4 h-4" />
              Reset
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6">
            {/* Left Column - Basic Filters */}
            <div className="space-y-6">
              {/* Status Filter */}
              <div className="space-y-3">
                <button
                  onClick={() => toggleSection('status')}
                  className="flex items-center justify-between w-full text-left"
                >
                  <h3 className="font-medium text-white">Status</h3>
                  {expandedSections.has('status') ? (
                    <ChevronUp className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  )}
                </button>
                <AnimatePresence>
                  {expandedSections.has('status') && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      className="space-y-2"
                    >
                      {(['active', 'completed', 'draft', 'archived', 'error'] as ProjectStatus[]).map(status => (
                        <label key={status} className="flex items-center gap-3 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={criteria.status.includes(status)}
                            onChange={() => handleMultiSelectChange('status', status, criteria.status)}
                            className="w-4 h-4 text-blue-500 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-300 capitalize">{status}</span>
                          <span className="text-xs text-gray-500 ml-auto">
                            {stats.statusCounts[status] || 0}
                          </span>
                        </label>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Type Filter */}
              <div className="space-y-3">
                <button
                  onClick={() => toggleSection('type')}
                  className="flex items-center justify-between w-full text-left"
                >
                  <h3 className="font-medium text-white">Type</h3>
                  {expandedSections.has('type') ? (
                    <ChevronUp className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  )}
                </button>
                <AnimatePresence>
                  {expandedSections.has('type') && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      className="space-y-2"
                    >
                      {(['mobile', 'web', 'form', 'ai', 'ecommerce'] as ProjectType[]).map(type => (
                        <label key={type} className="flex items-center gap-3 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={criteria.type.includes(type)}
                            onChange={() => handleMultiSelectChange('type', type, criteria.type)}
                            className="w-4 h-4 text-blue-500 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-300 capitalize">{type}</span>
                          <span className="text-xs text-gray-500 ml-auto">
                            {stats.typeCounts[type] || 0}
                          </span>
                        </label>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Priority Filter */}
              <div className="space-y-3">
                <button
                  onClick={() => toggleSection('priority')}
                  className="flex items-center justify-between w-full text-left"
                >
                  <h3 className="font-medium text-white">Priority</h3>
                  {expandedSections.has('priority') ? (
                    <ChevronUp className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  )}
                </button>
                <AnimatePresence>
                  {expandedSections.has('priority') && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      className="space-y-2"
                    >
                      {(['high', 'medium', 'low'] as Priority[]).map(priority => (
                        <label key={priority} className="flex items-center gap-3 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={criteria.priority.includes(priority)}
                            onChange={() => handleMultiSelectChange('priority', priority, criteria.priority)}
                            className="w-4 h-4 text-blue-500 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-300 capitalize">{priority}</span>
                          <span className="text-xs text-gray-500 ml-auto">
                            {stats.priorityCounts[priority] || 0}
                          </span>
                        </label>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* Middle Column - Advanced Filters */}
            <div className="space-y-6">
              {/* Complexity Filter */}
              <div className="space-y-3">
                <button
                  onClick={() => toggleSection('complexity')}
                  className="flex items-center justify-between w-full text-left"
                >
                  <h3 className="font-medium text-white">Complexity</h3>
                  {expandedSections.has('complexity') ? (
                    <ChevronUp className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  )}
                </button>
                <AnimatePresence>
                  {expandedSections.has('complexity') && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      className="space-y-2"
                    >
                      {(['simple', 'medium', 'complex'] as Complexity[]).map(complexity => (
                        <label key={complexity} className="flex items-center gap-3 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={criteria.complexity.includes(complexity)}
                            onChange={() => handleMultiSelectChange('complexity', complexity, criteria.complexity)}
                            className="w-4 h-4 text-blue-500 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-300 capitalize">{complexity}</span>
                          <span className="text-xs text-gray-500 ml-auto">
                            {stats.complexityCounts[complexity] || 0}
                          </span>
                        </label>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Date Range Filter */}
              <div className="space-y-3">
                <button
                  onClick={() => toggleSection('dateRange')}
                  className="flex items-center justify-between w-full text-left"
                >
                  <h3 className="font-medium text-white flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Date Range
                  </h3>
                  {expandedSections.has('dateRange') ? (
                    <ChevronUp className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  )}
                </button>
                <AnimatePresence>
                  {expandedSections.has('dateRange') && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      className="space-y-3"
                    >
                      <div>
                        <label className="block text-sm text-gray-400 mb-1">From</label>
                        <input
                          type="date"
                          value={formatDateForInput(criteria.dateRange.start)}
                          onChange={(e) => handleDateRangeChange('start', e.target.value)}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm text-gray-400 mb-1">To</label>
                        <input
                          type="date"
                          value={formatDateForInput(criteria.dateRange.end)}
                          onChange={(e) => handleDateRangeChange('end', e.target.value)}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Progress Range Filter */}
              <div className="space-y-3">
                <button
                  onClick={() => toggleSection('progress')}
                  className="flex items-center justify-between w-full text-left"
                >
                  <h3 className="font-medium text-white flex items-center gap-2">
                    <BarChart3 className="w-4 h-4" />
                    Progress Range
                  </h3>
                  {expandedSections.has('progress') ? (
                    <ChevronUp className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  )}
                </button>
                <AnimatePresence>
                  {expandedSections.has('progress') && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      className="space-y-3"
                    >
                      <div className="flex items-center gap-3">
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={criteria.progressRange.min}
                          onChange={(e) => handleProgressRangeChange('min', parseInt(e.target.value))}
                          className="flex-1"
                        />
                        <span className="text-sm text-gray-400 w-12 text-right">
                          {criteria.progressRange.min}%
                        </span>
                      </div>
                      <div className="flex items-center gap-3">
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={criteria.progressRange.max}
                          onChange={(e) => handleProgressRangeChange('max', parseInt(e.target.value))}
                          className="flex-1"
                        />
                        <span className="text-sm text-gray-400 w-12 text-right">
                          {criteria.progressRange.max}%
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        Progress: {criteria.progressRange.min}% - {criteria.progressRange.max}%
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Special Filters */}
              <div className="space-y-3">
                <h3 className="font-medium text-white">Special Filters</h3>
                <div className="space-y-2">
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={criteria.isStarred === true}
                      onChange={(e) => onCriteriaChange({
                        ...criteria,
                        isStarred: e.target.checked ? true : undefined
                      })}
                      className="w-4 h-4 text-blue-500 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <Star className="w-4 h-4 text-yellow-400" />
                    <span className="text-sm text-gray-300">Starred only</span>
                  </label>
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={criteria.hasRoute === true}
                      onChange={(e) => onCriteriaChange({
                        ...criteria,
                        hasRoute: e.target.checked ? true : undefined
                      })}
                      className="w-4 h-4 text-blue-500 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-300">Has route</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Right Column - Presets & Sorting */}
            <div className="space-y-6">
              {/* Sorting */}
              <div className="space-y-3">
                <h3 className="font-medium text-white">Sort By</h3>
                <div className="space-y-2">
                  <select
                    value={sortCriteria.field}
                    onChange={(e) => onSortChange({
                      ...sortCriteria,
                      field: e.target.value as SortCriteria['field']
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="lastModified">Last Modified</option>
                    <option value="createdAt">Created Date</option>
                    <option value="name">Name</option>
                    <option value="progress">Progress</option>
                    <option value="priority">Priority</option>
                    <option value="status">Status</option>
                    <option value="type">Type</option>
                    <option value="complexity">Complexity</option>
                  </select>
                  <div className="flex gap-2">
                    <button
                      onClick={() => onSortChange({ ...sortCriteria, direction: 'asc' })}
                      className={`flex-1 px-3 py-2 text-sm rounded-lg transition-colors ${
                        sortCriteria.direction === 'asc'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      Ascending
                    </button>
                    <button
                      onClick={() => onSortChange({ ...sortCriteria, direction: 'desc' })}
                      className={`flex-1 px-3 py-2 text-sm rounded-lg transition-colors ${
                        sortCriteria.direction === 'desc'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      Descending
                    </button>
                  </div>
                </div>
              </div>

              {/* Filter Presets */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-white">Presets</h3>
                  <button
                    onClick={() => setShowSavePreset(true)}
                    className="flex items-center gap-1 px-2 py-1 text-xs text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    <Save className="w-3 h-3" />
                    Save
                  </button>
                </div>
                
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {presets.map(preset => (
                    <div
                      key={preset.id}
                      className="flex items-center gap-2 p-2 bg-gray-800/50 rounded-lg"
                    >
                      <button
                        onClick={() => onLoadPreset(preset)}
                        className="flex-1 text-left"
                      >
                        <div className="text-sm text-white font-medium">{preset.name}</div>
                        <div className="text-xs text-gray-400">{preset.description}</div>
                      </button>
                      {!preset.isDefault && (
                        <button
                          onClick={() => onDeletePreset(preset.id)}
                          className="p-1 text-gray-500 hover:text-red-400 transition-colors"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Popular Tags */}
              {popularTags.length > 0 && (
                <div className="space-y-3">
                  <h3 className="font-medium text-white flex items-center gap-2">
                    <Tag className="w-4 h-4" />
                    Popular Tags
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {popularTags.map(tag => (
                      <button
                        key={tag}
                        onClick={() => {
                          const newTags = criteria.tags.includes(tag)
                            ? criteria.tags.filter(t => t !== tag)
                            : [...criteria.tags, tag];
                          onCriteriaChange({ ...criteria, tags: newTags });
                        }}
                        className={`px-2 py-1 text-xs rounded-full transition-colors ${
                          criteria.tags.includes(tag)
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        }`}
                      >
                        {tag}
                        <span className="ml-1 text-xs opacity-70">
                          {stats.tagCounts[tag]}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Save Preset Modal */}
        <AnimatePresence>
          {showSavePreset && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black/50 flex items-center justify-center p-4"
              onClick={() => setShowSavePreset(false)}
            >
              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
                className="bg-gray-800 border border-gray-700 rounded-xl p-6 w-full max-w-md"
              >
                <h3 className="text-lg font-semibold text-white mb-4">Save Filter Preset</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm text-gray-400 mb-2">Name</label>
                    <input
                      type="text"
                      value={presetName}
                      onChange={(e) => setPresetName(e.target.value)}
                      placeholder="Enter preset name"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-400 mb-2">Description</label>
                    <textarea
                      value={presetDescription}
                      onChange={(e) => setPresetDescription(e.target.value)}
                      placeholder="Enter preset description"
                      rows={3}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="flex gap-3">
                    <button
                      onClick={() => setShowSavePreset(false)}
                      className="flex-1 px-4 py-2 text-gray-400 hover:text-white transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSavePreset}
                      disabled={!presetName.trim()}
                      className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      Save
                    </button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
};

export default FilterPanel;