import React, { useRef, useState, useCallback } from 'react';
import { motion, PanInfo, useAnimation } from 'framer-motion';
import { Trash2, Star, Archive, Edit, MoreHorizontal } from 'lucide-react';
import { cn } from '../../utils/helpers';
import { useTouchFeedback } from '../../hooks/useTouchFeedback';

interface SwipeAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  color: 'red' | 'blue' | 'green' | 'yellow' | 'purple' | 'gray';
  action: () => void;
}

interface SwipeableCardProps {
  children: React.ReactNode;
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  swipeThreshold?: number;
  disabled?: boolean;
  className?: string;
  contentClassName?: string;
}

const SwipeableCard: React.FC<SwipeableCardProps> = ({
  children,
  leftActions = [],
  rightActions = [],
  onSwipeLeft,
  onSwipeRight,
  swipeThreshold = 80,
  disabled = false,
  className,
  contentClassName
}) => {
  const [isRevealed, setIsRevealed] = useState<'left' | 'right' | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const controls = useAnimation();
  const cardRef = useRef<HTMLDivElement>(null);
  const { triggerHapticFeedback } = useTouchFeedback();

  const getActionColor = (color: SwipeAction['color']) => {
    const colors = {
      red: 'bg-red-500 text-white',
      blue: 'bg-blue-500 text-white',
      green: 'bg-green-500 text-white',
      yellow: 'bg-yellow-500 text-white',
      purple: 'bg-purple-500 text-white',
      gray: 'bg-gray-500 text-white'
    };
    return colors[color];
  };

  const handlePanStart = useCallback(() => {
    if (disabled) return;
    setIsDragging(true);
    triggerHapticFeedback('light');
  }, [disabled, triggerHapticFeedback]);

  const handlePan = useCallback((event: any, info: PanInfo) => {
    if (disabled) return;

    const { offset } = info;
    const maxSwipe = 120;
    const constrainedX = Math.max(-maxSwipe, Math.min(maxSwipe, offset.x));
    
    controls.set({ x: constrainedX });
  }, [disabled, controls]);

  const handlePanEnd = useCallback(async (event: any, info: PanInfo) => {
    if (disabled) return;

    setIsDragging(false);
    const { offset, velocity } = info;
    const swipeVelocityThreshold = 500;

    // Determine if swipe was significant enough
    const shouldRevealLeft = offset.x > swipeThreshold || (offset.x > 30 && velocity.x > swipeVelocityThreshold);
    const shouldRevealRight = offset.x < -swipeThreshold || (offset.x < -30 && velocity.x < -swipeVelocityThreshold);

    if (shouldRevealLeft && leftActions.length > 0) {
      // Reveal left actions
      setIsRevealed('left');
      await controls.start({ x: 80 });
      triggerHapticFeedback('medium');
      onSwipeRight?.();
    } else if (shouldRevealRight && rightActions.length > 0) {
      // Reveal right actions
      setIsRevealed('right');
      await controls.start({ x: -80 });
      triggerHapticFeedback('medium');
      onSwipeLeft?.();
    } else {
      // Snap back to center
      setIsRevealed(null);
      await controls.start({ x: 0 });
    }
  }, [disabled, swipeThreshold, leftActions.length, rightActions.length, controls, triggerHapticFeedback, onSwipeLeft, onSwipeRight]);

  const handleActionClick = useCallback((action: SwipeAction) => {
    triggerHapticFeedback('medium');
    action.action();
    
    // Reset card position after action
    setTimeout(() => {
      setIsRevealed(null);
      controls.start({ x: 0 });
    }, 150);
  }, [controls, triggerHapticFeedback]);

  const resetPosition = useCallback(() => {
    setIsRevealed(null);
    controls.start({ x: 0 });
  }, [controls]);

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {/* Left Actions */}
      {leftActions.length > 0 && (
        <div className="absolute left-0 top-0 h-full flex items-center">
          {leftActions.map((action, index) => (
            <motion.button
              key={action.id}
              className={cn(
                'h-full px-4 flex items-center justify-center min-w-[60px] touch-target',
                getActionColor(action.color)
              )}
              onClick={() => handleActionClick(action)}
              initial={{ x: -60 }}
              animate={{ 
                x: isRevealed === 'left' ? 0 : -60,
                opacity: isRevealed === 'left' ? 1 : 0
              }}
              transition={{ 
                duration: 0.2, 
                delay: index * 0.05,
                ease: 'easeOut'
              }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="flex flex-col items-center gap-1">
                <span className="text-lg">{action.icon}</span>
                <span className="text-xs font-medium">{action.label}</span>
              </div>
            </motion.button>
          ))}
        </div>
      )}

      {/* Right Actions */}
      {rightActions.length > 0 && (
        <div className="absolute right-0 top-0 h-full flex items-center">
          {rightActions.map((action, index) => (
            <motion.button
              key={action.id}
              className={cn(
                'h-full px-4 flex items-center justify-center min-w-[60px] touch-target',
                getActionColor(action.color)
              )}
              onClick={() => handleActionClick(action)}
              initial={{ x: 60 }}
              animate={{ 
                x: isRevealed === 'right' ? 0 : 60,
                opacity: isRevealed === 'right' ? 1 : 0
              }}
              transition={{ 
                duration: 0.2, 
                delay: index * 0.05,
                ease: 'easeOut'
              }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="flex flex-col items-center gap-1">
                <span className="text-lg">{action.icon}</span>
                <span className="text-xs font-medium">{action.label}</span>
              </div>
            </motion.button>
          ))}
        </div>
      )}

      {/* Main Card Content */}
      <motion.div
        ref={cardRef}
        className={cn(
          'relative bg-white border border-gray-200 rounded-xl shadow-sm',
          'touch-manipulation select-none',
          isDragging && 'shadow-lg',
          contentClassName
        )}
        animate={controls}
        onPanStart={handlePanStart}
        onPan={handlePan}
        onPanEnd={handlePanEnd}
        drag={disabled ? false : 'x'}
        dragConstraints={{ left: -120, right: 120 }}
        dragElastic={0.1}
        whileTap={{ scale: isDragging ? 1 : 0.98 }}
        transition={{ duration: 0.2, ease: 'easeOut' }}
      >
        {/* Backdrop click handler to reset position */}
        {isRevealed && (
          <div 
            className="absolute inset-0 z-10 bg-transparent"
            onClick={resetPosition}
          />
        )}
        
        {children}
      </motion.div>
    </div>
  );
};

// Predefined common actions
export const commonSwipeActions = {
  delete: (onDelete: () => void): SwipeAction => ({
    id: 'delete',
    label: 'Delete',
    icon: <Trash2 className="w-5 h-5" />,
    color: 'red',
    action: onDelete
  }),
  
  star: (onStar: () => void): SwipeAction => ({
    id: 'star',
    label: 'Star',
    icon: <Star className="w-5 h-5" />,
    color: 'yellow',
    action: onStar
  }),
  
  archive: (onArchive: () => void): SwipeAction => ({
    id: 'archive',
    label: 'Archive',
    icon: <Archive className="w-5 h-5" />,
    color: 'gray',
    action: onArchive
  }),
  
  edit: (onEdit: () => void): SwipeAction => ({
    id: 'edit',
    label: 'Edit',
    icon: <Edit className="w-5 h-5" />,
    color: 'blue',
    action: onEdit
  }),
  
  more: (onMore: () => void): SwipeAction => ({
    id: 'more',
    label: 'More',
    icon: <MoreHorizontal className="w-5 h-5" />,
    color: 'gray',
    action: onMore
  })
};

export default SwipeableCard;