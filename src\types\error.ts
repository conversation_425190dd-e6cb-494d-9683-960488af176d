export enum ErrorType {
  GENERATION_FAILED = 'GENERATION_FAILED',
  STORAGE_ERROR = 'STORAGE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  COMPONENT_ERROR = 'COMPONENT_ERROR',
  CRITICAL_ERROR = 'CRITICAL_ERROR'
}

export interface ErrorInfo {
  type: ErrorType;
  message: string;
  stack?: string;
  componentStack?: string;
  timestamp: string;
  userAgent?: string;
  url?: string;
}

export interface ErrorHandler {
  type: ErrorType;
  message: string;
  recovery?: () => void;
  retry?: () => void;
  canRecover: boolean;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}