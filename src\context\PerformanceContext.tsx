import React, { createContext, useContext, useMemo, useCallback } from 'react';
import { performanceMonitor } from '../utils/performanceMonitoring';

// Split contexts to minimize re-renders
interface PerformanceContextValue {
  measureRender: (componentName: string, renderFn: () => any) => any;
  measureAsync: (operationName: string, operation: () => Promise<any>) => Promise<any>;
  recordMetric: (name: string, value: number) => void;
  getMetrics: (name: string) => number[];
}

const PerformanceContext = createContext<PerformanceContextValue | null>(null);

export function PerformanceProvider({ children }: { children: React.ReactNode }) {
  const value = useMemo<PerformanceContextValue>(() => ({
    measureRender: performanceMonitor.measureRenderTime.bind(performanceMonitor),
    measureAsync: performanceMonitor.measureAsyncOperation.bind(performanceMonitor),
    recordMetric: performanceMonitor.recordMetric.bind(performanceMonitor),
    getMetrics: performanceMonitor.getMetrics.bind(performanceMonitor),
  }), []);

  return (
    <PerformanceContext.Provider value={value}>
      {children}
    </PerformanceContext.Provider>
  );
}

export function usePerformance() {
  const context = useContext(PerformanceContext);
  if (!context) {
    throw new Error('usePerformance must be used within a PerformanceProvider');
  }
  return context;
}

// Hook for measuring component renders
export function useMeasuredRender(componentName: string) {
  const { measureRender } = usePerformance();
  
  return useCallback((renderFn: () => any): any => {
    return measureRender(componentName, renderFn);
  }, [measureRender, componentName]);
}

// Hook for measuring async operations
export function useMeasuredAsync(operationName: string) {
  const { measureAsync } = usePerformance();
  
  return useCallback((operation: () => Promise<any>): Promise<any> => {
    return measureAsync(operationName, operation);
  }, [measureAsync, operationName]);
}

// Hook for recording custom metrics
export function useMetrics() {
  const { recordMetric, getMetrics } = usePerformance();
  
  return useMemo(() => ({
    record: recordMetric,
    get: getMetrics,
  }), [recordMetric, getMetrics]);
}