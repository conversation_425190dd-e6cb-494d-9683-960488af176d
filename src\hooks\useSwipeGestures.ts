import { useRef, useCallback, useEffect } from 'react';
import { useMemoryManager } from './useMemoryManager';

interface SwipeGestureOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number;
  preventScroll?: boolean;
  enabled?: boolean;
}

interface TouchPosition {
  x: number;
  y: number;
  time: number;
}

export const useSwipeGestures = (options: SwipeGestureOptions) => {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    threshold = 50,
    preventScroll = false,
    enabled = true
  } = options;

  const touchStart = useRef<TouchPosition | null>(null);
  const touchEnd = useRef<TouchPosition | null>(null);
  const elementRef = useRef<HTMLElement | null>(null);
  const { addEventListener } = useMemoryManager();

  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (!enabled) return;
    
    const touch = e.touches[0];
    touchStart.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };
    touchEnd.current = null;

    if (preventScroll) {
      e.preventDefault();
    }
  }, [enabled, preventScroll]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!enabled || !touchStart.current) return;

    const touch = e.touches[0];
    touchEnd.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };

    if (preventScroll) {
      e.preventDefault();
    }
  }, [enabled, preventScroll]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!enabled || !touchStart.current || !touchEnd.current) return;

    const deltaX = touchEnd.current.x - touchStart.current.x;
    const deltaY = touchEnd.current.y - touchStart.current.y;
    const deltaTime = touchEnd.current.time - touchStart.current.time;
    
    // Calculate velocity (pixels per millisecond)
    const velocity = Math.sqrt(deltaX * deltaX + deltaY * deltaY) / deltaTime;
    
    // Only trigger if swipe is fast enough and exceeds threshold
    if (velocity > 0.3 && Math.abs(deltaX) > threshold || Math.abs(deltaY) > threshold) {
      // Determine primary direction
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        if (deltaX > 0 && onSwipeRight) {
          onSwipeRight();
        } else if (deltaX < 0 && onSwipeLeft) {
          onSwipeLeft();
        }
      } else {
        // Vertical swipe
        if (deltaY > 0 && onSwipeDown) {
          onSwipeDown();
        } else if (deltaY < 0 && onSwipeUp) {
          onSwipeUp();
        }
      }
    }

    // Reset
    touchStart.current = null;
    touchEnd.current = null;

    if (preventScroll) {
      e.preventDefault();
    }
  }, [enabled, threshold, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, preventScroll]);

  const attachToElement = useCallback((element: HTMLElement | null) => {
    // Clean up previous element
    if (elementRef.current) {
      // Cleanup is handled by memory manager
    }

    elementRef.current = element;

    if (element && enabled) {
      addEventListener(element, 'touchstart', handleTouchStart, { passive: !preventScroll });
      addEventListener(element, 'touchmove', handleTouchMove, { passive: !preventScroll });
      addEventListener(element, 'touchend', handleTouchEnd, { passive: !preventScroll });
    }
  }, [enabled, handleTouchStart, handleTouchMove, handleTouchEnd, preventScroll, addEventListener]);

  return { attachToElement };
};