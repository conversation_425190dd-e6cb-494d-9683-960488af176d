// API and generation-related type definitions

import { ProjectType, Complexity, Feature, TechStack, MarketAnalysis } from './project';

export type Platform = 'web' | 'mobile' | 'desktop' | 'api';

export interface GenerationOptions {
  includeMarketAnalysis: boolean;
  includeTechStack: boolean;
  includeFeatureBreakdown: boolean;
  customPrompts?: string[];
}

export interface AppGenerationRequest {
  description: string;
  type: ProjectType;
  complexity: Complexity;
  features: string[];
  targetPlatform: Platform[];
  customizations?: GenerationOptions;
}

export interface GeneratedApp {
  id: string;
  name: string;
  description: string;
  features: Feature[];
  techStack: TechStack;
  category: string;
  complexity: Complexity;
  estimatedTime: string;
  marketAnalysis: MarketAnalysis;
  createdAt: string;
}

export interface AnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
  stagger?: number;
}

export enum ErrorType {
  GENERATION_FAILED = 'GENERATION_FAILED',
  STORAGE_ERROR = 'STORAGE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR'
}

export interface ErrorHandler {
  type: ErrorType;
  message: string;
  recovery?: () => void;
  retry?: () => void;
}