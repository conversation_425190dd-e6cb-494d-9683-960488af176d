import { useState, useEffect } from 'react';

export interface BreakpointState {
  isXs: boolean;
  isSm: boolean;
  isMd: boolean;
  isLg: boolean;
  isXl: boolean;
  is2Xl: boolean;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

export interface ResponsiveLayoutState extends BreakpointState {
  width: number;
  height: number;
  orientation: 'portrait' | 'landscape';
  sidebarCollapsed: boolean;
  showMobileMenu: boolean;
}

const breakpoints = {
  xs: 475,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

export const useResponsiveLayout = () => {
  const [layoutState, setLayoutState] = useState<ResponsiveLayoutState>(() => {
    const width = typeof window !== 'undefined' ? window.innerWidth : 1024;
    const height = typeof window !== 'undefined' ? window.innerHeight : 768;
    
    return {
      width,
      height,
      isXs: width >= breakpoints.xs,
      isSm: width >= breakpoints.sm,
      isMd: width >= breakpoints.md,
      isLg: width >= breakpoints.lg,
      isXl: width >= breakpoints.xl,
      is2Xl: width >= breakpoints['2xl'],
      isMobile: width < breakpoints.md,
      isTablet: width >= breakpoints.md && width < breakpoints.lg,
      isDesktop: width >= breakpoints.lg,
      orientation: width > height ? 'landscape' : 'portrait',
      sidebarCollapsed: width < breakpoints.lg,
      showMobileMenu: false,
    };
  });

  useEffect(() => {
    const updateLayoutState = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setLayoutState(prev => ({
        ...prev,
        width,
        height,
        isXs: width >= breakpoints.xs,
        isSm: width >= breakpoints.sm,
        isMd: width >= breakpoints.md,
        isLg: width >= breakpoints.lg,
        isXl: width >= breakpoints.xl,
        is2Xl: width >= breakpoints['2xl'],
        isMobile: width < breakpoints.md,
        isTablet: width >= breakpoints.md && width < breakpoints.lg,
        isDesktop: width >= breakpoints.lg,
        orientation: width > height ? 'landscape' : 'portrait',
        sidebarCollapsed: width < breakpoints.lg ? true : prev.sidebarCollapsed,
        showMobileMenu: width >= breakpoints.lg ? false : prev.showMobileMenu,
      }));
    };

    // Debounce resize events for better performance
    let timeoutId: NodeJS.Timeout;
    const debouncedUpdate = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateLayoutState, 150);
    };

    window.addEventListener('resize', debouncedUpdate);
    window.addEventListener('orientationchange', debouncedUpdate);

    return () => {
      window.removeEventListener('resize', debouncedUpdate);
      window.removeEventListener('orientationchange', debouncedUpdate);
      clearTimeout(timeoutId);
    };
  }, []);

  const toggleSidebar = () => {
    setLayoutState(prev => ({
      ...prev,
      sidebarCollapsed: !prev.sidebarCollapsed,
    }));
  };

  const toggleMobileMenu = () => {
    setLayoutState(prev => ({
      ...prev,
      showMobileMenu: !prev.showMobileMenu,
    }));
  };

  const closeMobileMenu = () => {
    setLayoutState(prev => ({
      ...prev,
      showMobileMenu: false,
    }));
  };

  return {
    ...layoutState,
    toggleSidebar,
    toggleMobileMenu,
    closeMobileMenu,
  };
};

export default useResponsiveLayout;