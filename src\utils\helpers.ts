// Utility helper functions

import { Project, SortOption, FilterOption } from '../types';

/**
 * Combine class names conditionally
 */
export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

/**
 * Format a date string to a relative time format
 */
export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} min ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hour${Math.floor(diffInSeconds / 3600) > 1 ? 's' : ''} ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} day${Math.floor(diffInSeconds / 86400) > 1 ? 's' : ''} ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 604800)} week${Math.floor(diffInSeconds / 604800) > 1 ? 's' : ''} ago`;
  
  return date.toLocaleDateString();
};

/**
 * Generate a unique ID
 */
export const generateId = (): string => {
  return Math.random().toString(36).substring(2, 9) + Date.now().toString(36);
};

/**
 * Debounce function for search inputs
 */
export const debounce = <T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Filter and sort projects based on criteria
 */
export const filterAndSortProjects = (
  projects: Project[],
  searchQuery: string,
  filterBy: FilterOption,
  sortBy: SortOption
): Project[] => {
  const filtered = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterBy === 'all' || project.status === filterBy;
    return matchesSearch && matchesFilter;
  });

  return filtered.sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'date':
        return new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime();
      case 'status':
        return a.status.localeCompare(b.status);
      case 'progress':
        return b.progress - a.progress;
      default:
        return 0;
    }
  });
};

/**
 * Get status-specific styling classes
 */
export const getStatusStyles = (status: Project['status']) => {
  const styles = {
    active: {
      dot: 'bg-emerald-400 shadow-emerald-400/50',
      badge: 'bg-emerald-500/20 text-emerald-300 border-emerald-500/30',
      text: 'text-emerald-400'
    },
    completed: {
      dot: 'bg-blue-400 shadow-blue-400/50',
      badge: 'bg-blue-500/20 text-blue-300 border-blue-500/30',
      text: 'text-blue-400'
    },
    draft: {
      dot: 'bg-amber-400 shadow-amber-400/50',
      badge: 'bg-amber-500/20 text-amber-300 border-amber-500/30',
      text: 'text-amber-400'
    },
    archived: {
      dot: 'bg-gray-400 shadow-gray-400/50',
      badge: 'bg-gray-500/20 text-gray-300 border-gray-500/30',
      text: 'text-gray-400'
    },
    error: {
      dot: 'bg-red-400 shadow-red-400/50',
      badge: 'bg-red-500/20 text-red-300 border-red-500/30',
      text: 'text-red-400'
    }
  };

  return styles[status] || styles.draft;
};

/**
 * Get priority-specific styling classes
 */
export const getPriorityStyles = (priority: Project['priority']) => {
  const styles = {
    high: 'text-red-400',
    medium: 'text-yellow-400',
    low: 'text-green-400'
  };

  return styles[priority] || styles.medium;
};

/**
 * Truncate text to specified length
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Calculate project completion percentage
 */
export const calculateProgress = (project: Project): number => {
  // This could be enhanced with more sophisticated logic
  switch (project.status) {
    case 'completed':
      return 100;
    case 'active':
      return project.progress || 50;
    case 'draft':
      return 25;
    case 'archived':
      return 100;
    case 'error':
      return project.progress || 0;
    default:
      return 0;
  }
};

/**
 * Get project type icon name
 */
export const getProjectTypeIcon = (type: Project['type']): string => {
  const icons = {
    mobile: 'Smartphone',
    web: 'Globe',
    form: 'FileText',
    ai: 'Zap',
    ecommerce: 'ShoppingCart'
  };

  return icons[type] || 'FileText';
};

/**
 * Format date to readable string
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  // If less than a day, show relative time
  if (diffInSeconds < 86400) {
    return formatRelativeTime(dateString);
  }

  // Otherwise show formatted date
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
  });
};