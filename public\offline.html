<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>You're Offline - App Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .offline-container {
            max-width: 400px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
        }
        
        h1 {
            font-size: 24px;
            margin-bottom: 16px;
            font-weight: 600;
        }
        
        p {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 24px;
            opacity: 0.9;
        }
        
        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .features {
            margin-top: 30px;
            text-align: left;
        }
        
        .features h3 {
            font-size: 18px;
            margin-bottom: 12px;
            text-align: center;
        }
        
        .features ul {
            list-style: none;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4ade80;
            font-weight: bold;
        }
        
        @media (max-width: 480px) {
            .offline-container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 20px;
            }
            
            p {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            📱
        </div>
        
        <h1>You're Offline</h1>
        
        <p>
            Don't worry! The App Generator works offline too. 
            Your projects are saved locally and will sync when you're back online.
        </p>
        
        <a href="/" class="retry-button" onclick="window.location.reload()">
            Try Again
        </a>
        
        <div class="features">
            <h3>Available Offline:</h3>
            <ul>
                <li>View saved projects</li>
                <li>Create new app concepts</li>
                <li>Edit project details</li>
                <li>Export project data</li>
            </ul>
        </div>
    </div>

    <script>
        // Check for network connectivity
        function checkConnection() {
            if (navigator.onLine) {
                window.location.href = '/';
            }
        }
        
        // Check connection every 5 seconds
        setInterval(checkConnection, 5000);
        
        // Listen for online event
        window.addEventListener('online', () => {
            window.location.href = '/';
        });
        
        // Add touch feedback for mobile
        document.querySelector('.retry-button').addEventListener('touchstart', function() {
            this.style.transform = 'translateY(-2px) scale(0.95)';
        });
        
        document.querySelector('.retry-button').addEventListener('touchend', function() {
            this.style.transform = 'translateY(-2px)';
        });
    </script>
</body>
</html>