import React from 'react';
import { Link } from 'react-router-dom';
import { Star, Archive, Trash2, MoreHorizontal } from 'lucide-react';
import { Project } from '../../types';
import { getStatusStyles, getPriorityStyles, truncateText } from '../../utils/helpers';
import Badge from '../ui/Badge';

interface ProjectItemProps {
  project: Project;
  isHovered: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onAction: (projectId: string, action: 'star' | 'archive' | 'delete') => void;
}

const ProjectItem: React.FC<ProjectItemProps> = ({
  project,
  isHovered,
  onMouseEnter,
  onMouseLeave,
  onAction
}) => {
  const statusStyles = getStatusStyles(project.status);
  const priorityColor = getPriorityStyles(project.priority);

  const ProjectContent = () => (
    <div className="flex items-start gap-3">
      <div className="relative">
        <div className="p-2 bg-gray-800/50 rounded-lg group-hover:bg-gray-700/50 transition-all duration-200">
          <div className="w-4 h-4 text-gray-400 group-hover:text-white transition-all duration-200 bg-current rounded-sm" />
        </div>
        <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full shadow-lg ${statusStyles.dot} transition-all duration-200 ${isHovered ? 'scale-125' : ''}`} />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between mb-1">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <span className="text-sm font-medium leading-tight block group-hover:text-white transition-colors truncate">
              {project.name}
            </span>
            {project.isStarred && (
              <Star className="w-3 h-3 text-yellow-400 fill-current flex-shrink-0" />
            )}
          </div>
          
          <div className="flex items-center gap-1 ml-2">
            <div className={`w-2 h-2 rounded-full ${priorityColor.replace('text-', 'bg-')} opacity-60`} />
            {isHovered && (
              <div className="flex items-center gap-1">
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onAction(project.id, 'star');
                  }}
                  className="p-1 hover:bg-gray-600/50 rounded transition-colors"
                  aria-label="Toggle star"
                >
                  <Star className="w-3 h-3 text-gray-400 hover:text-yellow-400" />
                </button>
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onAction(project.id, 'archive');
                  }}
                  className="p-1 hover:bg-gray-600/50 rounded transition-colors"
                  aria-label="Archive project"
                >
                  <Archive className="w-3 h-3 text-gray-400 hover:text-blue-400" />
                </button>
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onAction(project.id, 'delete');
                  }}
                  className="p-1 hover:bg-gray-600/50 rounded transition-colors"
                  aria-label="Delete project"
                >
                  <Trash2 className="w-3 h-3 text-gray-400 hover:text-red-400" />
                </button>
              </div>
            )}
          </div>
        </div>
        
        <p className="text-xs text-gray-500 leading-relaxed mb-2 line-clamp-2">
          {truncateText(project.description, 80)}
        </p>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant={project.status === 'active' ? 'success' : project.status === 'completed' ? 'info' : project.status === 'error' ? 'error' : 'warning'} size="sm">
              {project.status}
            </Badge>
            {project.progress > 0 && (
              <div className="flex items-center gap-1">
                <div className="w-12 h-1 bg-gray-700 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-blue-400 rounded-full transition-all duration-300"
                    style={{ width: `${project.progress}%` }}
                  />
                </div>
                <span className="text-xs text-gray-500">{project.progress}%</span>
              </div>
            )}
          </div>
          
          <span className="text-xs text-gray-500">{project.lastModified}</span>
        </div>

        {project.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {project.tags.slice(0, 2).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-0.5 bg-gray-700/30 text-gray-400 rounded text-xs"
              >
                {tag}
              </span>
            ))}
            {project.tags.length > 2 && (
              <span className="px-2 py-0.5 bg-gray-700/30 text-gray-400 rounded text-xs">
                +{project.tags.length - 2}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div
      role="listitem"
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      className="group relative p-3 text-gray-300 hover:bg-gray-800/50 rounded-xl cursor-pointer transition-all duration-300 border border-transparent hover:border-gray-700/50"
    >
      {project.route ? (
        <Link to={project.route} className="block">
          <ProjectContent />
        </Link>
      ) : (
        <ProjectContent />
      )}
      
      {/* Hover Glow Effect */}
      {isHovered && (
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-500/20 transition-all duration-300" />
      )}
    </div>
  );
};

export default ProjectItem;