import React, { Suspense, ComponentType, ReactNode } from 'react';
import { LoadingSpinner } from './LoadingSpinner';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import Button from './Button';

interface LazyWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
  errorFallback?: ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error) => void;
}

interface LazyErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class LazyErrorBoundary extends React.Component<
  LazyWrapperProps & { onRetry: () => void },
  LazyErrorBoundaryState
> {
  constructor(props: LazyWrapperProps & { onRetry: () => void }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): LazyErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error) {
    this.props.onError?.(error);
    console.error('Lazy loading error:', error);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
    this.props.onRetry();
  };

  render() {
    if (this.state.hasError) {
      const ErrorComponent = this.props.errorFallback;
      
      if (ErrorComponent && this.state.error) {
        return <ErrorComponent error={this.state.error} retry={this.handleRetry} />;
      }

      return (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="w-6 h-6 text-red-400" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">
            Failed to load component
          </h3>
          <p className="text-gray-400 mb-4">
            There was an error loading this part of the application.
          </p>
          <Button
            variant="secondary"
            size="sm"
            onClick={this.handleRetry}
            icon={RefreshCw}
          >
            Try Again
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

export function LazyWrapper({
  children,
  fallback,
  errorFallback,
  onError,
}: LazyWrapperProps) {
  const [retryKey, setRetryKey] = React.useState(0);

  const handleRetry = React.useCallback(() => {
    setRetryKey(prev => prev + 1);
  }, []);

  const defaultFallback = (
    <div className="flex items-center justify-center p-8">
      <LoadingSpinner size="md" />
    </div>
  );

  return (
    <LazyErrorBoundary
      key={retryKey}
      errorFallback={errorFallback}
      onError={onError}
      onRetry={handleRetry}
    >
      <Suspense fallback={fallback || defaultFallback}>
        {children}
      </Suspense>
    </LazyErrorBoundary>
  );
}

// Higher-order component for lazy loading
export function withLazyLoading<P extends object>(
  Component: ComponentType<P>,
  options: {
    fallback?: ReactNode;
    errorFallback?: ComponentType<{ error: Error; retry: () => void }>;
    onError?: (error: Error) => void;
  } = {}
) {
  const LazyComponent = React.forwardRef<any, P>((props, ref) => (
    <LazyWrapper {...options}>
      <Component {...props} ref={ref} />
    </LazyWrapper>
  ));

  LazyComponent.displayName = `withLazyLoading(${Component.displayName || Component.name})`;

  return LazyComponent;
}

// Component for progressive image loading
interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  placeholder?: string;
  className?: string;
  onLoad?: () => void;
  onError?: () => void;
}

export function LazyImage({
  src,
  alt,
  placeholder,
  className = '',
  onLoad,
  onError,
  ...props
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);
  const [isInView, setIsInView] = React.useState(false);
  const imgRef = React.useRef<HTMLImageElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  return (
    <div ref={imgRef} className={`relative overflow-hidden ${className}`}>
      {/* Placeholder */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 bg-gray-800 animate-pulse flex items-center justify-center">
          {placeholder ? (
            <img src={placeholder} alt="" className="opacity-50" />
          ) : (
            <div className="w-8 h-8 bg-gray-700 rounded" />
          )}
        </div>
      )}
      
      {/* Actual image */}
      {isInView && !hasError && (
        <img
          src={src}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          className={`transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          {...props}
        />
      )}
      
      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 bg-gray-800 flex items-center justify-center">
          <AlertTriangle className="w-6 h-6 text-gray-500" />
        </div>
      )}
    </div>
  );
}