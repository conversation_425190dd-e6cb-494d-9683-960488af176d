import React, { Suspense, lazy, ComponentType } from 'react';
import { LoadingSpinner } from './LoadingSpinner';

interface LazyComponentProps {
  fallback?: React.ReactNode;
  className?: string;
}

export function createLazyComponent<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ReactNode
) {
  const LazyComp = lazy(importFunc);
  
  return React.forwardRef<any, React.ComponentProps<T> & LazyComponentProps>(
    (props, ref) => {
      const { fallback: propFallback, className, ...restProps } = props;
      
      return (
        <Suspense 
          fallback={
            propFallback || fallback || (
              <div className={`flex items-center justify-center p-4 ${className || ''}`}>
                <LoadingSpinner size="sm" />
              </div>
            )
          }
        >
          <LazyComp {...restProps} ref={ref} />
        </Suspense>
      );
    }
  );
}

// Hook for lazy loading with intersection observer
export function useLazyLoad(threshold = 0.1, rootMargin = '50px') {
  const [isVisible, setIsVisible] = React.useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold, rootMargin }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold, rootMargin]);

  return { ref, isVisible };
}