import { useEffect, useCallback, useRef } from 'react';
import { useApp } from '../context/AppContext';
import { Project, User } from '../types';

interface SyncMessage {
  type: 'STATE_UPDATE' | 'PROJECT_UPDATE' | 'USER_UPDATE' | 'FULL_SYNC_REQUEST' | 'FULL_SYNC_RESPONSE';
  payload: any;
  timestamp: number;
  tabId: string;
}

interface SyncState {
  projects: Project[];
  user: User | null;
  lastSyncTime: string;
}

/**
 * Custom hook for cross-tab state synchronization using localStorage events
 */
export function useCrossTabSync() {
  const {
    projects,
    user,
    lastSyncTime,
    updateProject,
    addProject,
    deleteProject,
    setUser,
    setLastSyncTime,
    setSyncStatus,
    setError
  } = useApp();

  const tabIdRef = useRef<string>(generateTabId());
  const lastBroadcastRef = useRef<number>(0);
  const syncKeyRef = useRef<string>('app-cross-tab-sync');

  // Generate unique tab ID
  function generateTabId(): string {
    return `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Broadcast message to other tabs
  const broadcastMessage = useCallback((message: Omit<SyncMessage, 'timestamp' | 'tabId'>) => {
    const fullMessage: SyncMessage = {
      ...message,
      timestamp: Date.now(),
      tabId: tabIdRef.current
    };

    try {
      localStorage.setItem(syncKeyRef.current, JSON.stringify(fullMessage));
      // Remove the item immediately to trigger storage event in other tabs
      localStorage.removeItem(syncKeyRef.current);
      lastBroadcastRef.current = fullMessage.timestamp;
    } catch (error) {
      console.error('Failed to broadcast message:', error);
      setError('Failed to sync with other tabs');
    }
  }, [setError]);

  // Handle incoming sync messages
  const handleSyncMessage = useCallback((message: SyncMessage) => {
    // Ignore messages from the same tab
    if (message.tabId === tabIdRef.current) {
      return;
    }

    // Ignore old messages
    if (message.timestamp <= lastBroadcastRef.current) {
      return;
    }

    try {
      setSyncStatus('syncing');

      switch (message.type) {
        case 'PROJECT_UPDATE':
          const { action, project, projectId, updates } = message.payload;
          
          switch (action) {
            case 'ADD':
              // Check if project already exists to avoid duplicates
              const existingProject = projects.find(p => p.id === project.id);
              if (!existingProject) {
                addProject(project);
              }
              break;
              
            case 'UPDATE':
              updateProject(projectId, updates);
              break;
              
            case 'DELETE':
              deleteProject(projectId);
              break;
          }
          break;

        case 'USER_UPDATE':
          setUser(message.payload);
          break;

        case 'FULL_SYNC_REQUEST':
          // Respond with current state
          broadcastMessage({
            type: 'FULL_SYNC_RESPONSE',
            payload: {
              projects,
              user,
              lastSyncTime
            }
          });
          break;

        case 'FULL_SYNC_RESPONSE':
          // Handle full state sync (conflict resolution)
          handleFullStateSync(message.payload);
          break;

        case 'STATE_UPDATE':
          // Handle general state updates
          handleStateUpdate(message.payload);
          break;
      }

      setLastSyncTime(new Date().toISOString());
      setSyncStatus('idle');
    } catch (error) {
      console.error('Failed to handle sync message:', error);
      setError('Failed to sync state from other tabs');
      setSyncStatus('error');
    }
  }, [projects, user, lastSyncTime, updateProject, addProject, deleteProject, setUser, setLastSyncTime, setSyncStatus, setError, broadcastMessage]);

  // Handle full state synchronization with conflict resolution
  const handleFullStateSync = useCallback((incomingState: SyncState) => {
    try {
      // Simple conflict resolution: use the most recent lastSyncTime
      const incomingTime = new Date(incomingState.lastSyncTime).getTime();
      const currentTime = lastSyncTime ? new Date(lastSyncTime).getTime() : 0;

      if (incomingTime > currentTime) {
        // Incoming state is newer, merge it
        mergeState(incomingState);
      }
    } catch (error) {
      console.error('Failed to handle full state sync:', error);
      setError('Failed to resolve state conflicts');
    }
  }, [lastSyncTime, setError]);

  // Merge incoming state with current state
  const mergeState = useCallback((incomingState: SyncState) => {
    try {
      // Merge projects (keep local changes, add new remote projects)
      const mergedProjects = [...projects];
      
      incomingState.projects.forEach(incomingProject => {
        const existingIndex = mergedProjects.findIndex(p => p.id === incomingProject.id);
        
        if (existingIndex >= 0) {
          // Project exists, check which is newer
          const existing = mergedProjects[existingIndex];
          const existingTime = new Date(existing.lastModified).getTime();
          const incomingTime = new Date(incomingProject.lastModified).getTime();
          
          if (incomingTime > existingTime) {
            // Incoming project is newer, update it
            updateProject(incomingProject.id, incomingProject);
          }
        } else {
          // New project, add it
          addProject(incomingProject);
        }
      });

      // Update user if incoming user is different
      if (incomingState.user && JSON.stringify(incomingState.user) !== JSON.stringify(user)) {
        setUser(incomingState.user);
      }

      setLastSyncTime(incomingState.lastSyncTime);
    } catch (error) {
      console.error('Failed to merge state:', error);
      setError('Failed to merge state from other tabs');
    }
  }, [projects, user, updateProject, addProject, setUser, setLastSyncTime, setError]);

  // Handle general state updates
  const handleStateUpdate = useCallback((payload: any) => {
    // Handle specific state updates that don't fit other categories
    console.log('Received state update:', payload);
  }, []);

  // Validate and sanitize incoming data
  const validateSyncMessage = useCallback((message: any): message is SyncMessage => {
    return (
      message &&
      typeof message === 'object' &&
      typeof message.type === 'string' &&
      typeof message.timestamp === 'number' &&
      typeof message.tabId === 'string' &&
      message.payload !== undefined
    );
  }, []);

  // Handle corrupted data gracefully
  const handleCorruptedData = useCallback((error: Error, data: any) => {
    console.error('Corrupted sync data detected:', error, data);
    
    // Try to recover by requesting full sync from other tabs
    broadcastMessage({
      type: 'FULL_SYNC_REQUEST',
      payload: null
    });
    
    setError('Detected corrupted sync data, attempting recovery');
  }, [broadcastMessage, setError]);

  // Listen for storage events (cross-tab communication)
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key !== syncKeyRef.current || !event.newValue) {
        return;
      }

      try {
        const message = JSON.parse(event.newValue);
        
        if (!validateSyncMessage(message)) {
          throw new Error('Invalid sync message format');
        }

        handleSyncMessage(message);
      } catch (error) {
        handleCorruptedData(error as Error, event.newValue);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [handleSyncMessage, validateSyncMessage, handleCorruptedData]);

  // Broadcast project changes
  const syncProjectUpdate = useCallback((action: 'ADD' | 'UPDATE' | 'DELETE', project?: Project, projectId?: string, updates?: Partial<Project>) => {
    broadcastMessage({
      type: 'PROJECT_UPDATE',
      payload: { action, project, projectId, updates }
    });
  }, [broadcastMessage]);

  // Broadcast user changes
  const syncUserUpdate = useCallback((user: User | null) => {
    broadcastMessage({
      type: 'USER_UPDATE',
      payload: user
    });
  }, [broadcastMessage]);

  // Request full sync from other tabs
  const requestFullSync = useCallback(() => {
    broadcastMessage({
      type: 'FULL_SYNC_REQUEST',
      payload: null
    });
  }, [broadcastMessage]);

  // Handle page visibility change (sync when tab becomes visible)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Tab became visible, request sync to get latest state
        requestFullSync();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [requestFullSync]);

  // Handle beforeunload (cleanup)
  useEffect(() => {
    const handleBeforeUnload = () => {
      // Clean up any pending sync operations
      setSyncStatus('idle');
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [setSyncStatus]);

  return {
    tabId: tabIdRef.current,
    syncProjectUpdate,
    syncUserUpdate,
    requestFullSync,
    broadcastMessage
  };
}

/**
 * Hook to automatically sync project operations
 */
export function useAutoSync() {
  const { syncProjectUpdate, syncUserUpdate } = useCrossTabSync();
  const { projects, user } = useApp();
  const previousProjectsRef = useRef<Project[]>([]);
  const previousUserRef = useRef<User | null>(null);

  // Detect project changes and sync them
  useEffect(() => {
    const previousProjects = previousProjectsRef.current;
    
    if (previousProjects.length === 0 && projects.length > 0) {
      // Initial load, don't sync
      previousProjectsRef.current = projects;
      return;
    }

    // Detect added projects
    projects.forEach(project => {
      const wasPresent = previousProjects.find(p => p.id === project.id);
      if (!wasPresent) {
        syncProjectUpdate('ADD', project);
      }
    });

    // Detect updated projects
    projects.forEach(project => {
      const previous = previousProjects.find(p => p.id === project.id);
      if (previous && previous.lastModified !== project.lastModified) {
        const updates = getProjectDifferences(previous, project);
        if (Object.keys(updates).length > 0) {
          syncProjectUpdate('UPDATE', undefined, project.id, updates);
        }
      }
    });

    // Detect deleted projects
    previousProjects.forEach(previousProject => {
      const stillExists = projects.find(p => p.id === previousProject.id);
      if (!stillExists) {
        syncProjectUpdate('DELETE', undefined, previousProject.id);
      }
    });

    previousProjectsRef.current = projects;
  }, [projects, syncProjectUpdate]);

  // Detect user changes and sync them
  useEffect(() => {
    const previousUser = previousUserRef.current;
    
    if (JSON.stringify(previousUser) !== JSON.stringify(user)) {
      if (previousUser !== null) { // Don't sync initial load
        syncUserUpdate(user);
      }
      previousUserRef.current = user;
    }
  }, [user, syncUserUpdate]);
}

// Helper function to get differences between two projects
function getProjectDifferences(oldProject: Project, newProject: Project): Partial<Project> {
  const differences: Partial<Project> = {};
  
  (Object.keys(newProject) as (keyof Project)[]).forEach(key => {
    if (JSON.stringify(oldProject[key]) !== JSON.stringify(newProject[key])) {
      differences[key] = newProject[key];
    }
  });
  
  return differences;
}