import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '../../test/utils';
import { mockProject } from '../../test/utils';

// Mock the performance monitoring to avoid issues in tests
vi.mock('../../utils/performanceMonitoring', () => ({
  measureComponentRender: () => (Component: any) => Component,
}));

// Import after mocking
const ProjectCard = await import('../ProjectCard').then(m => m.default);

describe('ProjectCard Component', () => {
  const defaultProps = {
    project: mockProject,
    onClick: vi.fn(),
    onAction: vi.fn(),
    variant: 'default' as const,
    showActions: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders project information correctly', () => {
    render(<ProjectCard {...defaultProps} />);
    
    expect(screen.getByText(mockProject.name)).toBeInTheDocument();
    expect(screen.getByText(mockProject.description)).toBeInTheDocument();
    expect(screen.getByText(`${mockProject.progress}%`)).toBeInTheDocument();
  });

  it('shows starred status when project is starred', () => {
    render(<ProjectCard {...defaultProps} />);
    
    const starIcon = screen.getByTestId('star-icon');
    expect(starIcon).toBeInTheDocument();
  });

  it('does not show star icon when project is not starred', () => {
    const unstarredProject = { ...mockProject, isStarred: false };
    render(<ProjectCard {...defaultProps} project={unstarredProject} />);
    
    expect(screen.queryByTestId('star-icon')).not.toBeInTheDocument();
  });

  it('displays project tags', () => {
    render(<ProjectCard {...defaultProps} />);
    
    mockProject.tags.forEach(tag => {
      expect(screen.getByText(tag)).toBeInTheDocument();
    });
  });

  it('handles click events', () => {
    render(<ProjectCard {...defaultProps} />);
    
    const card = screen.getByRole('listitem');
    fireEvent.click(card);
    
    expect(defaultProps.onClick).toHaveBeenCalledWith(mockProject);
  });

  it('handles star action', () => {
    render(<ProjectCard {...defaultProps} />);
    
    const starButton = screen.getByLabelText(/toggle star/i);
    fireEvent.click(starButton);
    
    expect(defaultProps.onAction).toHaveBeenCalledWith(
      mockProject.id,
      'star'
    );
  });

  it('handles archive action', () => {
    render(<ProjectCard {...defaultProps} />);
    
    const archiveButton = screen.getByLabelText(/archive project/i);
    fireEvent.click(archiveButton);
    
    expect(defaultProps.onAction).toHaveBeenCalledWith(
      mockProject.id,
      'archive'
    );
  });

  it('handles delete action', () => {
    render(<ProjectCard {...defaultProps} />);
    
    const deleteButton = screen.getByLabelText(/delete project/i);
    fireEvent.click(deleteButton);
    
    expect(defaultProps.onAction).toHaveBeenCalledWith(
      mockProject.id,
      'delete'
    );
  });

  it('renders compact variant correctly', () => {
    render(<ProjectCard {...defaultProps} variant="compact" />);
    
    // Compact variant should still show project name
    expect(screen.getByText(mockProject.name)).toBeInTheDocument();
  });

  it('shows correct status badge', () => {
    render(<ProjectCard {...defaultProps} />);
    
    const statusBadge = screen.getByText(mockProject.status);
    expect(statusBadge).toBeInTheDocument();
  });

  it('shows progress bar', () => {
    render(<ProjectCard {...defaultProps} />);
    
    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toBeInTheDocument();
    expect(progressBar).toHaveAttribute('aria-valuenow', mockProject.progress.toString());
  });

  it('handles hover states', () => {
    render(<ProjectCard {...defaultProps} />);
    
    const card = screen.getByRole('listitem');
    fireEvent.mouseEnter(card);
    
    // Should trigger hover state (tested through class changes or visual feedback)
    expect(card).toBeInTheDocument();
    
    fireEvent.mouseLeave(card);
  });
});
