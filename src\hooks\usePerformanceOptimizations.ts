import { useCallback, useMemo, useRef, useEffect } from 'react';
import { performanceMonitor } from '../utils/performanceMonitoring';

// Hook for debounced callbacks to prevent excessive re-renders
export function useDebounceCallback<T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const callbackRef = useRef(callback);
  
  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  return useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callbackRef.current(...args);
    }, delay);
  }, [delay]) as T;
}

// Hook for throttled callbacks
export function useThrottleCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCallRef = useRef<number>(0);
  const callbackRef = useRef(callback);
  
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  return useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCallRef.current >= delay) {
      lastCallRef.current = now;
      callbackRef.current(...args);
    }
  }, [delay]) as T;
}

// Hook for memoizing expensive computations with custom equality
export function useDeepMemo<T>(
  factory: () => T,
  deps: React.DependencyList,
  isEqual?: (a: T, b: T) => boolean
): T {
  const ref = useRef<{ deps: React.DependencyList; value: T }>();
  
  const hasChanged = !ref.current || 
    deps.length !== ref.current.deps.length ||
    deps.some((dep, index) => dep !== ref.current!.deps[index]);
  
  if (hasChanged) {
    const newValue = factory();
    
    // Use custom equality check if provided
    if (ref.current && isEqual && !isEqual(newValue, ref.current.value)) {
      ref.current = { deps, value: newValue };
    } else if (!ref.current || !isEqual) {
      ref.current = { deps, value: newValue };
    }
  }
  
  return ref.current!.value;
}

// Hook for stable references to prevent unnecessary re-renders
export function useStableCallback<T extends (...args: any[]) => any>(callback: T): T {
  const callbackRef = useRef(callback);
  
  useEffect(() => {
    callbackRef.current = callback;
  });
  
  return useCallback((...args: Parameters<T>) => {
    return callbackRef.current(...args);
  }, []) as T;
}

// Hook for measuring component render performance
export function useRenderPerformance(componentName: string) {
  const renderCountRef = useRef(0);
  const lastRenderTimeRef = useRef(0);
  
  useEffect(() => {
    renderCountRef.current++;
    const now = performance.now();
    
    if (lastRenderTimeRef.current > 0) {
      const timeSinceLastRender = now - lastRenderTimeRef.current;
      performanceMonitor.recordMetric(`render_interval_${componentName}`, timeSinceLastRender);
      
      // Warn about frequent re-renders
      if (timeSinceLastRender < 16 && renderCountRef.current > 5) {
        console.warn(`Frequent re-renders detected in ${componentName}: ${renderCountRef.current} renders`);
      }
    }
    
    lastRenderTimeRef.current = now;
  });
  
  return {
    renderCount: renderCountRef.current,
    resetRenderCount: () => { renderCountRef.current = 0; }
  };
}

// Hook for preventing unnecessary effect runs
export function useStableEffect(
  effect: React.EffectCallback,
  deps: React.DependencyList,
  isEqual?: (a: React.DependencyList, b: React.DependencyList) => boolean
) {
  const prevDepsRef = useRef<React.DependencyList>();
  const cleanupRef = useRef<void | (() => void)>();
  
  const depsChanged = !prevDepsRef.current ||
    (isEqual ? !isEqual(deps, prevDepsRef.current) : 
     deps.some((dep, index) => dep !== prevDepsRef.current![index]));
  
  useEffect(() => {
    if (depsChanged) {
      // Cleanup previous effect
      if (cleanupRef.current) {
        cleanupRef.current();
      }
      
      // Run new effect
      cleanupRef.current = effect();
      prevDepsRef.current = deps;
    }
    
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
      }
    };
  }, [depsChanged]);
}

// Hook for batching state updates
export function useBatchedUpdates() {
  const updateQueueRef = useRef<(() => void)[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  const batchUpdate = useCallback((updateFn: () => void) => {
    updateQueueRef.current.push(updateFn);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      const updates = updateQueueRef.current.splice(0);
      updates.forEach(update => update());
    }, 0);
  }, []);
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return batchUpdate;
}

// Hook for lazy initialization of expensive values
export function useLazyValue<T>(factory: () => T): T {
  const ref = useRef<{ hasValue: boolean; value: T }>({ hasValue: false, value: undefined as any });
  
  if (!ref.current.hasValue) {
    ref.current.value = factory();
    ref.current.hasValue = true;
  }
  
  return ref.current.value;
}

// Hook for conditional rendering optimization
export function useConditionalRender<T>(
  condition: boolean,
  component: () => T,
  fallback?: () => T
): T | null {
  return useMemo(() => {
    if (condition) {
      return component();
    }
    return fallback ? fallback() : null;
  }, [condition, component, fallback]);
}

// Hook for optimizing list rendering
export function useOptimizedList<T>(
  items: T[],
  keyExtractor: (item: T, index: number) => string,
  renderItem: (item: T, index: number) => React.ReactNode,
  maxVisible?: number
) {
  const visibleItems = useMemo(() => {
    return maxVisible ? items.slice(0, maxVisible) : items;
  }, [items, maxVisible]);
  
  const renderedItems = useMemo(() => {
    return visibleItems.map((item, index) => ({
      key: keyExtractor(item, index),
      element: renderItem(item, index)
    }));
  }, [visibleItems, keyExtractor, renderItem]);
  
  return {
    renderedItems,
    hasMore: maxVisible ? items.length > maxVisible : false,
    totalCount: items.length,
    visibleCount: visibleItems.length
  };
}

// Hook for performance budget monitoring
export function usePerformanceBudget(
  componentName: string,
  budget: {
    maxRenderTime?: number;
    maxMemoryUsage?: number;
    maxReRenders?: number;
  }
) {
  const renderCountRef = useRef(0);
  const violationsRef = useRef<string[]>([]);
  
  useEffect(() => {
    renderCountRef.current++;
    
    // Check render count budget
    if (budget.maxReRenders && renderCountRef.current > budget.maxReRenders) {
      const violation = `${componentName} exceeded render budget: ${renderCountRef.current}/${budget.maxReRenders}`;
      if (!violationsRef.current.includes(violation)) {
        violationsRef.current.push(violation);
        console.warn(violation);
      }
    }
    
    // Check memory usage if available
    if (budget.maxMemoryUsage && 'memory' in performance) {
      const memInfo = (performance as any).memory;
      const usedMB = memInfo.usedJSHeapSize / 1024 / 1024;
      
      if (usedMB > budget.maxMemoryUsage) {
        const violation = `${componentName} exceeded memory budget: ${usedMB.toFixed(2)}MB/${budget.maxMemoryUsage}MB`;
        if (!violationsRef.current.includes(violation)) {
          violationsRef.current.push(violation);
          console.warn(violation);
        }
      }
    }
  });
  
  return {
    renderCount: renderCountRef.current,
    violations: violationsRef.current,
    resetViolations: () => { violationsRef.current = []; }
  };
}