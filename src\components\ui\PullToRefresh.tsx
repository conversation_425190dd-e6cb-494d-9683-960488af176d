import React, { useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { RefreshCw, ChevronDown } from 'lucide-react';
import { cn } from '../../utils/helpers';
import { usePullToRefresh } from '../../hooks/usePullToRefresh';

interface PullToRefreshProps {
  onRefresh: () => Promise<void> | void;
  children: React.ReactNode;
  threshold?: number;
  maxPullDistance?: number;
  enabled?: boolean;
  className?: string;
  indicatorClassName?: string;
  refreshingText?: string;
  pullText?: string;
  releaseText?: string;
  showIcon?: boolean;
}

const PullToRefresh: React.FC<PullToRefreshProps> = ({
  onRefresh,
  children,
  threshold = 80,
  maxPullDistance = 120,
  enabled = true,
  className,
  indicatorClassName,
  refreshingText = 'Refreshing...',
  pullText = 'Pull to refresh',
  releaseText = 'Release to refresh',
  showIcon = true
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  
  const {
    attachToElement,
    pullState,
    getPullIndicatorText,
    getPullProgress,
    pullDistance,
    isPulling,
    isRefreshing,
    canRefresh
  } = usePullToRefresh({
    onRefresh,
    threshold,
    maxPullDistance,
    enabled,
    refreshingText,
    pullText,
    releaseText
  });

  useEffect(() => {
    if (containerRef.current) {
      attachToElement(containerRef.current);
    }
  }, [attachToElement]);

  const indicatorVariants = {
    hidden: {
      opacity: 0,
      y: -20,
      scale: 0.8
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.2,
        ease: 'easeOut'
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      scale: 0.8,
      transition: {
        duration: 0.15,
        ease: 'easeIn'
      }
    }
  };

  const getIndicatorColor = () => {
    if (isRefreshing) return 'text-blue-600';
    if (canRefresh) return 'text-green-600';
    return 'text-gray-400';
  };

  const getIndicatorBgColor = () => {
    if (isRefreshing) return 'bg-blue-50 border-blue-200';
    if (canRefresh) return 'bg-green-50 border-green-200';
    return 'bg-gray-50 border-gray-200';
  };

  return (
    <div 
      ref={containerRef}
      className={cn('relative overflow-hidden', className)}
      style={{
        transform: isPulling ? `translateY(${Math.min(pullDistance * 0.5, 60)}px)` : undefined,
        transition: isPulling ? 'none' : 'transform 0.3s ease-out'
      }}
    >
      {/* Pull Indicator */}
      <AnimatePresence>
        {(isPulling || isRefreshing) && (
          <motion.div
            variants={indicatorVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className={cn(
              'absolute top-0 left-1/2 transform -translate-x-1/2 z-10',
              'flex items-center justify-center gap-2 px-4 py-2 rounded-full',
              'border shadow-lg backdrop-blur-sm',
              getIndicatorBgColor(),
              indicatorClassName
            )}
            style={{
              transform: `translateX(-50%) translateY(${Math.max(-40, -40 + pullDistance * 0.3)}px)`
            }}
          >
            {showIcon && (
              <div className={cn('flex items-center justify-center', getIndicatorColor())}>
                {isRefreshing ? (
                  <RefreshCw 
                    className="w-4 h-4 animate-spin" 
                    style={{
                      animationDuration: '1s'
                    }}
                  />
                ) : (
                  <ChevronDown 
                    className="w-4 h-4 transition-transform duration-200"
                    style={{
                      transform: canRefresh ? 'rotate(180deg)' : 'rotate(0deg)'
                    }}
                  />
                )}
              </div>
            )}
            
            <span className={cn('text-sm font-medium', getIndicatorColor())}>
              {getPullIndicatorText()}
            </span>
            
            {/* Progress indicator */}
            {!isRefreshing && (
              <div className="w-8 h-1 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className={cn(
                    'h-full transition-all duration-100 ease-out rounded-full',
                    canRefresh ? 'bg-green-500' : 'bg-blue-500'
                  )}
                  style={{
                    width: `${getPullProgress() * 100}%`
                  }}
                />
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Content */}
      <div className="relative">
        {children}
      </div>
    </div>
  );
};

export default PullToRefresh;