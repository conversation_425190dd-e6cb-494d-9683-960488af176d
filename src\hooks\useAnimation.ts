import { useAnimation as use<PERSON><PERSON><PERSON><PERSON>nimation, AnimationControls } from 'framer-motion';
import { useCallback, useRef } from 'react';
import { animationVariants, transitions } from '../utils/animations';

export interface UseAnimationOptions {
  autoPlay?: boolean;
  delay?: number;
  duration?: number;
  repeat?: number;
  onComplete?: () => void;
}

export const useAnimation = (options: UseAnimationOptions = {}) => {
  const controls = useFramerAnimation();
  const timeoutRef = useRef<NodeJS.Timeout>();

  const animate = useCallback(
    async (variant: string, customOptions?: UseAnimationOptions) => {
      const finalOptions = { ...options, ...customOptions };
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      if (finalOptions.delay) {
        return new Promise<void>((resolve) => {
          timeoutRef.current = setTimeout(async () => {
            await controls.start(variant);
            finalOptions.onComplete?.();
            resolve();
          }, finalOptions.delay * 1000);
        });
      }

      await controls.start(variant);
      finalOptions.onComplete?.();
    },
    [controls, options]
  );

  const animateSequence = useCallback(
    async (sequence: Array<{ variant: string; delay?: number }>) => {
      for (const step of sequence) {
        await animate(step.variant, { delay: step.delay });
      }
    },
    [animate]
  );

  const reset = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    controls.stop();
  }, [controls]);

  return {
    controls,
    animate,
    animateSequence,
    reset
  };
};

export default useAnimation;