// Advanced search service with history and suggestions

import { Project } from '../types/project';
import { fuzzySearch, SearchResult } from '../utils/fuzzySearch';

export interface SearchHistoryItem {
  id: string;
  query: string;
  timestamp: string;
  resultCount: number;
}

export interface SearchSuggestion {
  text: string;
  type: 'history' | 'tag' | 'name' | 'description';
  count?: number;
}

export interface SearchOptions {
  includeHistory?: boolean;
  includeSuggestions?: boolean;
  maxHistory?: number;
  maxSuggestions?: number;
  threshold?: number;
}

class SearchService {
  private static instance: SearchService;
  private searchHistory: SearchHistoryItem[] = [];
  private readonly STORAGE_KEY = 'rork-search-history';
  private readonly MAX_HISTORY_ITEMS = 50;

  private constructor() {
    this.loadSearchHistory();
  }

  static getInstance(): SearchService {
    if (!SearchService.instance) {
      SearchService.instance = new SearchService();
    }
    return SearchService.instance;
  }

  /**
   * Perform advanced search with fuzzy matching
   */
  search(
    projects: Project[],
    query: string,
    options: SearchOptions = {}
  ): {
    results: SearchResult<Project>[];
    suggestions: SearchSuggestion[];
    history: SearchHistoryItem[];
  } {
    const {
      includeHistory = true,
      includeSuggestions = true,
      maxHistory = 10,
      maxSuggestions = 5,
      threshold = 0.1
    } = options;

    // Perform fuzzy search
    const searchFields: Array<keyof Project | ((item: Project) => string)> = [
      'name',
      'description',
      (project) => project.tags.join(' '),
      'type',
      'status',
      'priority',
      'complexity'
    ];

    const results = fuzzySearch(projects, query, searchFields, {
      threshold,
      limit: 100,
      includeMatches: true
    });

    // Add to search history if query is meaningful
    if (query.trim().length > 0) {
      this.addToHistory(query, results.length);
    }

    // Generate suggestions
    const suggestions = includeSuggestions 
      ? this.generateSuggestions(projects, query, maxSuggestions)
      : [];

    // Get recent history
    const history = includeHistory 
      ? this.getRecentHistory(maxHistory)
      : [];

    return {
      results,
      suggestions,
      history
    };
  }

  /**
   * Get search suggestions based on query and available data
   */
  private generateSuggestions(
    projects: Project[],
    query: string,
    maxSuggestions: number
  ): SearchSuggestion[] {
    const suggestions: SearchSuggestion[] = [];
    const queryLower = query.toLowerCase();

    if (!query.trim()) {
      // Show popular tags and recent searches when no query
      const tagCounts = this.getTagCounts(projects);
      const popularTags = Object.entries(tagCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, maxSuggestions)
        .map(([tag, count]) => ({
          text: tag,
          type: 'tag' as const,
          count
        }));

      suggestions.push(...popularTags);
    } else {
      // History-based suggestions
      const historySuggestions = this.searchHistory
        .filter(item => 
          item.query.toLowerCase().includes(queryLower) && 
          item.query.toLowerCase() !== queryLower
        )
        .slice(0, 2)
        .map(item => ({
          text: item.query,
          type: 'history' as const
        }));

      suggestions.push(...historySuggestions);

      // Tag-based suggestions
      const tagCounts = this.getTagCounts(projects);
      const tagSuggestions = Object.keys(tagCounts)
        .filter(tag => tag.toLowerCase().includes(queryLower))
        .slice(0, 2)
        .map(tag => ({
          text: tag,
          type: 'tag' as const,
          count: tagCounts[tag]
        }));

      suggestions.push(...tagSuggestions);

      // Project name suggestions
      const nameSuggestions = projects
        .filter(project => 
          project.name.toLowerCase().includes(queryLower) &&
          project.name.toLowerCase() !== queryLower
        )
        .slice(0, 2)
        .map(project => ({
          text: project.name,
          type: 'name' as const
        }));

      suggestions.push(...nameSuggestions);
    }

    return suggestions.slice(0, maxSuggestions);
  }

  /**
   * Get tag counts for suggestions
   */
  private getTagCounts(projects: Project[]): Record<string, number> {
    const tagCounts: Record<string, number> = {};
    
    projects.forEach(project => {
      project.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    });

    return tagCounts;
  }

  /**
   * Add search query to history
   */
  private addToHistory(query: string, resultCount: number): void {
    const trimmedQuery = query.trim();
    if (!trimmedQuery || trimmedQuery.length < 2) return;

    // Remove existing entry if it exists
    this.searchHistory = this.searchHistory.filter(
      item => item.query.toLowerCase() !== trimmedQuery.toLowerCase()
    );

    // Add new entry at the beginning
    this.searchHistory.unshift({
      id: Date.now().toString(),
      query: trimmedQuery,
      timestamp: new Date().toISOString(),
      resultCount
    });

    // Limit history size
    if (this.searchHistory.length > this.MAX_HISTORY_ITEMS) {
      this.searchHistory = this.searchHistory.slice(0, this.MAX_HISTORY_ITEMS);
    }

    this.saveSearchHistory();
  }

  /**
   * Get recent search history
   */
  getRecentHistory(limit: number = 10): SearchHistoryItem[] {
    return this.searchHistory.slice(0, limit);
  }

  /**
   * Clear search history
   */
  clearHistory(): void {
    this.searchHistory = [];
    this.saveSearchHistory();
  }

  /**
   * Remove specific item from history
   */
  removeFromHistory(id: string): void {
    this.searchHistory = this.searchHistory.filter(item => item.id !== id);
    this.saveSearchHistory();
  }

  /**
   * Load search history from localStorage
   */
  private loadSearchHistory(): void {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        if (Array.isArray(parsed)) {
          this.searchHistory = parsed.filter(this.isValidHistoryItem);
        }
      }
    } catch (error) {
      console.error('Failed to load search history:', error);
      this.searchHistory = [];
    }
  }

  /**
   * Save search history to localStorage
   */
  private saveSearchHistory(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.searchHistory));
    } catch (error) {
      console.error('Failed to save search history:', error);
    }
  }

  /**
   * Validate history item structure
   */
  private isValidHistoryItem(item: any): item is SearchHistoryItem {
    return (
      item &&
      typeof item === 'object' &&
      typeof item.id === 'string' &&
      typeof item.query === 'string' &&
      typeof item.timestamp === 'string' &&
      typeof item.resultCount === 'number'
    );
  }

  /**
   * Get search analytics
   */
  getSearchAnalytics(): {
    totalSearches: number;
    uniqueQueries: number;
    averageResultCount: number;
    popularQueries: Array<{ query: string; count: number }>;
  } {
    const queryCount: Record<string, number> = {};
    let totalResultCount = 0;

    this.searchHistory.forEach(item => {
      const query = item.query.toLowerCase();
      queryCount[query] = (queryCount[query] || 0) + 1;
      totalResultCount += item.resultCount;
    });

    const popularQueries = Object.entries(queryCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([query, count]) => ({ query, count }));

    return {
      totalSearches: this.searchHistory.length,
      uniqueQueries: Object.keys(queryCount).length,
      averageResultCount: this.searchHistory.length > 0 
        ? totalResultCount / this.searchHistory.length 
        : 0,
      popularQueries
    };
  }
}

export default SearchService;