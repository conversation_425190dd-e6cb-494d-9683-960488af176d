import { useEffect, useRef } from 'react';
import { useInView, useAnimation } from 'framer-motion';
import { createFadeVariant } from '../utils/animations';

export interface UseFadeInOptions {
  duration?: number;
  delay?: number;
  threshold?: number;
  triggerOnce?: boolean;
  rootMargin?: string;
}

export const useFadeIn = (options: UseFadeInOptions = {}) => {
  const {
    duration = 0.3,
    delay = 0,
    threshold = 0.1,
    triggerOnce = true,
    rootMargin = '0px'
  } = options;

  const ref = useRef<HTMLElement>(null);
  const isInView = useInView(ref, {
    threshold,
    once: triggerOnce,
    margin: rootMargin
  });
  const controls = useAnimation();

  const variants = createFadeVariant(duration, delay);

  useEffect(() => {
    if (isInView) {
      controls.start('animate');
    } else if (!triggerOnce) {
      controls.start('initial');
    }
  }, [isInView, controls, triggerOnce]);

  return {
    ref,
    variants,
    animate: controls,
    isInView
  };
};

export default useFadeIn;