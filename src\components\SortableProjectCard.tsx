import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical } from 'lucide-react';
import { Project } from '../types/project';
import ProjectCard from './ProjectCard';
import { cn } from '../utils/helpers';

interface SortableProjectCardProps {
  project: Project;
  showDragHandle?: boolean;
  onClick: () => void;
  onAction: (projectId: string, action: 'star' | 'archive' | 'delete' | 'duplicate') => void;
  isDragging?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
}

const SortableProjectCard: React.FC<SortableProjectCardProps> = ({
  project,
  showDragHandle = true,
  onClick,
  onAction,
  isDragging = false,
  variant = 'default'
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: project.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const isCurrentlyDragging = isDragging || isSortableDragging;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        'relative group',
        isCurrentlyDragging && 'z-50'
      )}
    >
      <div className="flex items-center gap-2">
        {/* Drag Handle */}
        {showDragHandle && (
          <div
            {...attributes}
            {...listeners}
            className={cn(
              'flex items-center justify-center w-6 h-6 rounded cursor-grab active:cursor-grabbing',
              'text-gray-400 hover:text-gray-600 hover:bg-gray-100',
              'transition-all duration-200',
              'opacity-0 group-hover:opacity-100',
              isCurrentlyDragging && 'opacity-100 cursor-grabbing'
            )}
            aria-label="Drag to reorder"
          >
            <GripVertical className="w-4 h-4" />
          </div>
        )}

        {/* Project Card */}
        <div className="flex-1">
          <ProjectCard
            project={project}
            onClick={onClick}
            onAction={onAction}
            isDragging={isCurrentlyDragging}
            variant={variant}
            className={cn(
              isCurrentlyDragging && 'shadow-2xl ring-2 ring-blue-500/20'
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default SortableProjectCard;