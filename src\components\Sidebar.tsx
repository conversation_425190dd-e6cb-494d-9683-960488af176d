import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Plus,
  MessageSquare,
  Settings,
  User,
  HelpCircle,
  CreditCard,
  LogOut,
  MoreHorizontal
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { Project } from '../types/project';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  isMobile?: boolean;
  isTablet?: boolean;
}

// Helper function to group projects by time
const groupProjectsByTime = (projects: Project[]) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
  const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

  const groups = {
    today: [] as Project[],
    yesterday: [] as Project[],
    lastWeek: [] as Project[],
    lastMonth: [] as Project[],
    older: [] as Project[]
  };

  projects.forEach(project => {
    const projectDate = new Date(project.lastModified || project.createdAt || Date.now());
    
    if (projectDate >= today) {
      groups.today.push(project);
    } else if (projectDate >= yesterday) {
      groups.yesterday.push(project);
    } else if (projectDate >= lastWeek) {
      groups.lastWeek.push(project);
    } else if (projectDate >= lastMonth) {
      groups.lastMonth.push(project);
    } else {
      groups.older.push(project);
    }
  });

  return groups;
};

const Sidebar: React.FC<SidebarProps> = ({ 
  isOpen, 
  onClose, 
  isMobile = false,
  isTablet = false
}) => {
  const { projects } = useApp();
  const [searchQuery, setSearchQuery] = useState('');
  const [isHovering, setIsHovering] = useState(false);

  // Hover management for desktop
  const handleMouseEnter = () => {
    if (!isMobile && !isTablet && !isOpen) {
      setIsHovering(true);
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile && !isTablet && !isOpen) {
      setTimeout(() => setIsHovering(false), 200);
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        document.getElementById('search-input')?.focus();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Filter projects by search
  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const groupedProjects = groupProjectsByTime(filteredProjects);
  const shouldShowExpanded = isOpen || (!isMobile && !isTablet && isHovering);

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && isMobile && (
        <div 
          className="fixed inset-0 bg-black/50 z-40"
          onClick={onClose}
        />
      )}

      {/* Hover Zone for Desktop */}
      {!isMobile && !isTablet && !isOpen && (
        <div
          className="fixed left-0 top-0 w-6 h-full z-45"
          onMouseEnter={handleMouseEnter}
        />
      )}

      {/* Sidebar */}
      <aside 
        className={`
          fixed top-0 left-0 h-full z-50 transition-all duration-200 ease-out
          ${shouldShowExpanded ? 'w-64' : isMobile ? 'w-0' : 'w-16'}
          ${isMobile ? (isOpen ? 'translate-x-0' : '-translate-x-full') : 'translate-x-0'}
          bg-gray-900 border-r border-gray-800
        `}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {shouldShowExpanded ? (
          /* Expanded Sidebar */
          <div className="flex flex-col h-full">
            
            {/* Header Section */}
            <div className="flex-shrink-0 px-3 pt-4 pb-3">
              {/* Brand */}
              <div className="flex items-center gap-2 mb-4">
                <div className="text-white font-semibold text-lg">NORYON</div>
              </div>

              {/* New Chat Button */}
              <button className="w-full flex items-center gap-2 px-3 py-2.5 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors mb-4">
                <Plus className="w-4 h-4" />
                <span>Start new chat</span>
              </button>

              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500" />
                <input
                  id="search-input"
                  type="text"
                  placeholder="Search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-gray-800 border-0 rounded-lg pl-9 pr-3 py-2 text-sm text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-gray-600"
                />
              </div>
            </div>

            {/* Chat List Section */}
            <div className="flex-1 overflow-hidden">
              <div className="px-3 pt-2 pb-3">
                <h3 className="text-gray-400 text-xs font-medium">Your Chats</h3>
              </div>
              
              <div className="px-1 pb-3 overflow-y-auto flex-1">
                {filteredProjects.length === 0 ? (
                  <div className="text-center py-8 px-4">
                    <MessageSquare className="w-8 h-8 text-gray-600 mx-auto mb-2" />
                    <p className="text-gray-500 text-sm">No chats yet</p>
                  </div>
                ) : (
                                     <div className="space-y-4">
                     {groupedProjects.today.length > 0 && (
                       <div>
                         <div className="px-3 mb-1">
                           <span className="text-gray-500 text-xs">Today</span>
                         </div>
                         <div className="space-y-0.5">
                           {groupedProjects.today.map((project) => (
                             <Link key={project.id} to={`/project/${project.id}`}>
                               <div className="group mx-2 px-3 py-2.5 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors cursor-pointer">
                                 <div className="truncate">{project.name}</div>
                               </div>
                             </Link>
                           ))}
                         </div>
                       </div>
                     )}

                     {groupedProjects.yesterday.length > 0 && (
                       <div>
                         <div className="px-3 mb-1">
                           <span className="text-gray-500 text-xs">Yesterday</span>
                         </div>
                         <div className="space-y-0.5">
                           {groupedProjects.yesterday.map((project) => (
                             <Link key={project.id} to={`/project/${project.id}`}>
                               <div className="group mx-2 px-3 py-2.5 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors cursor-pointer">
                                 <div className="truncate">{project.name}</div>
                               </div>
                             </Link>
                           ))}
                         </div>
                       </div>
                     )}

                     {groupedProjects.lastWeek.length > 0 && (
                       <div>
                         <div className="px-3 mb-1">
                           <span className="text-gray-500 text-xs">Previous 7 Days</span>
                         </div>
                         <div className="space-y-0.5">
                           {groupedProjects.lastWeek.map((project) => (
                             <Link key={project.id} to={`/project/${project.id}`}>
                               <div className="group mx-2 px-3 py-2.5 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors cursor-pointer">
                                 <div className="truncate">{project.name}</div>
                               </div>
                             </Link>
                           ))}
                         </div>
                       </div>
                     )}

                     {groupedProjects.lastMonth.length > 0 && (
                       <div>
                         <div className="px-3 mb-1">
                           <span className="text-gray-500 text-xs">Previous 30 Days</span>
                         </div>
                         <div className="space-y-0.5">
                           {groupedProjects.lastMonth.map((project) => (
                             <Link key={project.id} to={`/project/${project.id}`}>
                               <div className="group mx-2 px-3 py-2.5 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors cursor-pointer">
                                 <div className="truncate">{project.name}</div>
                               </div>
                             </Link>
                           ))}
                         </div>
                       </div>
                     )}

                     {groupedProjects.older.length > 0 && (
                       <div>
                         <div className="px-3 mb-1">
                           <span className="text-gray-500 text-xs">Older</span>
                         </div>
                         <div className="space-y-0.5">
                           {groupedProjects.older.map((project) => (
                             <Link key={project.id} to={`/project/${project.id}`}>
                               <div className="group mx-2 px-3 py-2.5 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors cursor-pointer">
                                 <div className="truncate">{project.name}</div>
                               </div>
                             </Link>
                           ))}
                         </div>
                       </div>
                     )}
                   </div>
                )}
              </div>
            </div>

            {/* Footer Section */}
            <div className="flex-shrink-0 border-t border-gray-800 px-3 pt-3 pb-4 space-y-1">
              <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors">
                <CreditCard className="w-4 h-4" />
                <span>Get free tokens</span>
              </button>
              
              <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors">
                <Settings className="w-4 h-4" />
                <span>Settings</span>
              </button>
              
              <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors">
                <HelpCircle className="w-4 h-4" />
                <span>Help Center</span>
              </button>
              
              <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors">
                <CreditCard className="w-4 h-4" />
                <span>My Subscription</span>
              </button>
              
              <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors">
                <User className="w-4 h-4" />
                <span>Select Account</span>
              </button>
              
              <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-300 hover:bg-gray-800 rounded-lg transition-colors">
                <LogOut className="w-4 h-4" />
                <span>Sign Out</span>
              </button>

              {/* User Profile */}
              <div className="flex items-center gap-3 px-3 py-2.5 mt-3">
                <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">M</span>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-white text-sm">mohhzaman</div>
                  <div className="text-gray-400 text-xs">Pro Personal Plan</div>
                </div>
                <MoreHorizontal className="w-4 h-4 text-gray-500" />
              </div>
            </div>
          </div>
        ) : (
          /* Collapsed Sidebar */
          <div className="flex flex-col items-center h-full px-2 py-4">
            
            {/* Logo */}
            <div className="flex-shrink-0 mb-6">
              <div className="w-10 h-10 flex items-center justify-center text-white font-semibold text-lg">
                N
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex-shrink-0 space-y-3 mb-6">
              <button className="w-10 h-10 flex items-center justify-center text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors">
                <Plus className="w-5 h-5" />
              </button>
              <button className="w-10 h-10 flex items-center justify-center text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors">
                <Search className="w-5 h-5" />
              </button>
            </div>
            
            {/* Spacer */}
            <div className="flex-1"></div>
            
            {/* Bottom Buttons */}
            <div className="flex-shrink-0 space-y-3">
              <button className="w-10 h-10 flex items-center justify-center text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors">
                <Settings className="w-5 h-5" />
              </button>
              <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">M</span>
              </div>
            </div>
          </div>
        )}
      </aside>
    </>
  );
};

export default Sidebar;