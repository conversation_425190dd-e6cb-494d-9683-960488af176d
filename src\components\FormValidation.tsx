import React, { useState, useEffect } from 'react';
import { Check, X, AlertCircle, Eye, EyeOff, Mail, User, Lock, Phone } from 'lucide-react';

interface ValidationRule {
  test: (value: string) => boolean;
  message: string;
  type: 'error' | 'warning';
}

interface FormField {
  name: string;
  value: string;
  rules: ValidationRule[];
  touched: boolean;
  focused: boolean;
}

interface FormErrors {
  [key: string]: {
    message: string;
    type: 'error' | 'warning';
  } | null;
}

const FormValidation: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);

  const [formData, setFormData] = useState<{ [key: string]: FormField }>({
    firstName: {
      name: 'firstName',
      value: '',
      rules: [
        { test: (v) => v.length >= 2, message: 'First name must be at least 2 characters', type: 'error' },
        { test: (v) => /^[a-zA-Z\s]+$/.test(v), message: 'First name can only contain letters', type: 'error' }
      ],
      touched: false,
      focused: false
    },
    lastName: {
      name: 'lastName',
      value: '',
      rules: [
        { test: (v) => v.length >= 2, message: 'Last name must be at least 2 characters', type: 'error' },
        { test: (v) => /^[a-zA-Z\s]+$/.test(v), message: 'Last name can only contain letters', type: 'error' }
      ],
      touched: false,
      focused: false
    },
    email: {
      name: 'email',
      value: '',
      rules: [
        { test: (v) => v.length > 0, message: 'Email is required', type: 'error' },
        { test: (v) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v), message: 'Please enter a valid email address', type: 'error' },
        { test: (v) => !v.includes('+'), message: 'Consider using your primary email address', type: 'warning' }
      ],
      touched: false,
      focused: false
    },
    phone: {
      name: 'phone',
      value: '',
      rules: [
        { test: (v) => v.length >= 10, message: 'Phone number must be at least 10 digits', type: 'error' },
        { test: (v) => /^\+?[\d\s\-\(\)]+$/.test(v), message: 'Please enter a valid phone number', type: 'error' }
      ],
      touched: false,
      focused: false
    },
    password: {
      name: 'password',
      value: '',
      rules: [
        { test: (v) => v.length >= 8, message: 'Password must be at least 8 characters', type: 'error' },
        { test: (v) => /[A-Z]/.test(v), message: 'Password must contain at least one uppercase letter', type: 'error' },
        { test: (v) => /[a-z]/.test(v), message: 'Password must contain at least one lowercase letter', type: 'error' },
        { test: (v) => /\d/.test(v), message: 'Password must contain at least one number', type: 'error' },
        { test: (v) => /[!@#$%^&*(),.?":{}|<>]/.test(v), message: 'Password must contain at least one special character', type: 'error' }
      ],
      touched: false,
      focused: false
    },
    confirmPassword: {
      name: 'confirmPassword',
      value: '',
      rules: [
        { test: (v) => v === formData.password?.value, message: 'Passwords do not match', type: 'error' }
      ],
      touched: false,
      focused: false
    }
  });

  // Validate individual field
  const validateField = (field: FormField): { message: string; type: 'error' | 'warning' } | null => {
    if (!field.value && !field.touched && !submitAttempted) return null;
    
    for (const rule of field.rules) {
      if (!rule.test(field.value)) {
        return { message: rule.message, type: rule.type };
      }
    }
    return null;
  };

  // Get form errors
  const getFormErrors = (): FormErrors => {
    const errors: FormErrors = {};
    Object.keys(formData).forEach(key => {
      errors[key] = validateField(formData[key]);
    });
    return errors;
  };

  const formErrors = getFormErrors();

  // Check if form is valid
  const isFormValid = () => {
    return Object.values(formErrors).every(error => error === null || error.type === 'warning');
  };

  // Handle input change with real-time validation
  const handleInputChange = (fieldName: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        value,
        touched: true
      }
    }));
  };

  // Handle focus
  const handleFocus = (fieldName: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        focused: true
      }
    }));
  };

  // Handle blur
  const handleBlur = (fieldName: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        focused: false,
        touched: true
      }
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitAttempted(true);

    // Mark all fields as touched
    setFormData(prev => {
      const updated = { ...prev };
      Object.keys(updated).forEach(key => {
        updated[key] = { ...updated[key], touched: true };
      });
      return updated;
    });

    if (!isFormValid()) {
      // Focus on first error field
      const firstErrorField = Object.keys(formErrors).find(key => 
        formErrors[key]?.type === 'error'
      );
      if (firstErrorField) {
        document.getElementById(firstErrorField)?.focus();
      }
      return;
    }

    setIsSubmitting(true);

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setIsSubmitted(true);
    } catch (error) {
      console.error('Submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData(prev => {
      const reset = { ...prev };
      Object.keys(reset).forEach(key => {
        reset[key] = { ...reset[key], value: '', touched: false, focused: false };
      });
      return reset;
    });
    setIsSubmitted(false);
    setSubmitAttempted(false);
  };

  // Get field status
  const getFieldStatus = (fieldName: string) => {
    const field = formData[fieldName];
    const error = formErrors[fieldName];
    
    if (!field.touched && !submitAttempted) return 'default';
    if (error?.type === 'error') return 'error';
    if (error?.type === 'warning') return 'warning';
    if (field.value) return 'success';
    return 'default';
  };

  // Success screen
  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-6">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Check className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Account Created Successfully!</h2>
          <p className="text-gray-600 mb-6">
            Welcome aboard! We've sent a confirmation email to {formData.email.value}
          </p>
          <button
            onClick={resetForm}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl hover:bg-blue-700 transition-colors font-medium"
          >
            Create Another Account
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center safe-area-inset">
      <div className="container-tight">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-auto animate-scale-in">
          <div className="text-center spacing-component">
            <h1 className="text-3xl font-bold text-gray-900 spacing-element">Create Account</h1>
            <p className="text-gray-600">Join us today with smart form validation</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6" noValidate>
          {/* Name Fields Row */}
          <div className="grid grid-cols-2 gap-4">
            {/* First Name */}
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                First Name
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="firstName"
                  type="text"
                  value={formData.firstName.value}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  onFocus={() => handleFocus('firstName')}
                  onBlur={() => handleBlur('firstName')}
                  className={`w-full pl-10 pr-10 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all ${
                    getFieldStatus('firstName') === 'error' 
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                      : getFieldStatus('firstName') === 'warning'
                      ? 'border-yellow-300 focus:ring-yellow-500 focus:border-yellow-500'
                      : getFieldStatus('firstName') === 'success'
                      ? 'border-green-300 focus:ring-green-500 focus:border-green-500'
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  placeholder="John"
                />
                {getFieldStatus('firstName') === 'success' && (
                  <Check className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
                )}
                {getFieldStatus('firstName') === 'error' && (
                  <X className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
                )}
                {getFieldStatus('firstName') === 'warning' && (
                  <AlertCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-yellow-500" />
                )}
              </div>
              {formErrors.firstName && (
                <p className={`mt-2 text-sm flex items-center gap-2 ${
                  formErrors.firstName.type === 'error' ? 'text-red-600' : 'text-yellow-600'
                }`}>
                  {formErrors.firstName.type === 'error' ? (
                    <X className="w-4 h-4" />
                  ) : (
                    <AlertCircle className="w-4 h-4" />
                  )}
                  {formErrors.firstName.message}
                </p>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                Last Name
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="lastName"
                  type="text"
                  value={formData.lastName.value}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  onFocus={() => handleFocus('lastName')}
                  onBlur={() => handleBlur('lastName')}
                  className={`w-full pl-10 pr-10 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all ${
                    getFieldStatus('lastName') === 'error' 
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                      : getFieldStatus('lastName') === 'warning'
                      ? 'border-yellow-300 focus:ring-yellow-500 focus:border-yellow-500'
                      : getFieldStatus('lastName') === 'success'
                      ? 'border-green-300 focus:ring-green-500 focus:border-green-500'
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  placeholder="Doe"
                />
                {getFieldStatus('lastName') === 'success' && (
                  <Check className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
                )}
                {getFieldStatus('lastName') === 'error' && (
                  <X className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
                )}
                {getFieldStatus('lastName') === 'warning' && (
                  <AlertCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-yellow-500" />
                )}
              </div>
              {formErrors.lastName && (
                <p className={`mt-2 text-sm flex items-center gap-2 ${
                  formErrors.lastName.type === 'error' ? 'text-red-600' : 'text-yellow-600'
                }`}>
                  {formErrors.lastName.type === 'error' ? (
                    <X className="w-4 h-4" />
                  ) : (
                    <AlertCircle className="w-4 h-4" />
                  )}
                  {formErrors.lastName.message}
                </p>
              )}
            </div>
          </div>

          {/* Email */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                id="email"
                type="email"
                value={formData.email.value}
                onChange={(e) => handleInputChange('email', e.target.value)}
                onFocus={() => handleFocus('email')}
                onBlur={() => handleBlur('email')}
                className={`w-full pl-10 pr-10 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all ${
                  getFieldStatus('email') === 'error' 
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                    : getFieldStatus('email') === 'warning'
                    ? 'border-yellow-300 focus:ring-yellow-500 focus:border-yellow-500'
                    : getFieldStatus('email') === 'success'
                    ? 'border-green-300 focus:ring-green-500 focus:border-green-500'
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                }`}
                placeholder="<EMAIL>"
              />
              {getFieldStatus('email') === 'success' && (
                <Check className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
              )}
              {getFieldStatus('email') === 'error' && (
                <X className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
              )}
              {getFieldStatus('email') === 'warning' && (
                <AlertCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-yellow-500" />
              )}
            </div>
            {formErrors.email && (
              <p className={`mt-2 text-sm flex items-center gap-2 ${
                formErrors.email.type === 'error' ? 'text-red-600' : 'text-yellow-600'
              }`}>
                {formErrors.email.type === 'error' ? (
                  <X className="w-4 h-4" />
                ) : (
                  <AlertCircle className="w-4 h-4" />
                )}
                {formErrors.email.message}
              </p>
            )}
          </div>

          {/* Phone */}
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                id="phone"
                type="tel"
                value={formData.phone.value}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                onFocus={() => handleFocus('phone')}
                onBlur={() => handleBlur('phone')}
                className={`w-full pl-10 pr-10 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all ${
                  getFieldStatus('phone') === 'error' 
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                    : getFieldStatus('phone') === 'warning'
                    ? 'border-yellow-300 focus:ring-yellow-500 focus:border-yellow-500'
                    : getFieldStatus('phone') === 'success'
                    ? 'border-green-300 focus:ring-green-500 focus:border-green-500'
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                }`}
                placeholder="+****************"
              />
              {getFieldStatus('phone') === 'success' && (
                <Check className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
              )}
              {getFieldStatus('phone') === 'error' && (
                <X className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
              )}
              {getFieldStatus('phone') === 'warning' && (
                <AlertCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-yellow-500" />
              )}
            </div>
            {formErrors.phone && (
              <p className={`mt-2 text-sm flex items-center gap-2 ${
                formErrors.phone.type === 'error' ? 'text-red-600' : 'text-yellow-600'
              }`}>
                {formErrors.phone.type === 'error' ? (
                  <X className="w-4 h-4" />
                ) : (
                  <AlertCircle className="w-4 h-4" />
                )}
                {formErrors.phone.message}
              </p>
            )}
          </div>

          {/* Password */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password.value}
                onChange={(e) => handleInputChange('password', e.target.value)}
                onFocus={() => handleFocus('password')}
                onBlur={() => handleBlur('password')}
                className={`w-full pl-10 pr-20 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all ${
                  getFieldStatus('password') === 'error' 
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                    : getFieldStatus('password') === 'warning'
                    ? 'border-yellow-300 focus:ring-yellow-500 focus:border-yellow-500'
                    : getFieldStatus('password') === 'success'
                    ? 'border-green-300 focus:ring-green-500 focus:border-green-500'
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                }`}
                placeholder="Create a strong password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-10 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
              {getFieldStatus('password') === 'success' && (
                <Check className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
              )}
              {getFieldStatus('password') === 'error' && (
                <X className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
              )}
            </div>
            
            {/* Password Strength Indicator */}
            {formData.password.value && (
              <div className="mt-3">
                <div className="flex gap-1 mb-2">
                  {[...Array(5)].map((_, i) => {
                    const rules = formData.password.rules;
                    const passedRules = rules.filter(rule => rule.test(formData.password.value)).length;
                    return (
                      <div
                        key={i}
                        className={`h-1 flex-1 rounded ${
                          i < passedRules 
                            ? passedRules <= 2 ? 'bg-red-400' 
                              : passedRules <= 3 ? 'bg-yellow-400' 
                              : 'bg-green-400'
                            : 'bg-gray-200'
                        }`}
                      />
                    );
                  })}
                </div>
                <div className="space-y-1">
                  {formData.password.rules.map((rule, index) => (
                    <div key={index} className="flex items-center gap-2 text-xs">
                      {rule.test(formData.password.value) ? (
                        <Check className="w-3 h-3 text-green-500" />
                      ) : (
                        <X className="w-3 h-3 text-gray-400" />
                      )}
                      <span className={rule.test(formData.password.value) ? 'text-green-600' : 'text-gray-500'}>
                        {rule.message}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Confirm Password */}
          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
              Confirm Password
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                id="confirmPassword"
                type="password"
                value={formData.confirmPassword.value}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                onFocus={() => handleFocus('confirmPassword')}
                onBlur={() => handleBlur('confirmPassword')}
                className={`w-full pl-10 pr-10 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all ${
                  getFieldStatus('confirmPassword') === 'error' 
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                    : getFieldStatus('confirmPassword') === 'success'
                    ? 'border-green-300 focus:ring-green-500 focus:border-green-500'
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                }`}
                placeholder="Confirm your password"
              />
              {getFieldStatus('confirmPassword') === 'success' && (
                <Check className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
              )}
              {getFieldStatus('confirmPassword') === 'error' && (
                <X className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
              )}
            </div>
            {formErrors.confirmPassword && (
              <p className="mt-2 text-sm flex items-center gap-2 text-red-600">
                <X className="w-4 h-4" />
                {formErrors.confirmPassword.message}
              </p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-full py-3 px-4 rounded-xl font-medium transition-all ${
              isSubmitting
                ? 'bg-gray-400 cursor-not-allowed'
                : isFormValid()
                ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                Creating Account...
              </div>
            ) : (
              'Create Account'
            )}
          </button>

          {/* Form Progress */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
              <div className="flex gap-1">
                {Object.keys(formData).map((key, index) => (
                  <div
                    key={key}
                    className={`w-2 h-2 rounded-full ${
                      getFieldStatus(key) === 'success' ? 'bg-green-400' : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>
              <span>
                {Object.values(formErrors).filter(error => error === null || error.type === 'warning').length} of {Object.keys(formData).length} fields completed
              </span>
            </div>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
};

export default FormValidation;