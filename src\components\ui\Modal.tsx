import React, { useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { cn } from '../../utils/helpers';
import { useResponsiveLayout } from '../../hooks/useResponsiveLayout';
import { useSafeEventListener } from '../../hooks/useEnhancedMemoryManagement';
import Button from './Button';
import MobileModal from './MobileModal';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  variant?: 'default' | 'centered' | 'drawer';
  showCloseButton?: boolean;
  closeOnBackdropClick?: boolean;
  closeOnEscape?: boolean;
  preventScroll?: boolean;
  className?: string;
  overlayClassName?: string;
  contentClassName?: string;
}

const backdropVariants = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1,
    transition: { duration: 0.2, ease: 'easeOut' }
  },
  exit: { 
    opacity: 0,
    transition: { duration: 0.15, ease: 'easeIn' }
  }
};

const modalVariants = {
  default: {
    hidden: { 
      opacity: 0, 
      scale: 0.95, 
      y: -20 
    },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: { 
        duration: 0.3, 
        ease: 'easeOut',
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.95, 
      y: -20,
      transition: { duration: 0.2, ease: 'easeIn' }
    }
  },
  drawer: {
    hidden: { 
      opacity: 0, 
      x: '100%' 
    },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { 
        duration: 0.3, 
        ease: 'easeOut',
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: { 
      opacity: 0, 
      x: '100%',
      transition: { duration: 0.25, ease: 'easeIn' }
    }
  }
};

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  variant = 'default',
  showCloseButton = true,
  closeOnBackdropClick = true,
  closeOnEscape = true,
  preventScroll = true,
  className,
  overlayClassName,
  contentClassName
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);
  const { isMobile } = useResponsiveLayout();

  // Focus management
  const focusableElementsSelector = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';

  const trapFocus = useCallback((e: KeyboardEvent) => {
    if (!modalRef.current) return;

    const focusableElements = modalRef.current.querySelectorAll(focusableElementsSelector);
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    if (e.key === 'Tab') {
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    }
  }, []);

  const handleEscape = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape' && closeOnEscape) {
      onClose();
    }
  }, [closeOnEscape, onClose]);

  const handleBackdropClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget && closeOnBackdropClick) {
      onClose();
    }
  }, [closeOnBackdropClick, onClose]);

  // Touch handling for mobile
  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (variant === 'drawer') {
      // Add swipe-to-close functionality for drawer variant
      const startY = e.touches[0].clientY;
      const startX = e.touches[0].clientX;

      const handleTouchMove = (moveEvent: TouchEvent) => {
        const currentX = moveEvent.touches[0].clientX;
        const deltaX = currentX - startX;
        
        // If swiping right with significant distance, close modal
        if (deltaX > 100) {
          onClose();
          document.removeEventListener('touchmove', handleTouchMove);
        }
      };

      document.addEventListener('touchmove', handleTouchMove, { once: true });
    }
  }, [variant, onClose]);

  // Use safe event listeners for proper cleanup
  useSafeEventListener('keydown', handleEscape, isOpen ? document : null, undefined, [closeOnEscape, onClose]);
  useSafeEventListener('keydown', trapFocus, isOpen ? document : null, undefined, []);
  useSafeEventListener('touchstart', handleTouchStart, isOpen && variant === 'drawer' ? document : null, undefined, [variant, onClose]);

  useEffect(() => {
    if (isOpen) {
      // Store previously focused element
      previousActiveElement.current = document.activeElement as HTMLElement;
      
      // Prevent scroll if enabled
      if (preventScroll) {
        document.body.style.overflow = 'hidden';
      }

      // Focus first focusable element
      const timeoutId = setTimeout(() => {
        const firstFocusable = modalRef.current?.querySelector(focusableElementsSelector) as HTMLElement;
        firstFocusable?.focus();
      }, 100);

      return () => {
        clearTimeout(timeoutId);
      };
    }

    return () => {
      // Restore scroll
      if (preventScroll) {
        document.body.style.overflow = 'unset';
      }

      // Restore focus
      if (previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
    };
  }, [isOpen, preventScroll]); // Proper dependency array

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-full h-full'
  };

  const getModalClasses = () => {
    const base = 'relative w-full mx-4';
    
    if (variant === 'drawer') {
      return 'fixed right-0 top-0 h-full w-full max-w-md';
    }
    
    if (size === 'full') {
      return 'w-full h-full m-4';
    }
    
    return cn(base, sizeClasses[size]);
  };

  const getContainerClasses = () => {
    if (variant === 'drawer') {
      return 'fixed inset-0 z-50 flex justify-end';
    }
    
    return 'fixed inset-0 z-50 flex items-center justify-center';
  };

  // Use MobileModal on mobile devices
  if (isMobile) {
    return (
      <MobileModal
        isOpen={isOpen}
        onClose={onClose}
        title={title}
        variant={variant === 'drawer' ? 'drawer' : 'bottom-sheet'}
        showCloseButton={showCloseButton}
        closeOnBackdropClick={closeOnBackdropClick}
        closeOnSwipe={true}
        preventScroll={preventScroll}
        className={className}
        overlayClassName={overlayClassName}
        contentClassName={contentClassName}
      >
        {children}
      </MobileModal>
    );
  }

  return (
    <AnimatePresence mode="wait">
      {isOpen && (
        <div className={getContainerClasses()}>
          {/* Backdrop */}
          <motion.div
            variants={backdropVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className={cn(
              'absolute inset-0 bg-black/60 backdrop-blur-sm',
              overlayClassName
            )}
            onClick={handleBackdropClick}
          />
          
          {/* Modal */}
          <motion.div
            ref={modalRef}
            variants={modalVariants[variant === 'drawer' ? 'drawer' : 'default']}
            initial="hidden"
            animate="visible"
            exit="exit"
            className={getModalClasses()}
            role="dialog"
            aria-modal="true"
            aria-labelledby={title ? 'modal-title' : undefined}
          >
            <div className={cn(
              'bg-white border border-gray-200 shadow-2xl',
              variant === 'drawer' ? 'h-full rounded-l-2xl' : 'rounded-2xl',
              size === 'full' && 'h-full',
              contentClassName
            )}>
              {/* Header */}
              {(title || showCloseButton) && (
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  {title && (
                    <h2 id="modal-title" className="text-xl font-semibold text-gray-900">
                      {title}
                    </h2>
                  )}
                  {showCloseButton && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onClose}
                      leftIcon={<X className="h-4 w-4" />}
                      className="ml-auto"
                      aria-label="Close modal"
                    />
                  )}
                </div>
              )}
              
              {/* Content */}
              <div className={cn(
                'p-6',
                size === 'full' && 'flex-1 overflow-auto',
                className
              )}>
                {children}
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default Modal;