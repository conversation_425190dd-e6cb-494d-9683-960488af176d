import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Smartphone, 
  Gesture, 
  RefreshCw, 
  Hand, 
  TouchIcon,
  Vibrate,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';
import { useResponsiveLayout } from '../hooks/useResponsiveLayout';
import TouchButton from './ui/TouchButton';
import MobileModal from './ui/MobileModal';
import SwipeableCard, { commonSwipeActions } from './ui/SwipeableCard';
import PullToRefresh from './ui/PullToRefresh';
import { cn } from '../utils/helpers';

const MobileInteractionsDemo: React.FC = () => {
  const { isMobile } = useResponsiveLayout();
  const [showModal, setShowModal] = useState(false);
  const [modalVariant, setModalVariant] = useState<'bottom-sheet' | 'full-screen' | 'drawer'>('bottom-sheet');
  const [refreshCount, setRefreshCount] = useState(0);
  const [swipeCount, setSwipeCount] = useState(0);

  const handleRefresh = async () => {
    await new Promise(resolve => setTimeout(resolve, 1500));
    setRefreshCount(prev => prev + 1);
  };

  const handleSwipeAction = (action: string) => {
    setSwipeCount(prev => prev + 1);
    console.log(`Swiped: ${action}`);
  };

  const demoCards = [
    {
      id: '1',
      title: 'Swipeable Card 1',
      description: 'Swipe left for actions, right for more options',
      color: 'bg-blue-50 border-blue-200'
    },
    {
      id: '2',
      title: 'Swipeable Card 2',
      description: 'Try different swipe gestures and feel the haptic feedback',
      color: 'bg-green-50 border-green-200'
    },
    {
      id: '3',
      title: 'Swipeable Card 3',
      description: 'Each card has customizable swipe actions',
      color: 'bg-purple-50 border-purple-200'
    }
  ];

  if (!isMobile) {
    return (
      <div className="flex items-center justify-center min-h-[400px] bg-gray-50 rounded-xl border-2 border-dashed border-gray-300">
        <div className="text-center p-8">
          <Smartphone className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">
            Mobile Interactions Demo
          </h3>
          <p className="text-gray-500 max-w-md">
            This demo showcases mobile-specific interactions like swipe gestures, 
            pull-to-refresh, and touch-optimized modals. Please view on a mobile device 
            or resize your browser to mobile width to see the interactions.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Mobile Interactions
        </h2>
        <p className="text-gray-600">
          Experience touch-optimized interactions
        </p>
      </div>

      {/* Pull to Refresh Demo */}
      <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-2">
            <RefreshCw className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">Pull to Refresh</h3>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Pull down to refresh content (Refreshed {refreshCount} times)
          </p>
        </div>
        
        <PullToRefresh onRefresh={handleRefresh} className="h-32">
          <div className="p-4 flex items-center justify-center h-full">
            <div className="text-center">
              <ArrowUp className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Pull down to refresh</p>
            </div>
          </div>
        </PullToRefresh>
      </div>

      {/* Touch Buttons Demo */}
      <div className="bg-white rounded-xl border border-gray-200 p-4">
        <div className="flex items-center gap-2 mb-4">
          <TouchIcon className="w-5 h-5 text-green-600" />
          <h3 className="font-semibold text-gray-900">Touch-Optimized Buttons</h3>
        </div>
        
        <div className="grid grid-cols-2 gap-3">
          <TouchButton variant="primary" size="md">
            Primary
          </TouchButton>
          <TouchButton variant="secondary" size="md">
            Secondary
          </TouchButton>
          <TouchButton variant="ghost" size="lg" hapticFeedback={true}>
            With Haptics
          </TouchButton>
          <TouchButton variant="danger" size="lg" loading>
            Loading
          </TouchButton>
        </div>
      </div>

      {/* Swipeable Cards Demo */}
      <div className="bg-white rounded-xl border border-gray-200 p-4">
        <div className="flex items-center gap-2 mb-4">
          <Gesture className="w-5 h-5 text-purple-600" />
          <h3 className="font-semibold text-gray-900">Swipeable Cards</h3>
        </div>
        <p className="text-sm text-gray-600 mb-4">
          Swipe actions performed: {swipeCount}
        </p>
        
        <div className="space-y-3">
          {demoCards.map((card) => (
            <SwipeableCard
              key={card.id}
              leftActions={[
                commonSwipeActions.star(() => handleSwipeAction('star')),
                commonSwipeActions.edit(() => handleSwipeAction('edit'))
              ]}
              rightActions={[
                commonSwipeActions.archive(() => handleSwipeAction('archive')),
                commonSwipeActions.delete(() => handleSwipeAction('delete'))
              ]}
              className="rounded-lg"
            >
              <div className={cn('p-4 rounded-lg border', card.color)}>
                <h4 className="font-medium text-gray-900 mb-1">{card.title}</h4>
                <p className="text-sm text-gray-600">{card.description}</p>
                <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <ArrowLeft className="w-3 h-3" />
                    <span>Star, Edit</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <ArrowRight className="w-3 h-3" />
                    <span>Archive, Delete</span>
                  </div>
                </div>
              </div>
            </SwipeableCard>
          ))}
        </div>
      </div>

      {/* Mobile Modals Demo */}
      <div className="bg-white rounded-xl border border-gray-200 p-4">
        <div className="flex items-center gap-2 mb-4">
          <Hand className="w-5 h-5 text-orange-600" />
          <h3 className="font-semibold text-gray-900">Mobile Modals</h3>
        </div>
        
        <div className="grid grid-cols-1 gap-3">
          <TouchButton
            variant="outline"
            onClick={() => {
              setModalVariant('bottom-sheet');
              setShowModal(true);
            }}
          >
            Bottom Sheet Modal
          </TouchButton>
          
          <TouchButton
            variant="outline"
            onClick={() => {
              setModalVariant('drawer');
              setShowModal(true);
            }}
          >
            Drawer Modal
          </TouchButton>
          
          <TouchButton
            variant="outline"
            onClick={() => {
              setModalVariant('full-screen');
              setShowModal(true);
            }}
          >
            Full Screen Modal
          </TouchButton>
        </div>
      </div>

      {/* Gesture Instructions */}
      <div className="bg-blue-50 rounded-xl border border-blue-200 p-4">
        <div className="flex items-center gap-2 mb-3">
          <Vibrate className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-blue-900">Gesture Guide</h3>
        </div>
        
        <div className="space-y-2 text-sm text-blue-800">
          <div className="flex items-center gap-2">
            <ArrowDown className="w-4 h-4" />
            <span>Pull down to refresh content</span>
          </div>
          <div className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            <span>Swipe left on cards for primary actions</span>
          </div>
          <div className="flex items-center gap-2">
            <ArrowRight className="w-4 h-4" />
            <span>Swipe right on cards for secondary actions</span>
          </div>
          <div className="flex items-center gap-2">
            <ArrowDown className="w-4 h-4" />
            <span>Swipe down on modals to close</span>
          </div>
        </div>
      </div>

      {/* Mobile Modal */}
      <MobileModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={`${modalVariant.charAt(0).toUpperCase() + modalVariant.slice(1).replace('-', ' ')} Modal`}
        variant={modalVariant}
        showHandle={modalVariant === 'bottom-sheet'}
        closeOnSwipe={true}
      >
        <div className="p-4 space-y-4">
          <p className="text-gray-600">
            This is a {modalVariant.replace('-', ' ')} modal optimized for mobile interactions.
          </p>
          
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Features:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Swipe to close gesture</li>
              <li>• Touch-friendly close button</li>
              <li>• Haptic feedback on interactions</li>
              <li>• Safe area padding for notched devices</li>
              <li>• Backdrop blur effect</li>
            </ul>
          </div>
          
          <div className="pt-4">
            <TouchButton
              variant="primary"
              fullWidth
              onClick={() => setShowModal(false)}
            >
              Close Modal
            </TouchButton>
          </div>
        </div>
      </MobileModal>
    </div>
  );
};

export default MobileInteractionsDemo;