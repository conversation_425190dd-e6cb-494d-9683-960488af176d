// Validation utilities

import { Project, ProjectType, ProjectStatus, Priority, Complexity } from '../types';

// Validation functions
export const validation = {
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isValidProjectName: (name: string): boolean => {
    return name.trim().length >= 3 && name.trim().length <= 100;
  },

  isValidDescription: (description: string): boolean => {
    return description.trim().length >= 10 && description.trim().length <= 500;
  },

  isValidProjectType: (type: string): type is ProjectType => {
    return ['mobile', 'web', 'form', 'ai', 'ecommerce'].includes(type);
  },

  isValidProjectStatus: (status: string): status is ProjectStatus => {
    return ['active', 'completed', 'draft', 'archived', 'error'].includes(status);
  },

  isValidPriority: (priority: string): priority is Priority => {
    return ['high', 'medium', 'low'].includes(priority);
  },

  isValidComplexity: (complexity: string): complexity is Complexity => {
    return ['simple', 'medium', 'complex'].includes(complexity);
  },

  isValidProgress: (progress: number): boolean => {
    return progress >= 0 && progress <= 100;
  }
};

// Project validation
export const validateProject = (project: Partial<Project>): string[] => {
  const errors: string[] = [];

  if (!project.name || !validation.isValidProjectName(project.name)) {
    errors.push('Project name must be between 3 and 100 characters');
  }

  if (!project.description || !validation.isValidDescription(project.description)) {
    errors.push('Description must be between 10 and 500 characters');
  }

  if (!project.type || !validation.isValidProjectType(project.type)) {
    errors.push('Invalid project type');
  }

  if (project.status && !validation.isValidProjectStatus(project.status)) {
    errors.push('Invalid project status');
  }

  if (project.priority && !validation.isValidPriority(project.priority)) {
    errors.push('Invalid priority level');
  }

  if (project.complexity && !validation.isValidComplexity(project.complexity)) {
    errors.push('Invalid complexity level');
  }

  if (project.progress !== undefined && !validation.isValidProgress(project.progress)) {
    errors.push('Progress must be between 0 and 100');
  }

  return errors;
};
