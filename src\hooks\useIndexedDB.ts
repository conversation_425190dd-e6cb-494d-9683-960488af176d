import { useState, useEffect, useCallback, useRef } from 'react';

interface IndexedDBConfig {
  databaseName: string;
  version: number;
  stores: {
    name: string;
    keyPath?: string;
    autoIncrement?: boolean;
    indexes?: {
      name: string;
      keyPath: string | string[];
      unique?: boolean;
    }[];
  }[];
}

interface UseIndexedDBResult<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  add: (item: T) => Promise<boolean>;
  update: (key: IDBValidKey, item: Partial<T>) => Promise<boolean>;
  remove: (key: IDBValidKey) => Promise<boolean>;
  get: (key: IDBValidKey) => Promise<T | null>;
  getAll: () => Promise<T[]>;
  clear: () => Promise<boolean>;
  count: () => Promise<number>;
  search: (indexName: string, query: IDBValidKey | IDBKeyRange) => Promise<T[]>;
}

/**
 * Custom hook for IndexedDB operations with automatic database setup
 */
export function useIndexedDB<T = any>(
  config: IndexedDBConfig,
  storeName: string
): UseIndexedDBResult<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dbRef = useRef<IDBDatabase | null>(null);

  // Initialize database
  const initDB = useCallback((): Promise<IDBDatabase> => {
    return new Promise((resolve, reject) => {
      if (dbRef.current) {
        resolve(dbRef.current);
        return;
      }

      const request = indexedDB.open(config.databaseName, config.version);

      request.onerror = () => {
        reject(new Error(`Failed to open database: ${request.error?.message}`));
      };

      request.onsuccess = () => {
        dbRef.current = request.result;
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create object stores
        config.stores.forEach(store => {
          if (!db.objectStoreNames.contains(store.name)) {
            const objectStore = db.createObjectStore(store.name, {
              keyPath: store.keyPath,
              autoIncrement: store.autoIncrement
            });

            // Create indexes
            store.indexes?.forEach(index => {
              objectStore.createIndex(index.name, index.keyPath, {
                unique: index.unique || false
              });
            });
          }
        });
      };
    });
  }, [config]);

  // Generic transaction helper
  const performTransaction = useCallback(async <R>(
    mode: IDBTransactionMode,
    operation: (store: IDBObjectStore) => IDBRequest<R>
  ): Promise<R> => {
    const db = await initDB();
    const transaction = db.transaction([storeName], mode);
    const store = transaction.objectStore(storeName);
    const request = operation(store);

    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(new Error(`Transaction failed: ${request.error?.message}`));
    });
  }, [initDB, storeName]);

  // Add item
  const add = useCallback(async (item: T): Promise<boolean> => {
    try {
      setLoading(true);
      await performTransaction('readwrite', store => store.add(item));
      await loadData();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add item';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [performTransaction]);

  // Update item
  const update = useCallback(async (key: IDBValidKey, updates: Partial<T>): Promise<boolean> => {
    try {
      setLoading(true);
      
      // Get existing item
      const existing = await performTransaction('readonly', store => store.get(key));
      if (!existing) {
        throw new Error('Item not found');
      }

      // Merge updates
      const updated = { ...existing, ...updates };
      await performTransaction('readwrite', store => store.put(updated));
      await loadData();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update item';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [performTransaction]);

  // Remove item
  const remove = useCallback(async (key: IDBValidKey): Promise<boolean> => {
    try {
      setLoading(true);
      await performTransaction('readwrite', store => store.delete(key));
      await loadData();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove item';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [performTransaction]);

  // Get single item
  const get = useCallback(async (key: IDBValidKey): Promise<T | null> => {
    try {
      const result = await performTransaction('readonly', store => store.get(key));
      return result || null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get item';
      setError(errorMessage);
      return null;
    }
  }, [performTransaction]);

  // Get all items
  const getAll = useCallback(async (): Promise<T[]> => {
    try {
      const result = await performTransaction('readonly', store => store.getAll());
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get all items';
      setError(errorMessage);
      return [];
    }
  }, [performTransaction]);

  // Clear all items
  const clear = useCallback(async (): Promise<boolean> => {
    try {
      setLoading(true);
      await performTransaction('readwrite', store => store.clear());
      setData([]);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear items';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [performTransaction]);

  // Count items
  const count = useCallback(async (): Promise<number> => {
    try {
      const result = await performTransaction('readonly', store => store.count());
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to count items';
      setError(errorMessage);
      return 0;
    }
  }, [performTransaction]);

  // Search using index
  const search = useCallback(async (indexName: string, query: IDBValidKey | IDBKeyRange): Promise<T[]> => {
    try {
      const db = await initDB();
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const index = store.index(indexName);
      const request = index.getAll(query);

      return new Promise((resolve, reject) => {
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(new Error(`Search failed: ${request.error?.message}`));
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search items';
      setError(errorMessage);
      return [];
    }
  }, [initDB, storeName]);

  // Load data
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const allData = await getAll();
      setData(allData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load data';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [getAll]);

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (dbRef.current) {
        dbRef.current.close();
        dbRef.current = null;
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    add,
    update,
    remove,
    get,
    getAll,
    clear,
    count,
    search
  };
}

/**
 * Predefined hook for project data storage
 */
export function useProjectStorage() {
  const config: IndexedDBConfig = {
    databaseName: 'AppGeneratorDB',
    version: 1,
    stores: [
      {
        name: 'projects',
        keyPath: 'id',
        indexes: [
          { name: 'status', keyPath: 'status' },
          { name: 'type', keyPath: 'type' },
          { name: 'lastModified', keyPath: 'lastModified' },
          { name: 'tags', keyPath: 'tags', unique: false }
        ]
      },
      {
        name: 'exports',
        keyPath: 'id',
        indexes: [
          { name: 'projectId', keyPath: 'projectId' },
          { name: 'createdAt', keyPath: 'createdAt' }
        ]
      }
    ]
  };

  return useIndexedDB(config, 'projects');
}

/**
 * Hook for managing storage quota and usage
 */
export function useStorageQuota() {
  const [quota, setQuota] = useState<number>(0);
  const [usage, setUsage] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  const checkQuota = useCallback(async () => {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        setLoading(true);
        const estimate = await navigator.storage.estimate();
        setQuota(estimate.quota || 0);
        setUsage(estimate.usage || 0);
      } catch (error) {
        console.error('Failed to check storage quota:', error);
      } finally {
        setLoading(false);
      }
    }
  }, []);

  useEffect(() => {
    checkQuota();
  }, [checkQuota]);

  return {
    quota,
    usage,
    loading,
    usagePercentage: quota > 0 ? (usage / quota) * 100 : 0,
    availableSpace: quota - usage,
    checkQuota
  };
}