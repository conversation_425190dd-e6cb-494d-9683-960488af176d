import React from 'react';

const AnimatedBackground: React.FC = () => {
  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      {/* Dark base background */}
      <div className="absolute inset-0 bg-gray-950" />
      
      {/* Main spectrum gradient - large blurred colorful light */}
      <div className="absolute -bottom-32 -right-32 w-[800px] h-[600px] opacity-80">
        <div 
          className="w-full h-full rounded-full blur-[120px] animate-pulse-slow"
          style={{
            background: `radial-gradient(ellipse at center, 
              #ff0080 0%,
              #ff4000 15%,
              #ffaa00 30%,
              #00ff80 45%,
              #0080ff 60%,
              #8000ff 75%,
              #ff0080 90%,
              transparent 100%
            )`
          }}
        />
      </div>
      
      {/* Secondary smaller gradient for depth */}
      <div className="absolute bottom-0 right-0 w-[400px] h-[300px] opacity-60">
        <div 
          className="w-full h-full rounded-full blur-[80px] animate-pulse-slower"
          style={{
            background: `radial-gradient(circle at center,
              #ff6b9d 0%,
              #4ecdc4 50%,
              transparent 100%
            )`
          }}
        />
      </div>
      
      {/* Subtle top accent */}
      <div className="absolute top-20 right-20 w-[200px] h-[200px] opacity-30">
        <div 
          className="w-full h-full rounded-full blur-[60px] animate-float-gentle"
          style={{
            background: `radial-gradient(circle at center,
              #a855f7 0%,
              #3b82f6 50%,
              transparent 100%
            )`
          }}
        />
      </div>
      
      {/* Subtle grid overlay for texture */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div 
          className="absolute inset-0"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '100px 100px'
          }} 
        />
      </div>
      
      <style jsx>{`
        @keyframes pulse-slow {
          0%, 100% { 
            opacity: 0.6; 
            transform: scale(1);
          }
          50% { 
            opacity: 1; 
            transform: scale(1.1);
          }
        }
        
        @keyframes pulse-slower {
          0%, 100% { 
            opacity: 0.4; 
            transform: scale(0.9);
          }
          50% { 
            opacity: 0.8; 
            transform: scale(1.2);
          }
        }
        
        @keyframes float-gentle {
          0%, 100% { 
            transform: translateY(0px) scale(1);
          }
          50% { 
            transform: translateY(-20px) scale(1.1);
          }
        }
        
        .animate-pulse-slow {
          animation: pulse-slow 8s ease-in-out infinite;
        }
        
        .animate-pulse-slower {
          animation: pulse-slower 12s ease-in-out infinite 2s;
        }
        
        .animate-float-gentle {
          animation: float-gentle 15s ease-in-out infinite 4s;
        }
      `}</style>
    </div>
  );
};

export default AnimatedBackground;