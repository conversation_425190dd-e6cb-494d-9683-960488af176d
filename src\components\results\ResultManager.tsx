// Result management component with pagination and virtual scrolling

import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Grid, List, MoreHorizontal } from 'lucide-react';

export interface ResultManagerProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  renderEmpty: () => React.ReactNode;
  itemsPerPage?: number;
  enableVirtualScrolling?: boolean;
  enablePagination?: boolean;
  viewMode?: 'grid' | 'list';
  onViewModeChange?: (mode: 'grid' | 'list') => void;
  className?: string;
  itemHeight?: number; // For virtual scrolling
  containerHeight?: number; // For virtual scrolling
}

function ResultManager<T>({
  items,
  renderItem,
  renderEmpty,
  itemsPerPage = 20,
  enableVirtualScrolling = false,
  enablePagination = true,
  viewMode = 'list',
  onViewModeChange,
  className = "",
  itemHeight = 120,
  containerHeight = 600
}: ResultManagerProps<T>) {
  const [currentPage, setCurrentPage] = useState(1);
  const [scrollTop, setScrollTop] = useState(0);

  // Calculate pagination
  const totalPages = Math.ceil(items.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  // Get items for current page (if pagination is enabled)
  const paginatedItems = useMemo(() => {
    if (!enablePagination) return items;
    return items.slice(startIndex, endIndex);
  }, [items, startIndex, endIndex, enablePagination]);

  // Virtual scrolling calculations
  const visibleItems = useMemo(() => {
    if (!enableVirtualScrolling) return paginatedItems;

    const visibleCount = Math.ceil(containerHeight / itemHeight) + 2; // Buffer
    const startIdx = Math.floor(scrollTop / itemHeight);
    const endIdx = Math.min(startIdx + visibleCount, paginatedItems.length);

    return paginatedItems.slice(startIdx, endIdx).map((item, index) => ({
      item,
      index: startIdx + index,
      top: (startIdx + index) * itemHeight
    }));
  }, [paginatedItems, scrollTop, itemHeight, containerHeight, enableVirtualScrolling]);

  // Handle scroll for virtual scrolling
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    if (enableVirtualScrolling) {
      setScrollTop(e.currentTarget.scrollTop);
    }
  }, [enableVirtualScrolling]);

  // Pagination handlers
  const goToPage = useCallback((page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  }, [totalPages]);

  const goToPreviousPage = useCallback(() => {
    goToPage(currentPage - 1);
  }, [currentPage, goToPage]);

  const goToNextPage = useCallback(() => {
    goToPage(currentPage + 1);
  }, [currentPage, goToPage]);

  // Get page numbers for pagination
  const getPageNumbers = useCallback(() => {
    const pages: (number | string)[] = [];
    const maxVisible = 7;

    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);

      if (currentPage > 4) {
        pages.push('...');
      }

      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (currentPage < totalPages - 3) {
        pages.push('...');
      }

      pages.push(totalPages);
    }

    return pages;
  }, [currentPage, totalPages]);

  // Show empty state if no items
  if (items.length === 0) {
    return <div className={className}>{renderEmpty()}</div>;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with view controls and result count */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-400">
          {enablePagination ? (
            <>
              Showing {startIndex + 1}-{Math.min(endIndex, items.length)} of {items.length} results
            </>
          ) : (
            <>
              {items.length} result{items.length !== 1 ? 's' : ''}
            </>
          )}
        </div>

        {onViewModeChange && (
          <div className="flex items-center gap-1 bg-gray-800/50 rounded-lg p-1">
            <button
              onClick={() => onViewModeChange('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
              }`}
              aria-label="List view"
            >
              <List className="w-4 h-4" />
            </button>
            <button
              onClick={() => onViewModeChange('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
              }`}
              aria-label="Grid view"
            >
              <Grid className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Results container */}
      <div
        className={`relative ${enableVirtualScrolling ? 'overflow-auto' : ''}`}
        style={enableVirtualScrolling ? { height: containerHeight } : undefined}
        onScroll={handleScroll}
      >
        {enableVirtualScrolling ? (
          // Virtual scrolling container
          <div style={{ height: paginatedItems.length * itemHeight, position: 'relative' }}>
            <AnimatePresence mode="popLayout">
              {visibleItems.map(({ item, index, top }) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2, delay: index * 0.02 }}
                  style={{
                    position: 'absolute',
                    top,
                    left: 0,
                    right: 0,
                    height: itemHeight
                  }}
                >
                  {renderItem(item, index)}
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        ) : (
          // Regular container
          <div className={`space-y-3 ${viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' : ''}`}>
            <AnimatePresence mode="popLayout">
              {paginatedItems.map((item, index) => (
                <motion.div
                  key={startIndex + index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  {renderItem(item, startIndex + index)}
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Pagination */}
      {enablePagination && totalPages > 1 && (
        <div className="flex items-center justify-center gap-2 pt-4">
          <button
            onClick={goToPreviousPage}
            disabled={currentPage === 1}
            className="flex items-center gap-2 px-3 py-2 text-sm text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ChevronLeft className="w-4 h-4" />
            Previous
          </button>

          <div className="flex items-center gap-1">
            {getPageNumbers().map((page, index) => (
              <React.Fragment key={index}>
                {page === '...' ? (
                  <span className="px-3 py-2 text-gray-500">
                    <MoreHorizontal className="w-4 h-4" />
                  </span>
                ) : (
                  <button
                    onClick={() => goToPage(page as number)}
                    className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                      currentPage === page
                        ? 'bg-blue-500 text-white'
                        : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                    }`}
                  >
                    {page}
                  </button>
                )}
              </React.Fragment>
            ))}
          </div>

          <button
            onClick={goToNextPage}
            disabled={currentPage === totalPages}
            className="flex items-center gap-2 px-3 py-2 text-sm text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Next
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  );
}

export default ResultManager;