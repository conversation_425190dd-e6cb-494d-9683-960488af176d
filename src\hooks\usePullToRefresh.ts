import { useRef, useCallback, useEffect, useState } from 'react';
import { useMemoryManager } from './useMemoryManager';

interface PullToRefreshOptions {
  onRefresh: () => Promise<void> | void;
  threshold?: number;
  maxPullDistance?: number;
  enabled?: boolean;
  refreshingText?: string;
  pullText?: string;
  releaseText?: string;
}

interface PullState {
  isPulling: boolean;
  isRefreshing: boolean;
  pullDistance: number;
  canRefresh: boolean;
}

export const usePullToRefresh = (options: PullToRefreshOptions) => {
  const {
    onRefresh,
    threshold = 80,
    maxPullDistance = 120,
    enabled = true,
    refreshingText = 'Refreshing...',
    pullText = 'Pull to refresh',
    releaseText = 'Release to refresh'
  } = options;

  const [pullState, setPullState] = useState<PullState>({
    isPulling: false,
    isRefreshing: false,
    pullDistance: 0,
    canRefresh: false
  });

  const touchStart = useRef<{ y: number; scrollTop: number } | null>(null);
  const elementRef = useRef<HTMLElement | null>(null);
  const isRefreshingRef = useRef(false);
  const { addEventListener } = useMemoryManager();

  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (!enabled || isRefreshingRef.current) return;

    const element = elementRef.current;
    if (!element) return;

    // Only allow pull-to-refresh when at the top of the scroll container
    const scrollTop = element.scrollTop || window.pageYOffset;
    if (scrollTop > 0) return;

    const touch = e.touches[0];
    touchStart.current = {
      y: touch.clientY,
      scrollTop
    };
  }, [enabled]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!enabled || !touchStart.current || isRefreshingRef.current) return;

    const element = elementRef.current;
    if (!element) return;

    const touch = e.touches[0];
    const deltaY = touch.clientY - touchStart.current.y;
    
    // Only handle downward pulls when at the top
    if (deltaY > 0 && (element.scrollTop || window.pageYOffset) === 0) {
      e.preventDefault();
      
      const pullDistance = Math.min(deltaY * 0.5, maxPullDistance);
      const canRefresh = pullDistance >= threshold;

      setPullState(prev => ({
        ...prev,
        isPulling: true,
        pullDistance,
        canRefresh
      }));
    }
  }, [enabled, threshold, maxPullDistance]);

  const handleTouchEnd = useCallback(async () => {
    if (!enabled || !touchStart.current || isRefreshingRef.current) return;

    const { canRefresh } = pullState;

    if (canRefresh) {
      isRefreshingRef.current = true;
      setPullState(prev => ({
        ...prev,
        isPulling: false,
        isRefreshing: true,
        pullDistance: threshold
      }));

      try {
        await onRefresh();
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        isRefreshingRef.current = false;
        setPullState({
          isPulling: false,
          isRefreshing: false,
          pullDistance: 0,
          canRefresh: false
        });
      }
    } else {
      setPullState({
        isPulling: false,
        isRefreshing: false,
        pullDistance: 0,
        canRefresh: false
      });
    }

    touchStart.current = null;
  }, [enabled, pullState.canRefresh, onRefresh, threshold]);

  const attachToElement = useCallback((element: HTMLElement | null) => {
    elementRef.current = element;

    if (element && enabled) {
      addEventListener(element, 'touchstart', handleTouchStart, { passive: false });
      addEventListener(element, 'touchmove', handleTouchMove, { passive: false });
      addEventListener(element, 'touchend', handleTouchEnd, { passive: true });
    }
  }, [enabled, handleTouchStart, handleTouchMove, handleTouchEnd, addEventListener]);

  const getPullIndicatorText = () => {
    if (pullState.isRefreshing) return refreshingText;
    if (pullState.canRefresh) return releaseText;
    return pullText;
  };

  const getPullProgress = () => {
    return Math.min(pullState.pullDistance / threshold, 1);
  };

  return {
    attachToElement,
    pullState,
    getPullIndicatorText,
    getPullProgress,
    pullDistance: pullState.pullDistance,
    isPulling: pullState.isPulling,
    isRefreshing: pullState.isRefreshing,
    canRefresh: pullState.canRefresh
  };
};