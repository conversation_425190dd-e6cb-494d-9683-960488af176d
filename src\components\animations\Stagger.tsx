import React from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { createStaggerVariant } from '../../utils/animations';

interface StaggerProps extends Omit<HTMLMotionProps<'div'>, 'variants'> {
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
  staggerDelay?: number;
  childVariants?: any;
}

export const Stagger: React.FC<StaggerProps> = ({
  children,
  as = 'div',
  staggerDelay = 0.1,
  childVariants,
  ...props
}) => {
  const variants = createStaggerVariant(staggerDelay);
  const MotionComponent = motion[as as keyof typeof motion] as any;

  return (
    <MotionComponent
      variants={variants}
      initial="initial"
      animate="animate"
      {...props}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          variants={childVariants || {
            initial: { opacity: 0, y: 20 },
            animate: { opacity: 1, y: 0 }
          }}
        >
          {child}
        </motion.div>
      ))}
    </MotionComponent>
  );
};

export default Stagger;