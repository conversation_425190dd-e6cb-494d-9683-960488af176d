import React, { useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence, PanInfo } from 'framer-motion';
import { X, ChevronDown } from 'lucide-react';
import { cn } from '../../utils/helpers';
import { useSwipeGestures } from '../../hooks/useSwipeGestures';
import { useTouchFeedback } from '../../hooks/useTouchFeedback';
import Button from './Button';

interface MobileModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  variant?: 'bottom-sheet' | 'full-screen' | 'drawer';
  showHandle?: boolean;
  showCloseButton?: boolean;
  closeOnBackdropClick?: boolean;
  closeOnSwipe?: boolean;
  preventScroll?: boolean;
  className?: string;
  overlayClassName?: string;
  contentClassName?: string;
  maxHeight?: string;
}

const backdropVariants = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1,
    transition: { duration: 0.2, ease: 'easeOut' }
  },
  exit: { 
    opacity: 0,
    transition: { duration: 0.15, ease: 'easeIn' }
  }
};

const modalVariants = {
  'bottom-sheet': {
    hidden: { 
      y: '100%',
      opacity: 0
    },
    visible: { 
      y: 0,
      opacity: 1,
      transition: { 
        duration: 0.3, 
        ease: 'easeOut',
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: { 
      y: '100%',
      opacity: 0,
      transition: { duration: 0.25, ease: 'easeIn' }
    }
  },
  'full-screen': {
    hidden: { 
      scale: 0.95,
      opacity: 0
    },
    visible: { 
      scale: 1,
      opacity: 1,
      transition: { 
        duration: 0.3, 
        ease: 'easeOut'
      }
    },
    exit: { 
      scale: 0.95,
      opacity: 0,
      transition: { duration: 0.2, ease: 'easeIn' }
    }
  },
  'drawer': {
    hidden: { 
      x: '100%',
      opacity: 0
    },
    visible: { 
      x: 0,
      opacity: 1,
      transition: { 
        duration: 0.3, 
        ease: 'easeOut',
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: { 
      x: '100%',
      opacity: 0,
      transition: { duration: 0.25, ease: 'easeIn' }
    }
  }
};

const MobileModal: React.FC<MobileModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  variant = 'bottom-sheet',
  showHandle = true,
  showCloseButton = true,
  closeOnBackdropClick = true,
  closeOnSwipe = true,
  preventScroll = true,
  className,
  overlayClassName,
  contentClassName,
  maxHeight = '90vh'
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const { triggerHapticFeedback } = useTouchFeedback();

  // Handle swipe to close
  const { attachToElement: attachSwipeGestures } = useSwipeGestures({
    onSwipeDown: variant === 'bottom-sheet' && closeOnSwipe ? () => {
      triggerHapticFeedback('light');
      onClose();
    } : undefined,
    onSwipeRight: variant === 'drawer' && closeOnSwipe ? () => {
      triggerHapticFeedback('light');
      onClose();
    } : undefined,
    threshold: 100,
    enabled: closeOnSwipe
  });

  // Handle pan gesture for bottom sheet
  const handlePan = useCallback((event: any, info: PanInfo) => {
    if (variant !== 'bottom-sheet' || !closeOnSwipe) return;

    const { offset, velocity } = info;
    
    // Close if dragged down significantly or with high velocity
    if (offset.y > 150 || (offset.y > 50 && velocity.y > 500)) {
      triggerHapticFeedback('medium');
      onClose();
    }
  }, [variant, closeOnSwipe, onClose, triggerHapticFeedback]);

  const handleBackdropClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget && closeOnBackdropClick) {
      triggerHapticFeedback('light');
      onClose();
    }
  }, [closeOnBackdropClick, onClose, triggerHapticFeedback]);

  const handleCloseClick = useCallback(() => {
    triggerHapticFeedback('light');
    onClose();
  }, [onClose, triggerHapticFeedback]);

  // Attach swipe gestures
  useEffect(() => {
    if (isOpen && modalRef.current && closeOnSwipe) {
      attachSwipeGestures(modalRef.current);
    }
  }, [isOpen, attachSwipeGestures, closeOnSwipe]);

  // Prevent scroll when modal is open
  useEffect(() => {
    if (isOpen && preventScroll) {
      const originalStyle = window.getComputedStyle(document.body).overflow;
      document.body.style.overflow = 'hidden';
      
      // Add safe area padding for iOS
      if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        document.body.style.paddingBottom = 'env(safe-area-inset-bottom)';
      }

      return () => {
        document.body.style.overflow = originalStyle;
        document.body.style.paddingBottom = '';
      };
    }
  }, [isOpen, preventScroll]);

  // Focus management
  useEffect(() => {
    if (isOpen && modalRef.current) {
      const focusableElements = modalRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0] as HTMLElement;
      
      setTimeout(() => {
        firstElement?.focus();
      }, 100);
    }
  }, [isOpen]);

  const getContainerClasses = () => {
    const base = 'fixed inset-0 z-50 flex';
    
    switch (variant) {
      case 'bottom-sheet':
        return `${base} items-end justify-center`;
      case 'full-screen':
        return `${base} items-center justify-center p-4`;
      case 'drawer':
        return `${base} items-center justify-end`;
      default:
        return `${base} items-end justify-center`;
    }
  };

  const getModalClasses = () => {
    const base = 'relative w-full';
    
    switch (variant) {
      case 'bottom-sheet':
        return `${base} max-w-lg mx-auto`;
      case 'full-screen':
        return `${base} max-w-full h-full`;
      case 'drawer':
        return `${base} max-w-sm h-full`;
      default:
        return `${base} max-w-lg mx-auto`;
    }
  };

  const getContentClasses = () => {
    const base = 'bg-white shadow-2xl overflow-hidden';
    
    switch (variant) {
      case 'bottom-sheet':
        return `${base} rounded-t-3xl`;
      case 'full-screen':
        return `${base} rounded-2xl h-full`;
      case 'drawer':
        return `${base} rounded-l-2xl h-full`;
      default:
        return `${base} rounded-t-3xl`;
    }
  };

  return (
    <AnimatePresence mode="wait">
      {isOpen && (
        <div className={getContainerClasses()}>
          {/* Backdrop */}
          <motion.div
            variants={backdropVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className={cn(
              'absolute inset-0 bg-black/60 backdrop-blur-sm',
              overlayClassName
            )}
            onClick={handleBackdropClick}
          />
          
          {/* Modal */}
          <motion.div
            ref={modalRef}
            variants={modalVariants[variant]}
            initial="hidden"
            animate="visible"
            exit="exit"
            drag={variant === 'bottom-sheet' && closeOnSwipe ? 'y' : false}
            dragConstraints={{ top: 0, bottom: 0 }}
            dragElastic={{ top: 0, bottom: 0.2 }}
            onPanEnd={handlePan}
            className={getModalClasses()}
            role="dialog"
            aria-modal="true"
            aria-labelledby={title ? 'mobile-modal-title' : undefined}
            style={{
              maxHeight: variant === 'bottom-sheet' ? maxHeight : undefined
            }}
          >
            <div className={cn(getContentClasses(), contentClassName)}>
              {/* Handle for bottom sheet */}
              {showHandle && variant === 'bottom-sheet' && (
                <div className="flex justify-center pt-3 pb-2">
                  <div className="w-12 h-1.5 bg-gray-300 rounded-full touch-target" />
                </div>
              )}

              {/* Header */}
              {(title || showCloseButton) && (
                <div className="flex items-center justify-between p-4 border-b border-gray-200">
                  {title && (
                    <h2 id="mobile-modal-title" className="text-lg font-semibold text-gray-900 flex-1">
                      {title}
                    </h2>
                  )}
                  {showCloseButton && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCloseClick}
                      className="ml-auto touch-target min-w-[44px] min-h-[44px] flex items-center justify-center"
                      aria-label="Close modal"
                    >
                      {variant === 'bottom-sheet' ? (
                        <ChevronDown className="h-5 w-5" />
                      ) : (
                        <X className="h-5 w-5" />
                      )}
                    </Button>
                  )}
                </div>
              )}
              
              {/* Content */}
              <div 
                ref={contentRef}
                className={cn(
                  'overflow-auto',
                  variant === 'full-screen' ? 'flex-1' : 'max-h-[70vh]',
                  className
                )}
                style={{
                  // Add safe area padding for iOS
                  paddingBottom: 'env(safe-area-inset-bottom)'
                }}
              >
                {children}
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default MobileModal;