// Performance monitoring and profiling utilities
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  private observers: PerformanceObserver[] = [];
  private isEnabled: boolean = process.env.NODE_ENV === 'development';

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  constructor() {
    if (this.isEnabled && typeof window !== 'undefined') {
      this.initializeObservers();
    }
  }

  private initializeObservers() {
    try {
      // Observe long tasks
      if ('PerformanceObserver' in window) {
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) {
              console.warn(`Long task detected: ${entry.duration}ms`, entry);
              this.recordMetric('longTasks', entry.duration);
            }
          }
        });
        longTaskObserver.observe({ entryTypes: ['longtask'] });
        this.observers.push(longTaskObserver);

        // Observe layout shifts
        const layoutShiftObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const layoutShiftEntry = entry as PerformanceEntry & { value?: number };
            if (layoutShiftEntry.value && layoutShiftEntry.value > 0.1) {
              console.warn(`Layout shift detected: ${layoutShiftEntry.value}`, entry);
              this.recordMetric('layoutShifts', layoutShiftEntry.value);
            }
          }
        });
        layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(layoutShiftObserver);

        // Observe largest contentful paint
        const lcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            console.log(`LCP: ${entry.startTime}ms`, entry);
            this.recordMetric('lcp', entry.startTime);
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      }
    } catch (error) {
      console.warn('Failed to initialize performance observers:', error);
    }
  }

  recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }

  getMetrics(name: string): number[] {
    return this.metrics.get(name) || [];
  }

  getAverageMetric(name: string): number {
    const values = this.getMetrics(name);
    return values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
  }

  measureRenderTime<T>(componentName: string, renderFn: () => T): T {
    if (!this.isEnabled) return renderFn();

    const startTime = performance.now();
    const result = renderFn();
    const endTime = performance.now();
    
    const renderTime = endTime - startTime;
    this.recordMetric(`render_${componentName}`, renderTime);
    
    if (renderTime > 16) { // More than one frame
      console.warn(`Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
    }
    
    return result;
  }

  measureAsyncOperation<T>(operationName: string, operation: () => Promise<T>): Promise<T> {
    if (!this.isEnabled) return operation();

    const startTime = performance.now();
    return operation().finally(() => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.recordMetric(`async_${operationName}`, duration);
      
      if (duration > 100) {
        console.warn(`Slow async operation detected in ${operationName}: ${duration.toFixed(2)}ms`);
      }
    });
  }

  logPerformanceReport() {
    if (!this.isEnabled) return;

    console.group('Performance Report');
    
    for (const [name, values] of this.metrics.entries()) {
      const avg = this.getAverageMetric(name);
      const max = Math.max(...values);
      const min = Math.min(...values);
      
      console.log(`${name}:`, {
        average: `${avg.toFixed(2)}ms`,
        max: `${max.toFixed(2)}ms`,
        min: `${min.toFixed(2)}ms`,
        count: values.length,
      });
    }
    
    console.groupEnd();
  }

  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }
}

// React performance utilities
export function measureComponentRender(componentName: string) {
  return function <P extends object>(
    WrappedComponent: React.ComponentType<P>
  ): React.ComponentType<P> {
    const MeasuredComponent = React.forwardRef<any, P>((props, ref) => {
      const monitor = PerformanceMonitor.getInstance();
      
      return monitor.measureRenderTime(componentName, () => {
        return React.createElement(WrappedComponent, { ...props, ref });
      });
    });

    MeasuredComponent.displayName = `Measured(${componentName})`;
    return MeasuredComponent;
  };
}

// Hook for measuring hook performance
export function useMeasuredEffect(
  effect: React.EffectCallback,
  deps: React.DependencyList | undefined,
  hookName: string
) {
  React.useEffect(() => {
    const monitor = PerformanceMonitor.getInstance();
    return monitor.measureRenderTime(`effect_${hookName}`, effect);
  }, deps);
}

// Memory usage monitoring
export function measureMemoryUsage(label: string) {
  if (process.env.NODE_ENV === 'development' && 'memory' in performance) {
    const memInfo = (performance as any).memory;
    console.log(`Memory usage (${label}):`, {
      used: `${(memInfo.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      total: `${(memInfo.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      limit: `${(memInfo.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
    });
  }
}

// Bundle size analyzer
export function analyzeBundleSize() {
  if (process.env.NODE_ENV === 'development') {
    // This would be enhanced with actual bundle analysis
    console.group('Bundle Analysis');
    console.log('Check Network tab in DevTools for chunk sizes');
    console.log('Use webpack-bundle-analyzer for detailed analysis');
    console.groupEnd();
  }
}

// Performance budget checker
export interface PerformanceBudget {
  maxRenderTime: number;
  maxAsyncTime: number;
  maxMemoryUsage: number;
  maxBundleSize: number;
}

export function checkPerformanceBudget(budget: PerformanceBudget) {
  const monitor = PerformanceMonitor.getInstance();
  const violations: string[] = [];

  // Check render times
  for (const [name, values] of monitor.metrics.entries()) {
    if (name.startsWith('render_')) {
      const avg = monitor.getAverageMetric(name);
      if (avg > budget.maxRenderTime) {
        violations.push(`${name} average render time: ${avg.toFixed(2)}ms (budget: ${budget.maxRenderTime}ms)`);
      }
    }
    
    if (name.startsWith('async_')) {
      const avg = monitor.getAverageMetric(name);
      if (avg > budget.maxAsyncTime) {
        violations.push(`${name} average async time: ${avg.toFixed(2)}ms (budget: ${budget.maxAsyncTime}ms)`);
      }
    }
  }

  if (violations.length > 0) {
    console.warn('Performance budget violations:', violations);
  }

  return violations;
}

// Global performance monitor instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// React import
import React from 'react';