// Advanced search input with suggestions and history

import React, { useRef, useEffect, useState } from 'react';
import { Search, X, Clock, Hash, FileText, History, TrendingUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { SearchSuggestion, SearchHistoryItem } from '../../services/searchService';

interface AdvancedSearchInputProps {
  query: string;
  setQuery: (query: string) => void;
  suggestions: SearchSuggestion[];
  history: SearchHistoryItem[];
  showSuggestions: boolean;
  setShowSuggestions: (show: boolean) => void;
  isSearching: boolean;
  onClearSearch: () => void;
  onSelectSuggestion: (suggestion: string) => void;
  onRemoveFromHistory: (id: string) => void;
  placeholder?: string;
  className?: string;
}

const AdvancedSearchInput: React.FC<AdvancedSearchInputProps> = ({
  query,
  setQuery,
  suggestions,
  history,
  showSuggestions,
  setShowSuggestions,
  isSearching,
  onClearSearch,
  onSelectSuggestion,
  onRemoveFromHistory,
  placeholder = "Search projects...",
  className = ""
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [focusedIndex, setFocusedIndex] = useState(-1);

  // All suggestions and history combined for keyboard navigation
  const allItems = [...suggestions, ...history];

  // Handle click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [setShowSuggestions]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || allItems.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev => (prev + 1) % allItems.length);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => prev <= 0 ? allItems.length - 1 : prev - 1);
        break;
      case 'Enter':
        e.preventDefault();
        if (focusedIndex >= 0 && focusedIndex < allItems.length) {
          const item = allItems[focusedIndex];
          const text = 'query' in item ? item.query : item.text;
          onSelectSuggestion(text);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setFocusedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setFocusedIndex(-1);
    
    if (value.trim()) {
      setShowSuggestions(true);
    }
  };

  // Handle input focus
  const handleInputFocus = () => {
    if (query.trim() || suggestions.length > 0 || history.length > 0) {
      setShowSuggestions(true);
    }
  };

  // Get icon for suggestion type
  const getSuggestionIcon = (type: SearchSuggestion['type']) => {
    switch (type) {
      case 'history':
        return <Clock className="w-3 h-3" />;
      case 'tag':
        return <Hash className="w-3 h-3" />;
      case 'name':
        return <FileText className="w-3 h-3" />;
      case 'description':
        return <FileText className="w-3 h-3" />;
      default:
        return <Search className="w-3 h-3" />;
    }
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          className="w-full pl-10 pr-10 py-2.5 bg-gray-800/50 border border-gray-700/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
          aria-label="Search projects"
          aria-expanded={showSuggestions}
          aria-haspopup="listbox"
          role="combobox"
        />
        
        {/* Loading indicator */}
        {isSearching && (
          <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
          </div>
        )}
        
        {/* Clear button */}
        {query && (
          <button
            onClick={onClearSearch}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
            aria-label="Clear search"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Suggestions Dropdown */}
      <AnimatePresence>
        {showSuggestions && (suggestions.length > 0 || history.length > 0) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-gray-800/95 backdrop-blur-sm border border-gray-700/50 rounded-xl shadow-xl z-50 max-h-80 overflow-y-auto"
            role="listbox"
          >
            {/* Suggestions Section */}
            {suggestions.length > 0 && (
              <div className="p-2">
                <div className="flex items-center gap-2 px-3 py-2 text-xs font-medium text-gray-400 uppercase tracking-wide">
                  <TrendingUp className="w-3 h-3" />
                  Suggestions
                </div>
                {suggestions.map((suggestion, index) => (
                  <button
                    key={`suggestion-${index}`}
                    onClick={() => onSelectSuggestion(suggestion.text)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      focusedIndex === index
                        ? 'bg-blue-500/20 text-blue-300'
                        : 'hover:bg-gray-700/50 text-gray-300'
                    }`}
                    role="option"
                    aria-selected={focusedIndex === index}
                  >
                    <span className="text-gray-400">
                      {getSuggestionIcon(suggestion.type)}
                    </span>
                    <span className="flex-1">{suggestion.text}</span>
                    {suggestion.count && (
                      <span className="text-xs text-gray-500 bg-gray-700/50 px-2 py-0.5 rounded">
                        {suggestion.count}
                      </span>
                    )}
                  </button>
                ))}
              </div>
            )}

            {/* History Section */}
            {history.length > 0 && (
              <div className="p-2 border-t border-gray-700/30">
                <div className="flex items-center gap-2 px-3 py-2 text-xs font-medium text-gray-400 uppercase tracking-wide">
                  <History className="w-3 h-3" />
                  Recent Searches
                </div>
                {history.map((item, index) => {
                  const adjustedIndex = suggestions.length + index;
                  return (
                    <div
                      key={item.id}
                      className={`flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${
                        focusedIndex === adjustedIndex
                          ? 'bg-blue-500/20'
                          : 'hover:bg-gray-700/50'
                      }`}
                    >
                      <button
                        onClick={() => onSelectSuggestion(item.query)}
                        className="flex-1 flex items-center gap-3 text-left"
                        role="option"
                        aria-selected={focusedIndex === adjustedIndex}
                      >
                        <span className="text-gray-400">
                          <Clock className="w-3 h-3" />
                        </span>
                        <span className="flex-1 text-gray-300">{item.query}</span>
                        <span className="text-xs text-gray-500">
                          {item.resultCount} results
                        </span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onRemoveFromHistory(item.id);
                        }}
                        className="p-1 text-gray-500 hover:text-red-400 transition-colors"
                        aria-label={`Remove "${item.query}" from history`}
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Empty state */}
            {suggestions.length === 0 && history.length === 0 && query.trim() && (
              <div className="p-4 text-center text-gray-500">
                <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No suggestions available</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AdvancedSearchInput;