// Export and import service for app projects
import { Project } from '../types/project';
import { GeneratedApp } from '../types/api';

export type ExportFormat = 'json' | 'pdf' | 'markdown' | 'csv';

export interface ExportOptions {
  format: ExportFormat;
  includeMarketAnalysis?: boolean;
  includeTechStack?: boolean;
  includeFeatures?: boolean;
  includeTimeline?: boolean;
  customFields?: string[];
}

export interface ExportResult {
  success: boolean;
  data?: string | Blob;
  filename: string;
  error?: string;
}

export interface ImportResult {
  success: boolean;
  projects?: Project[];
  apps?: GeneratedApp[];
  error?: string;
  warnings?: string[];
}

export class ExportService {
  static async exportProject(project: Project, options: ExportOptions): Promise<ExportResult> {
    try {
      switch (options.format) {
        case 'json':
          return this.exportAsJSON(project, options);
        case 'pdf':
          return this.exportAsPDF(project, options);
        case 'markdown':
          return this.exportAsMarkdown(project, options);
        case 'csv':
          return this.exportAsCSV(project, options);
        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        error: error instanceof Error ? error.message : 'Export failed'
      };
    }
  }

  static async exportMultipleProjects(projects: Project[], options: ExportOptions): Promise<ExportResult> {
    try {
      switch (options.format) {
        case 'json':
          return this.exportMultipleAsJSON(projects, options);
        case 'csv':
          return this.exportMultipleAsCSV(projects, options);
        case 'markdown':
          return this.exportMultipleAsMarkdown(projects, options);
        default:
          throw new Error(`Batch export not supported for format: ${options.format}`);
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        error: error instanceof Error ? error.message : 'Batch export failed'
      };
    }
  }

  static async exportGeneratedApp(app: GeneratedApp, options: ExportOptions): Promise<ExportResult> {
    try {
      switch (options.format) {
        case 'json':
          return this.exportAppAsJSON(app, options);
        case 'pdf':
          return this.exportAppAsPDF(app, options);
        case 'markdown':
          return this.exportAppAsMarkdown(app, options);
        default:
          throw new Error(`Unsupported export format for generated app: ${options.format}`);
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        error: error instanceof Error ? error.message : 'App export failed'
      };
    }
  }

  static async importProjects(file: File): Promise<ImportResult> {
    try {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const fileContent = await this.readFileContent(file);

      switch (fileExtension) {
        case 'json':
          return this.importFromJSON(fileContent);
        case 'csv':
          return this.importFromCSV(fileContent);
        default:
          throw new Error(`Unsupported import format: ${fileExtension}`);
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Import failed'
      };
    }
  }

  // JSON Export Methods
  private static exportAsJSON(project: Project, options: ExportOptions): ExportResult {
    const exportData = this.prepareProjectData(project, options);
    const jsonString = JSON.stringify(exportData, null, 2);
    const filename = `${this.sanitizeFilename(project.name)}_${this.getTimestamp()}.json`;

    return {
      success: true,
      data: jsonString,
      filename
    };
  }

  private static exportMultipleAsJSON(projects: Project[], options: ExportOptions): ExportResult {
    const exportData = {
      exportedAt: new Date().toISOString(),
      version: '1.0',
      projects: projects.map(project => this.prepareProjectData(project, options))
    };
    const jsonString = JSON.stringify(exportData, null, 2);
    const filename = `projects_export_${this.getTimestamp()}.json`;

    return {
      success: true,
      data: jsonString,
      filename
    };
  }

  private static exportAppAsJSON(app: GeneratedApp, options: ExportOptions): ExportResult {
    const exportData = this.prepareAppData(app, options);
    const jsonString = JSON.stringify(exportData, null, 2);
    const filename = `${this.sanitizeFilename(app.name)}_app_${this.getTimestamp()}.json`;

    return {
      success: true,
      data: jsonString,
      filename
    };
  }

  // Markdown Export Methods
  private static exportAsMarkdown(project: Project, options: ExportOptions): ExportResult {
    let markdown = `# ${project.name}\n\n`;
    markdown += `**Description:** ${project.description}\n\n`;
    markdown += `**Type:** ${project.type}\n`;
    markdown += `**Status:** ${project.status}\n`;
    markdown += `**Complexity:** ${project.complexity}\n`;
    markdown += `**Priority:** ${project.priority}\n`;
    markdown += `**Progress:** ${project.progress}%\n\n`;

    if (project.tags.length > 0) {
      markdown += `**Tags:** ${project.tags.join(', ')}\n\n`;
    }

    if (options.includeFeatures && project.features) {
      markdown += `## Features\n\n`;
      project.features.forEach(feature => {
        markdown += `### ${feature.name}\n`;
        markdown += `${feature.description}\n`;
        markdown += `- **Complexity:** ${feature.complexity}\n`;
        markdown += `- **Estimated Hours:** ${feature.estimatedHours}\n\n`;
      });
    }

    if (options.includeTechStack && project.techStack) {
      markdown += `## Technology Stack\n\n`;
      Object.entries(project.techStack).forEach(([category, technologies]) => {
        if (technologies.length > 0) {
          markdown += `### ${category.charAt(0).toUpperCase() + category.slice(1)}\n`;
          technologies.forEach(tech => {
            markdown += `- ${tech}\n`;
          });
          markdown += '\n';
        }
      });
    }

    if (options.includeMarketAnalysis && project.marketAnalysis) {
      markdown += `## Market Analysis\n\n`;
      markdown += `**Target Audience:** ${project.marketAnalysis.targetAudience}\n`;
      markdown += `**Market Size:** ${project.marketAnalysis.marketSize}\n`;
      markdown += `**Revenue Model:** ${project.marketAnalysis.revenueModel}\n`;
      markdown += `**Estimated Revenue:** ${project.marketAnalysis.estimatedRevenue}\n\n`;

      if (project.marketAnalysis.uniqueSellingPoints.length > 0) {
        markdown += `### Unique Selling Points\n`;
        project.marketAnalysis.uniqueSellingPoints.forEach(usp => {
          markdown += `- ${usp}\n`;
        });
        markdown += '\n';
      }

      if (project.marketAnalysis.competitors.length > 0) {
        markdown += `### Competitors\n`;
        project.marketAnalysis.competitors.forEach(competitor => {
          markdown += `- ${competitor}\n`;
        });
        markdown += '\n';
      }
    }

    markdown += `---\n*Exported on ${new Date().toLocaleDateString()}*\n`;

    const filename = `${this.sanitizeFilename(project.name)}_${this.getTimestamp()}.md`;

    return {
      success: true,
      data: markdown,
      filename
    };
  }

  private static exportMultipleAsMarkdown(projects: Project[], options: ExportOptions): ExportResult {
    let markdown = `# Projects Export\n\n`;
    markdown += `*Exported on ${new Date().toLocaleDateString()}*\n\n`;
    markdown += `Total Projects: ${projects.length}\n\n`;

    projects.forEach((project, index) => {
      markdown += `## ${index + 1}. ${project.name}\n\n`;
      markdown += `**Description:** ${project.description}\n`;
      markdown += `**Type:** ${project.type} | **Status:** ${project.status} | **Progress:** ${project.progress}%\n\n`;
      
      if (project.tags.length > 0) {
        markdown += `**Tags:** ${project.tags.join(', ')}\n\n`;
      }
      
      markdown += `---\n\n`;
    });

    const filename = `projects_export_${this.getTimestamp()}.md`;

    return {
      success: true,
      data: markdown,
      filename
    };
  }

  private static exportAppAsMarkdown(app: GeneratedApp, options: ExportOptions): ExportResult {
    let markdown = `# ${app.name}\n\n`;
    markdown += `${app.description}\n\n`;
    markdown += `**Category:** ${app.category}\n`;
    markdown += `**Complexity:** ${app.complexity}\n`;
    markdown += `**Estimated Time:** ${app.estimatedTime}\n\n`;

    if (options.includeFeatures && app.features) {
      markdown += `## Features\n\n`;
      app.features.forEach(feature => {
        markdown += `### ${feature.name}\n`;
        markdown += `${feature.description}\n`;
        markdown += `- **Complexity:** ${feature.complexity}\n`;
        markdown += `- **Estimated Hours:** ${feature.estimatedHours}\n\n`;
      });
    }

    if (options.includeTechStack && app.techStack) {
      markdown += `## Technology Stack\n\n`;
      Object.entries(app.techStack).forEach(([category, technologies]) => {
        if (technologies.length > 0) {
          markdown += `### ${category.charAt(0).toUpperCase() + category.slice(1)}\n`;
          technologies.forEach(tech => {
            markdown += `- ${tech}\n`;
          });
          markdown += '\n';
        }
      });
    }

    if (options.includeMarketAnalysis && app.marketAnalysis) {
      markdown += `## Market Analysis\n\n`;
      markdown += `**Target Audience:** ${app.marketAnalysis.targetAudience}\n`;
      markdown += `**Market Size:** ${app.marketAnalysis.marketSize}\n`;
      markdown += `**Revenue Model:** ${app.marketAnalysis.revenueModel}\n`;
      markdown += `**Estimated Revenue:** ${app.marketAnalysis.estimatedRevenue}\n\n`;

      if (app.marketAnalysis.uniqueSellingPoints.length > 0) {
        markdown += `### Unique Selling Points\n`;
        app.marketAnalysis.uniqueSellingPoints.forEach(usp => {
          markdown += `- ${usp}\n`;
        });
        markdown += '\n';
      }
    }

    markdown += `---\n*Generated on ${new Date(app.createdAt).toLocaleDateString()}*\n`;

    const filename = `${this.sanitizeFilename(app.name)}_app_${this.getTimestamp()}.md`;

    return {
      success: true,
      data: markdown,
      filename
    };
  }

  // CSV Export Methods
  private static exportAsCSV(project: Project, options: ExportOptions): ExportResult {
    const headers = [
      'Name',
      'Description',
      'Type',
      'Status',
      'Complexity',
      'Priority',
      'Progress',
      'Created',
      'Last Modified',
      'Tags'
    ];

    const row = [
      project.name,
      project.description,
      project.type,
      project.status,
      project.complexity,
      project.priority,
      project.progress.toString(),
      project.createdAt,
      project.lastModified,
      project.tags.join('; ')
    ];

    const csvContent = [headers.join(','), row.join(',')].join('\n');
    const filename = `${this.sanitizeFilename(project.name)}_${this.getTimestamp()}.csv`;

    return {
      success: true,
      data: csvContent,
      filename
    };
  }

  private static exportMultipleAsCSV(projects: Project[], options: ExportOptions): ExportResult {
    const headers = [
      'Name',
      'Description',
      'Type',
      'Status',
      'Complexity',
      'Priority',
      'Progress',
      'Created',
      'Last Modified',
      'Tags'
    ];

    const rows = projects.map(project => [
      project.name,
      project.description,
      project.type,
      project.status,
      project.complexity,
      project.priority,
      project.progress.toString(),
      project.createdAt,
      project.lastModified,
      project.tags.join('; ')
    ]);

    const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    const filename = `projects_export_${this.getTimestamp()}.csv`;

    return {
      success: true,
      data: csvContent,
      filename
    };
  }

  // PDF Export Methods (simplified - would need a proper PDF library in production)
  private static exportAsPDF(project: Project, options: ExportOptions): ExportResult {
    // This is a simplified implementation
    // In production, you would use a library like jsPDF or Puppeteer
    const htmlContent = this.generateHTMLForPDF(project, options);
    const filename = `${this.sanitizeFilename(project.name)}_${this.getTimestamp()}.html`;

    return {
      success: true,
      data: htmlContent,
      filename
    };
  }

  private static exportAppAsPDF(app: GeneratedApp, options: ExportOptions): ExportResult {
    const htmlContent = this.generateAppHTMLForPDF(app, options);
    const filename = `${this.sanitizeFilename(app.name)}_app_${this.getTimestamp()}.html`;

    return {
      success: true,
      data: htmlContent,
      filename
    };
  }

  // Import Methods
  private static async importFromJSON(content: string): Promise<ImportResult> {
    try {
      const data = JSON.parse(content);
      const warnings: string[] = [];

      // Handle single project
      if (data.id && data.name) {
        const project = this.validateAndConvertProject(data);
        return {
          success: true,
          projects: [project],
          warnings
        };
      }

      // Handle multiple projects
      if (data.projects && Array.isArray(data.projects)) {
        const projects = data.projects
          .map((projectData: any) => {
            try {
              return this.validateAndConvertProject(projectData);
            } catch (error) {
              warnings.push(`Failed to import project "${projectData.name || 'Unknown'}": ${error}`);
              return null;
            }
          })
          .filter(Boolean);

        return {
          success: true,
          projects,
          warnings
        };
      }

      // Handle generated app
      if (data.category && data.techStack) {
        // This is a generated app, convert to project
        const project = this.convertAppToProject(data);
        return {
          success: true,
          projects: [project],
          apps: [data],
          warnings
        };
      }

      throw new Error('Invalid JSON format');
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'JSON parsing failed'
      };
    }
  }

  private static async importFromCSV(content: string): Promise<ImportResult> {
    try {
      const lines = content.split('\n').filter(line => line.trim());
      if (lines.length < 2) {
        throw new Error('CSV file must have at least a header and one data row');
      }

      const headers = lines[0].split(',').map(h => h.trim());
      const projects: Project[] = [];
      const warnings: string[] = [];

      for (let i = 1; i < lines.length; i++) {
        try {
          const values = lines[i].split(',').map(v => v.trim());
          const projectData: any = {};

          headers.forEach((header, index) => {
            projectData[header.toLowerCase()] = values[index] || '';
          });

          const project = this.convertCSVRowToProject(projectData, i + 1);
          projects.push(project);
        } catch (error) {
          warnings.push(`Failed to import row ${i + 1}: ${error}`);
        }
      }

      return {
        success: true,
        projects,
        warnings
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'CSV parsing failed'
      };
    }
  }

  // Helper Methods
  private static prepareProjectData(project: Project, options: ExportOptions): any {
    const data: any = {
      id: project.id,
      name: project.name,
      description: project.description,
      type: project.type,
      status: project.status,
      complexity: project.complexity,
      priority: project.priority,
      progress: project.progress,
      createdAt: project.createdAt,
      lastModified: project.lastModified,
      tags: project.tags
    };

    if (options.includeFeatures && project.features) {
      data.features = project.features;
    }

    if (options.includeTechStack && project.techStack) {
      data.techStack = project.techStack;
    }

    if (options.includeMarketAnalysis && project.marketAnalysis) {
      data.marketAnalysis = project.marketAnalysis;
    }

    return data;
  }

  private static prepareAppData(app: GeneratedApp, options: ExportOptions): any {
    const data: any = {
      id: app.id,
      name: app.name,
      description: app.description,
      category: app.category,
      complexity: app.complexity,
      estimatedTime: app.estimatedTime,
      createdAt: app.createdAt
    };

    if (options.includeFeatures && app.features) {
      data.features = app.features;
    }

    if (options.includeTechStack && app.techStack) {
      data.techStack = app.techStack;
    }

    if (options.includeMarketAnalysis && app.marketAnalysis) {
      data.marketAnalysis = app.marketAnalysis;
    }

    return data;
  }

  private static validateAndConvertProject(data: any): Project {
    if (!data.id || !data.name) {
      throw new Error('Project must have id and name');
    }

    return {
      id: data.id,
      name: data.name,
      description: data.description || '',
      type: data.type || 'web',
      status: data.status || 'draft',
      complexity: data.complexity || 'medium',
      priority: data.priority || 'medium',
      progress: data.progress || 0,
      createdAt: data.createdAt || new Date().toISOString(),
      lastModified: data.lastModified || new Date().toISOString(),
      tags: data.tags || [],
      features: data.features,
      techStack: data.techStack,
      marketAnalysis: data.marketAnalysis
    };
  }

  private static convertAppToProject(app: GeneratedApp): Project {
    return {
      id: app.id,
      name: app.name,
      description: app.description,
      type: app.category as any,
      status: 'draft',
      complexity: app.complexity,
      priority: 'medium',
      progress: 0,
      createdAt: app.createdAt,
      lastModified: app.createdAt,
      tags: ['Generated', 'AI'],
      features: app.features,
      techStack: app.techStack,
      marketAnalysis: app.marketAnalysis
    };
  }

  private static convertCSVRowToProject(data: any, rowNumber: number): Project {
    return {
      id: data.id || `imported-${Date.now()}-${rowNumber}`,
      name: data.name || `Imported Project ${rowNumber}`,
      description: data.description || '',
      type: data.type || 'web',
      status: data.status || 'draft',
      complexity: data.complexity || 'medium',
      priority: data.priority || 'medium',
      progress: parseInt(data.progress) || 0,
      createdAt: data.created || new Date().toISOString(),
      lastModified: data['last modified'] || new Date().toISOString(),
      tags: data.tags ? data.tags.split(';').map((t: string) => t.trim()) : []
    };
  }

  private static generateHTMLForPDF(project: Project, options: ExportOptions): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${project.name}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          h1 { color: #333; border-bottom: 2px solid #007bff; }
          h2 { color: #666; margin-top: 30px; }
          .meta { background: #f8f9fa; padding: 15px; border-radius: 5px; }
          .feature { margin: 10px 0; padding: 10px; border-left: 3px solid #007bff; }
        </style>
      </head>
      <body>
        <h1>${project.name}</h1>
        <div class="meta">
          <p><strong>Description:</strong> ${project.description}</p>
          <p><strong>Type:</strong> ${project.type}</p>
          <p><strong>Status:</strong> ${project.status}</p>
          <p><strong>Progress:</strong> ${project.progress}%</p>
        </div>
        ${options.includeFeatures && project.features ? `
          <h2>Features</h2>
          ${project.features.map(f => `<div class="feature"><strong>${f.name}</strong><br>${f.description}</div>`).join('')}
        ` : ''}
        <p><em>Exported on ${new Date().toLocaleDateString()}</em></p>
      </body>
      </html>
    `;
  }

  private static generateAppHTMLForPDF(app: GeneratedApp, options: ExportOptions): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${app.name}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          h1 { color: #333; border-bottom: 2px solid #007bff; }
          h2 { color: #666; margin-top: 30px; }
          .meta { background: #f8f9fa; padding: 15px; border-radius: 5px; }
          .feature { margin: 10px 0; padding: 10px; border-left: 3px solid #007bff; }
        </style>
      </head>
      <body>
        <h1>${app.name}</h1>
        <div class="meta">
          <p><strong>Description:</strong> ${app.description}</p>
          <p><strong>Category:</strong> ${app.category}</p>
          <p><strong>Complexity:</strong> ${app.complexity}</p>
          <p><strong>Estimated Time:</strong> ${app.estimatedTime}</p>
        </div>
        ${options.includeFeatures && app.features ? `
          <h2>Features</h2>
          ${app.features.map(f => `<div class="feature"><strong>${f.name}</strong><br>${f.description}</div>`).join('')}
        ` : ''}
        <p><em>Generated on ${new Date(app.createdAt).toLocaleDateString()}</em></p>
      </body>
      </html>
    `;
  }

  private static async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  private static sanitizeFilename(filename: string): string {
    return filename.replace(/[^a-z0-9]/gi, '_').toLowerCase();
  }

  private static getTimestamp(): string {
    return new Date().toISOString().split('T')[0];
  }

  // Download helper
  static downloadFile(data: string | Blob, filename: string, mimeType: string = 'text/plain'): void {
    const blob = typeof data === 'string' ? new Blob([data], { type: mimeType }) : data;
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}