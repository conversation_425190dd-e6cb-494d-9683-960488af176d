import { useCallback, useRef } from 'react';
import { useMemoryManager } from './useMemoryManager';

interface TouchFeedbackOptions {
  hapticFeedback?: boolean;
  visualFeedback?: boolean;
  feedbackDuration?: number;
  feedbackIntensity?: 'light' | 'medium' | 'heavy';
}

export const useTouchFeedback = (options: TouchFeedbackOptions = {}) => {
  const {
    hapticFeedback = true,
    visualFeedback = true,
    feedbackDuration = 150,
    feedbackIntensity = 'light'
  } = options;

  const activeElements = useRef<Set<HTMLElement>>(new Set());
  const { addTimer, addEventListener, addCleanup } = useMemoryManager();

  const triggerHapticFeedback = useCallback((intensity: 'light' | 'medium' | 'heavy' = feedbackIntensity) => {
    if (!hapticFeedback) return;

    // Check if the device supports haptic feedback
    if ('vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30]
      };
      navigator.vibrate(patterns[intensity]);
    }

    // For devices with more advanced haptic feedback (iOS Safari)
    if ('ontouchstart' in window && (window as any).DeviceMotionEvent) {
      try {
        // This is a fallback for iOS devices
        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
        audio.volume = 0.01;
        audio.play().catch(() => {});
      } catch (e) {
        // Silently fail if audio creation fails
      }
    }
  }, [hapticFeedback, feedbackIntensity]);

  const addVisualFeedback = useCallback((element: HTMLElement) => {
    if (!visualFeedback) return;

    element.style.transform = 'scale(0.95)';
    element.style.transition = `transform ${feedbackDuration}ms ease-out`;
    
    activeElements.current.add(element);

    setTimeout(() => {
      if (activeElements.current.has(element)) {
        element.style.transform = 'scale(1)';
        activeElements.current.delete(element);
      }
    }, feedbackDuration);
  }, [visualFeedback, feedbackDuration]);

  const removeVisualFeedback = useCallback((element: HTMLElement) => {
    if (activeElements.current.has(element)) {
      element.style.transform = 'scale(1)';
      activeElements.current.delete(element);
    }
  }, []);

  const handleTouchStart = useCallback((element: HTMLElement, options?: { haptic?: boolean; visual?: boolean }) => {
    const shouldHaptic = options?.haptic ?? hapticFeedback;
    const shouldVisual = options?.visual ?? visualFeedback;

    if (shouldHaptic) {
      triggerHapticFeedback();
    }

    if (shouldVisual) {
      addVisualFeedback(element);
    }
  }, [hapticFeedback, visualFeedback, triggerHapticFeedback, addVisualFeedback]);

  const handleTouchEnd = useCallback((element: HTMLElement) => {
    removeVisualFeedback(element);
  }, [removeVisualFeedback]);

  const attachTouchFeedback = useCallback((element: HTMLElement | null, customOptions?: TouchFeedbackOptions) => {
    if (!element) return;

    const mergedOptions = { ...options, ...customOptions };

    const onTouchStart = () => handleTouchStart(element, mergedOptions);
    const onTouchEnd = () => handleTouchEnd(element);
    const onTouchCancel = () => handleTouchEnd(element);

    element.addEventListener('touchstart', onTouchStart, { passive: true });
    element.addEventListener('touchend', onTouchEnd, { passive: true });
    element.addEventListener('touchcancel', onTouchCancel, { passive: true });

    return () => {
      element.removeEventListener('touchstart', onTouchStart);
      element.removeEventListener('touchend', onTouchEnd);
      element.removeEventListener('touchcancel', onTouchCancel);
      removeVisualFeedback(element);
    };
  }, [options, handleTouchStart, handleTouchEnd, removeVisualFeedback]);

  return {
    triggerHapticFeedback,
    addVisualFeedback,
    removeVisualFeedback,
    handleTouchStart,
    handleTouchEnd,
    attachTouchFeedback
  };
};