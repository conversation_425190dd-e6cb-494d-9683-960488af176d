// Empty state for no projects

import React from 'react';
import { Folder, Plus, Zap, Import } from 'lucide-react';
import EmptyState, { EmptyStateAction } from './EmptyState';

interface NoProjectsProps {
  onCreateProject: () => void;
  onImportProjects?: () => void;
  onOpenGenerator?: () => void;
  className?: string;
}

const NoProjects: React.FC<NoProjectsProps> = ({
  onCreateProject,
  onImportProjects,
  onOpenGenerator,
  className
}) => {
  const actions: EmptyStateAction[] = [
    {
      label: 'Create your first project',
      onClick: onCreateProject,
      variant: 'primary',
      icon: Plus
    }
  ];

  if (onOpenGenerator) {
    actions.push({
      label: 'Generate with AI',
      onClick: onOpenGenerator,
      variant: 'secondary',
      icon: Zap
    });
  }

  if (onImportProjects) {
    actions.push({
      label: 'Import projects',
      onClick: onImportProjects,
      variant: 'secondary',
      icon: Import
    });
  }

  return (
    <EmptyState
      icon={Folder}
      title="No projects yet"
      description="Start building amazing applications by creating your first project. You can generate ideas with AI or import existing projects."
      actions={actions}
      className={className}
    />
  );
};

export default NoProjects;