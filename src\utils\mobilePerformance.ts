// Mobile performance optimization utilities

export class MobilePerformanceOptimizer {
  private static instance: MobilePerformanceOptimizer;
  private intersectionObserver: IntersectionObserver | null = null;
  private resizeObserver: ResizeObserver | null = null;
  private rafCallbacks: Set<() => void> = new Set();
  private isRafScheduled = false;

  private constructor() {
    this.initializeObservers();
    this.optimizeScrolling();
  }

  static getInstance(): MobilePerformanceOptimizer {
    if (!MobilePerformanceOptimizer.instance) {
      MobilePerformanceOptimizer.instance = new MobilePerformanceOptimizer();
    }
    return MobilePerformanceOptimizer.instance;
  }

  private initializeObservers() {
    // Intersection Observer for lazy loading
    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            element.dispatchEvent(new CustomEvent('enterViewport'));
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    // Resize Observer for responsive updates
    if ('ResizeObserver' in window) {
      this.resizeObserver = new ResizeObserver((entries) => {
        this.scheduleRAF(() => {
          entries.forEach((entry) => {
            const element = entry.target as HTMLElement;
            element.dispatchEvent(new CustomEvent('resize', {
              detail: { contentRect: entry.contentRect }
            }));
          });
        });
      });
    }
  }

  private optimizeScrolling() {
    // Add passive event listeners for better scroll performance
    const passiveOptions = { passive: true };
    
    window.addEventListener('touchstart', () => {}, passiveOptions);
    window.addEventListener('touchmove', () => {}, passiveOptions);
    window.addEventListener('touchend', () => {}, passiveOptions);
    window.addEventListener('wheel', () => {}, passiveOptions);
  }

  // Schedule callbacks using requestAnimationFrame
  scheduleRAF(callback: () => void) {
    this.rafCallbacks.add(callback);
    
    if (!this.isRafScheduled) {
      this.isRafScheduled = true;
      requestAnimationFrame(() => {
        this.rafCallbacks.forEach(cb => cb());
        this.rafCallbacks.clear();
        this.isRafScheduled = false;
      });
    }
  }

  // Lazy loading utilities
  observeForLazyLoading(element: HTMLElement) {
    if (this.intersectionObserver) {
      this.intersectionObserver.observe(element);
    }
  }

  unobserveForLazyLoading(element: HTMLElement) {
    if (this.intersectionObserver) {
      this.intersectionObserver.unobserve(element);
    }
  }

  // Responsive utilities
  observeForResize(element: HTMLElement) {
    if (this.resizeObserver) {
      this.resizeObserver.observe(element);
    }
  }

  unobserveForResize(element: HTMLElement) {
    if (this.resizeObserver) {
      this.resizeObserver.unobserve(element);
    }
  }

  // Memory management
  cleanup() {
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    this.rafCallbacks.clear();
  }
}

// Touch optimization utilities
export const touchOptimizations = {
  // Add touch feedback to elements
  addTouchFeedback(element: HTMLElement, className = 'touch-feedback') {
    const addFeedback = () => element.classList.add(className);
    const removeFeedback = () => element.classList.remove(className);

    element.addEventListener('touchstart', addFeedback, { passive: true });
    element.addEventListener('touchend', removeFeedback, { passive: true });
    element.addEventListener('touchcancel', removeFeedback, { passive: true });

    return () => {
      element.removeEventListener('touchstart', addFeedback);
      element.removeEventListener('touchend', removeFeedback);
      element.removeEventListener('touchcancel', removeFeedback);
    };
  },

  // Optimize touch events for better performance
  optimizeTouchEvents(element: HTMLElement) {
    const preventDefaultOnMove = (e: TouchEvent) => {
      if (e.touches.length > 1) {
        e.preventDefault();
      }
    };

    element.addEventListener('touchmove', preventDefaultOnMove, { passive: false });

    return () => {
      element.removeEventListener('touchmove', preventDefaultOnMove);
    };
  }
};

// Image optimization utilities
export const imageOptimizations = {
  // Create optimized image loading
  createOptimizedImage(src: string, alt: string, options: {
    lazy?: boolean;
    placeholder?: string;
    sizes?: string;
  } = {}) {
    const img = document.createElement('img');
    img.alt = alt;
    
    if (options.lazy) {
      img.loading = 'lazy';
    }
    
    if (options.placeholder) {
      img.src = options.placeholder;
      img.dataset.src = src;
    } else {
      img.src = src;
    }
    
    if (options.sizes) {
      img.sizes = options.sizes;
    }

    return img;
  },

  // Preload critical images
  preloadImage(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = reject;
      img.src = src;
    });
  }
};

// Animation optimization utilities
export const animationOptimizations = {
  // Check if user prefers reduced motion
  prefersReducedMotion(): boolean {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  // Create performance-optimized animations
  createOptimizedAnimation(element: HTMLElement, keyframes: Keyframe[], options: KeyframeAnimationOptions) {
    if (this.prefersReducedMotion()) {
      return null;
    }

    // Use transform and opacity for better performance
    const optimizedKeyframes = keyframes.map(frame => ({
      ...frame,
      // Ensure hardware acceleration
      transform: frame.transform || 'translateZ(0)'
    }));

    return element.animate(optimizedKeyframes, {
      ...options,
      // Optimize for mobile
      duration: typeof options.duration === 'number' ? Math.min(options.duration, 300) : options.duration
    });
  }
};

// Memory management utilities
export const memoryOptimizations = {
  // Debounce function calls
  debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
    let timeout: NodeJS.Timeout;
    return ((...args: any[]) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    }) as T;
  },

  // Throttle function calls
  throttle<T extends (...args: any[]) => any>(func: T, limit: number): T {
    let inThrottle: boolean;
    return ((...args: any[]) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }) as T;
  },

  // Clean up event listeners
  createCleanupManager() {
    const cleanupFunctions: (() => void)[] = [];

    return {
      add(cleanup: () => void) {
        cleanupFunctions.push(cleanup);
      },
      cleanup() {
        cleanupFunctions.forEach(fn => fn());
        cleanupFunctions.length = 0;
      }
    };
  }
};

// Initialize mobile performance optimizations
export function initializeMobilePerformance() {
  const optimizer = MobilePerformanceOptimizer.getInstance();
  
  // Add global performance optimizations
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      // Perform non-critical optimizations during idle time
      console.log('Mobile performance optimizations initialized');
    });
  }

  return optimizer;
}