import { Variants } from 'framer-motion';

// Detect if user prefers reduced motion with caching
let reducedMotionCache: boolean | null = null;
let mediaQueryList: MediaQueryList | null = null;

export const prefersReducedMotion = () => {
  if (reducedMotionCache === null) {
    if (typeof window !== 'undefined') {
      mediaQueryList = window.matchMedia('(prefers-reduced-motion: reduce)');
      reducedMotionCache = mediaQueryList.matches;
      
      // Listen for changes and update cache
      const updateCache = (e: MediaQueryListEvent) => {
        reducedMotionCache = e.matches;
      };
      
      mediaQueryList.addEventListener('change', updateCache);
      
      // Cleanup function for memory management
      if (typeof window !== 'undefined' && 'addEventListener' in window) {
        window.addEventListener('beforeunload', () => {
          if (mediaQueryList) {
            mediaQueryList.removeEventListener('change', updateCache);
          }
        });
      }
    } else {
      reducedMotionCache = false;
    }
  }
  return reducedMotionCache;
};

// Mobile-optimized animation variants
export const mobileAnimations = {
  // Faster, more subtle animations for mobile
  fadeIn: {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: { 
        duration: prefersReducedMotion() ? 0 : 0.2,
        ease: 'easeOut'
      }
    },
    exit: { 
      opacity: 0,
      transition: { 
        duration: prefersReducedMotion() ? 0 : 0.15,
        ease: 'easeIn'
      }
    }
  } as Variants,

  // Optimized slide animations for mobile
  slideUp: {
    initial: { y: 20, opacity: 0 },
    animate: { 
      y: 0, 
      opacity: 1,
      transition: { 
        duration: prefersReducedMotion() ? 0 : 0.25,
        ease: [0.25, 0.46, 0.45, 0.94] // Custom easing for mobile
      }
    },
    exit: { 
      y: -10, 
      opacity: 0,
      transition: { 
        duration: prefersReducedMotion() ? 0 : 0.2,
        ease: 'easeIn'
      }
    }
  } as Variants,

  // Mobile-friendly modal animations
  mobileModal: {
    initial: { 
      y: '100%',
      opacity: 0,
      scale: 0.95
    },
    animate: { 
      y: 0,
      opacity: 1,
      scale: 1,
      transition: { 
        duration: prefersReducedMotion() ? 0 : 0.3,
        ease: [0.25, 0.46, 0.45, 0.94],
        opacity: { duration: prefersReducedMotion() ? 0 : 0.2 }
      }
    },
    exit: { 
      y: '100%',
      opacity: 0,
      scale: 0.95,
      transition: { 
        duration: prefersReducedMotion() ? 0 : 0.25,
        ease: [0.55, 0.06, 0.68, 0.19]
      }
    }
  } as Variants,

  // Swipe-based animations
  swipeLeft: {
    initial: { x: 0 },
    animate: { 
      x: -100,
      transition: { 
        duration: prefersReducedMotion() ? 0 : 0.2,
        ease: 'easeOut'
      }
    }
  } as Variants,

  swipeRight: {
    initial: { x: 0 },
    animate: { 
      x: 100,
      transition: { 
        duration: prefersReducedMotion() ? 0 : 0.2,
        ease: 'easeOut'
      }
    }
  } as Variants,

  // Stagger animations optimized for mobile
  mobileStagger: {
    animate: {
      transition: {
        staggerChildren: prefersReducedMotion() ? 0 : 0.05, // Faster stagger on mobile
        delayChildren: prefersReducedMotion() ? 0 : 0.1
      }
    }
  } as Variants,

  // Touch feedback animation
  touchFeedback: {
    initial: { scale: 1 },
    animate: { 
      scale: 0.95,
      transition: { 
        duration: prefersReducedMotion() ? 0 : 0.1,
        ease: 'easeOut'
      }
    }
  } as Variants,

  // Pull to refresh animation
  pullToRefresh: {
    initial: { y: -50, opacity: 0 },
    animate: { 
      y: 0, 
      opacity: 1,
      transition: { 
        duration: prefersReducedMotion() ? 0 : 0.3,
        ease: 'easeOut'
      }
    },
    exit: { 
      y: -50, 
      opacity: 0,
      transition: { 
        duration: prefersReducedMotion() ? 0 : 0.2,
        ease: 'easeIn'
      }
    }
  } as Variants
};

// Mobile-specific transition configurations
export const mobileTransitions = {
  // Fast transitions for immediate feedback
  immediate: {
    duration: prefersReducedMotion() ? 0 : 0.1,
    ease: 'easeOut'
  },
  
  // Standard mobile transitions
  standard: {
    duration: prefersReducedMotion() ? 0 : 0.2,
    ease: [0.25, 0.46, 0.45, 0.94]
  },
  
  // Slower transitions for complex animations
  complex: {
    duration: prefersReducedMotion() ? 0 : 0.3,
    ease: [0.25, 0.46, 0.45, 0.94]
  },
  
  // Spring-based transitions for natural feel
  spring: {
    type: 'spring' as const,
    stiffness: prefersReducedMotion() ? 1000 : 300,
    damping: prefersReducedMotion() ? 100 : 25,
    mass: 1
  }
};

// Utility function to create responsive animations
export const createResponsiveAnimation = (
  mobileVariant: Variants,
  desktopVariant: Variants
): Variants => {
  const isMobile = window.innerWidth < 768;
  return isMobile ? mobileVariant : desktopVariant;
};

// Hook for managing mobile animations with memory optimization
export const useMobileAnimations = () => {
  const [isMobile, setIsMobile] = useState(() => 
    typeof window !== 'undefined' ? window.innerWidth < 768 : false
  );
  const reducedMotion = prefersReducedMotion();

  // Memoize animation getter to prevent recreation
  const getAnimation = useCallback((animationName: keyof typeof mobileAnimations) => {
    if (reducedMotion) {
      return {
        initial: {},
        animate: {},
        exit: {}
      };
    }
    return mobileAnimations[animationName];
  }, [reducedMotion]);

  // Memoize transition getter to prevent recreation
  const getTransition = useCallback((transitionName: keyof typeof mobileTransitions) => {
    if (reducedMotion) {
      return { duration: 0 };
    }
    return mobileTransitions[transitionName];
  }, [reducedMotion]);

  // Listen for resize events with proper cleanup
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Use passive listener for better performance
    window.addEventListener('resize', handleResize, { passive: true });

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Memoize return object to prevent unnecessary re-renders
  return useMemo(() => ({
    isMobile,
    reducedMotion,
    getAnimation,
    getTransition,
    animations: mobileAnimations,
    transitions: mobileTransitions
  }), [isMobile, reducedMotion, getAnimation, getTransition]);
};

// Add required imports
import { useState, useEffect, useCallback, useMemo } from 'react';