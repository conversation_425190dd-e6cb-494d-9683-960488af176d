import { useState, useEffect, useCallback, useRef } from 'react';
import { ProgressiveLoader } from '../utils/lazyLoading';

export interface UseProgressiveLoadingOptions<T> {
  data: T[];
  batchSize?: number;
  autoLoad?: boolean;
  loadDelay?: number;
}

export function useProgressiveLoading<T>({
  data,
  batchSize = 50,
  autoLoad = false,
  loadDelay = 100,
}: UseProgressiveLoadingOptions<T>) {
  const [loadedItems, setLoadedItems] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<Error | null>(null);
  
  const loaderRef = useRef<ProgressiveLoader<T> | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Initialize loader when data changes
  useEffect(() => {
    if (data.length === 0) {
      setLoadedItems([]);
      setProgress(0);
      loaderRef.current = null;
      return;
    }

    loaderRef.current = new ProgressiveLoader(
      data,
      batchSize,
      (progressValue) => setProgress(progressValue)
    );

    setLoadedItems([]);
    setProgress(0);
    setError(null);

    if (autoLoad) {
      loadNextBatch();
    }
  }, [data, batchSize, autoLoad]);

  const loadNextBatch = useCallback(async () => {
    if (!loaderRef.current || isLoading) return;

    setIsLoading(true);
    setError(null);

    try {
      // Add delay for better UX
      if (loadDelay > 0) {
        await new Promise(resolve => {
          timeoutRef.current = setTimeout(resolve, loadDelay);
        });
      }

      const batch = await loaderRef.current.loadNextBatch();
      
      if (batch.length > 0) {
        setLoadedItems(loaderRef.current.getLoadedItems());
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to load batch'));
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, loadDelay]);

  const loadAll = useCallback(async () => {
    if (!loaderRef.current || isLoading) return;

    setIsLoading(true);
    setError(null);

    try {
      const allItems = await loaderRef.current.loadAll();
      setLoadedItems(allItems);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to load all items'));
    } finally {
      setIsLoading(false);
    }
  }, [isLoading]);

  const reset = useCallback(() => {
    if (loaderRef.current) {
      loaderRef.current.reset();
      setLoadedItems([]);
      setProgress(0);
      setError(null);
    }
  }, []);

  const hasMore = loaderRef.current?.hasMore() ?? false;

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    loadedItems,
    isLoading,
    progress,
    error,
    hasMore,
    loadNextBatch,
    loadAll,
    reset,
  };
}

// Hook for virtual scrolling
export function useVirtualScrolling<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5,
}: {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}) {
  const [scrollTop, setScrollTop] = useState(0);
  
  const handleScroll = useCallback((event: React.UIEvent<HTMLElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, []);

  const virtualItems = useCallback(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );

    const visibleItems = items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index,
      offsetTop: (startIndex + index) * itemHeight,
    }));

    return {
      visibleItems,
      totalHeight: items.length * itemHeight,
      startIndex,
      endIndex,
    };
  }, [items, scrollTop, itemHeight, containerHeight, overscan]);

  return {
    ...virtualItems(),
    handleScroll,
  };
}

// Hook for intersection observer lazy loading
export function useIntersectionObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
) {
  const observerRef = useRef<IntersectionObserver>();
  const elementsRef = useRef<Set<Element>>(new Set());

  useEffect(() => {
    observerRef.current = new IntersectionObserver(callback, {
      root: null,
      rootMargin: '50px',
      threshold: 0.1,
      ...options,
    });

    // Observe existing elements
    elementsRef.current.forEach(element => {
      observerRef.current?.observe(element);
    });

    return () => {
      observerRef.current?.disconnect();
    };
  }, [callback, options]);

  const observe = useCallback((element: Element) => {
    if (element && observerRef.current) {
      elementsRef.current.add(element);
      observerRef.current.observe(element);
    }
  }, []);

  const unobserve = useCallback((element: Element) => {
    if (element && observerRef.current) {
      elementsRef.current.delete(element);
      observerRef.current.unobserve(element);
    }
  }, []);

  return { observe, unobserve };
}