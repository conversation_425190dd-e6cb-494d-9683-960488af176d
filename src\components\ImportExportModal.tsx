// Import/Export modal component
import React, { useState, useRef } from 'react';
import { Download, Upload, FileText, FileSpreadsheet, File, X, CheckCircle, AlertCircle } from 'lucide-react';
import Modal from './ui/Modal';
import Button from './ui/Button';
import { ExportService, ExportFormat, ExportOptions } from '../services/exportService';
import { Project } from '../types/project';
import { GeneratedApp } from '../types/api';
import { useToast } from './ui/ToastContainer';

interface ImportExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'export' | 'import';
  project?: Project;
  generatedApp?: GeneratedApp;
  onImportComplete?: (projects: Project[], apps?: GeneratedApp[]) => void;
}

const ImportExportModal: React.FC<ImportExportModalProps> = ({
  isOpen,
  onClose,
  mode,
  project,
  generatedApp,
  onImportComplete
}) => {
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('json');
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'json',
    includeMarketAnalysis: true,
    includeTechStack: true,
    includeFeatures: true,
    includeTimeline: false
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [importResults, setImportResults] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showSuccess, showError } = useToast();

  const formatOptions = [
    { value: 'json', label: 'JSON', icon: FileText, description: 'Complete data with all details' },
    { value: 'markdown', label: 'Markdown', icon: FileText, description: 'Human-readable documentation' },
    { value: 'csv', label: 'CSV', icon: FileSpreadsheet, description: 'Spreadsheet format for basic data' },
    { value: 'pdf', label: 'PDF', icon: File, description: 'Formatted document (HTML preview)' }
  ] as const;

  const handleExport = async () => {
    if (!project && !generatedApp) return;

    setIsProcessing(true);
    try {
      const options = { ...exportOptions, format: selectedFormat };
      let result;

      if (generatedApp) {
        result = await ExportService.exportGeneratedApp(generatedApp, options);
      } else if (project) {
        result = await ExportService.exportProject(project, options);
      }

      if (result?.success && result.data) {
        const mimeTypes = {
          json: 'application/json',
          markdown: 'text/markdown',
          csv: 'text/csv',
          pdf: 'text/html'
        };

        ExportService.downloadFile(
          result.data,
          result.filename,
          mimeTypes[selectedFormat]
        );

        showSuccess(
          'Export Successful',
          `${project?.name || generatedApp?.name} has been exported as ${selectedFormat.toUpperCase()}`
        );
        onClose();
      } else {
        throw new Error(result?.error || 'Export failed');
      }
    } catch (error) {
      showError(
        'Export Failed',
        error instanceof Error ? error.message : 'An error occurred during export'
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsProcessing(true);
    setImportResults(null);

    try {
      const result = await ExportService.importProjects(file);
      setImportResults(result);

      if (result.success && result.projects) {
        onImportComplete?.(result.projects, result.apps);
        showSuccess(
          'Import Successful',
          `Successfully imported ${result.projects.length} project(s)`
        );
      } else {
        throw new Error(result.error || 'Import failed');
      }
    } catch (error) {
      showError(
        'Import Failed',
        error instanceof Error ? error.message : 'An error occurred during import'
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const updateExportOptions = (key: keyof ExportOptions, value: any) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={mode === 'export' ? 'Export Project' : 'Import Projects'}>
      <div className="space-y-6">
        {mode === 'export' ? (
          <>
            {/* Format Selection */}
            <div>
              <label className="block text-white text-sm font-medium mb-3">Export Format</label>
              <div className="grid grid-cols-2 gap-3">
                {formatOptions.map((format) => {
                  const Icon = format.icon;
                  return (
                    <button
                      key={format.value}
                      onClick={() => {
                        setSelectedFormat(format.value);
                        updateExportOptions('format', format.value);
                      }}
                      className={`p-4 rounded-lg border text-left transition-all duration-300 ${
                        selectedFormat === format.value
                          ? 'bg-blue-500/20 border-blue-500/30 text-blue-300'
                          : 'bg-white/[0.05] border-white/20 text-gray-300 hover:bg-white/[0.10] hover:border-white/30'
                      }`}
                    >
                      <div className="flex items-center gap-3 mb-2">
                        <Icon className="w-5 h-5" />
                        <span className="font-medium">{format.label}</span>
                      </div>
                      <p className="text-xs text-gray-400">{format.description}</p>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Export Options */}
            <div>
              <label className="block text-white text-sm font-medium mb-3">Include in Export</label>
              <div className="space-y-3">
                {[
                  { key: 'includeFeatures', label: 'Features & Specifications', enabled: true },
                  { key: 'includeTechStack', label: 'Technology Stack', enabled: true },
                  { key: 'includeMarketAnalysis', label: 'Market Analysis', enabled: true },
                  { key: 'includeTimeline', label: 'Project Timeline', enabled: false }
                ].map((option) => (
                  <label key={option.key} className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={exportOptions[option.key as keyof ExportOptions] as boolean}
                      onChange={(e) => updateExportOptions(option.key as keyof ExportOptions, e.target.checked)}
                      className="w-4 h-4 text-blue-600 bg-white/[0.08] border-white/20 rounded focus:ring-blue-500 focus:ring-2"
                      disabled={!option.enabled}
                    />
                    <span className={`text-sm ${option.enabled ? 'text-gray-300' : 'text-gray-500'}`}>
                      {option.label}
                      {!option.enabled && ' (Coming Soon)'}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Export Preview */}
            <div className="bg-white/[0.05] rounded-lg p-4 border border-white/10">
              <h4 className="text-white font-medium mb-2">Export Preview</h4>
              <p className="text-gray-400 text-sm">
                Exporting: <span className="text-white">{project?.name || generatedApp?.name}</span>
              </p>
              <p className="text-gray-400 text-sm">
                Format: <span className="text-white">{selectedFormat.toUpperCase()}</span>
              </p>
              <p className="text-gray-400 text-sm">
                Includes: {Object.entries(exportOptions)
                  .filter(([key, value]) => key.startsWith('include') && value)
                  .map(([key]) => key.replace('include', '').replace(/([A-Z])/g, ' $1').trim())
                  .join(', ')}
              </p>
            </div>
          </>
        ) : (
          <>
            {/* Import Section */}
            <div>
              <label className="block text-white text-sm font-medium mb-3">Select File to Import</label>
              <div className="border-2 border-dashed border-white/20 rounded-lg p-8 text-center hover:border-white/30 transition-colors">
                <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-300 mb-2">Click to select a file or drag and drop</p>
                <p className="text-gray-500 text-sm mb-4">Supports JSON and CSV files</p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".json,.csv"
                  onChange={handleImport}
                  className="hidden"
                />
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  variant="outline"
                  disabled={isProcessing}
                >
                  {isProcessing ? 'Processing...' : 'Choose File'}
                </Button>
              </div>
            </div>

            {/* Import Results */}
            {importResults && (
              <div className={`p-4 rounded-lg border ${
                importResults.success 
                  ? 'bg-green-500/10 border-green-500/20' 
                  : 'bg-red-500/10 border-red-500/20'
              }`}>
                <div className="flex items-center gap-2 mb-2">
                  {importResults.success ? (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-400" />
                  )}
                  <h4 className={`font-medium ${
                    importResults.success ? 'text-green-300' : 'text-red-300'
                  }`}>
                    {importResults.success ? 'Import Successful' : 'Import Failed'}
                  </h4>
                </div>
                
                {importResults.success && (
                  <p className="text-green-400 text-sm">
                    Successfully imported {importResults.projects?.length || 0} project(s)
                    {importResults.apps && ` and ${importResults.apps.length} generated app(s)`}
                  </p>
                )}
                
                {importResults.error && (
                  <p className="text-red-400 text-sm">{importResults.error}</p>
                )}
                
                {importResults.warnings && importResults.warnings.length > 0 && (
                  <div className="mt-2">
                    <p className="text-yellow-400 text-sm font-medium">Warnings:</p>
                    <ul className="text-yellow-400 text-sm mt-1">
                      {importResults.warnings.map((warning: string, index: number) => (
                        <li key={index}>• {warning}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-white/10">
          <Button
            onClick={onClose}
            variant="outline"
            className="flex-1"
          >
            Cancel
          </Button>
          {mode === 'export' && (
            <Button
              onClick={handleExport}
              disabled={isProcessing || (!project && !generatedApp)}
              className="flex-1"
            >
              {isProcessing ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Export {selectedFormat.toUpperCase()}
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default ImportExportModal;