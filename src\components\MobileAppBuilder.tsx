import React, { useState } from 'react';
import { ArrowLef<PERSON>, Code, Eye, Download, Share2 } from 'lucide-react';
import { Link } from 'react-router-dom';

const MobileAppBuilder: React.FC = () => {
  const [activeTab, setActiveTab] = useState('Code');

  return (
    <div className="h-screen bg-gray-900 flex flex-col">
      {/* Top Bar */}
      <div className="flex items-center justify-between px-4 py-3 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-4">
          <Link 
            to="/"
            className="flex items-center gap-2 px-3 py-1 text-gray-300 hover:text-white hover:bg-gray-700 rounded transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span className="text-sm">Back</span>
          </Link>
          
          <span className="text-white font-medium">HomeStay Search Hub</span>
        </div>
        
        <div className="flex items-center">
          <button
            onClick={() => setActiveTab('Code')}
            className={`px-4 py-2 text-sm font-medium rounded-l-lg transition-colors ${
              activeTab === 'Code' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            <Code className="w-4 h-4 inline mr-2" />
            Code
          </button>
          <button
            onClick={() => setActiveTab('Preview')}
            className={`px-4 py-2 text-sm font-medium rounded-r-lg transition-colors ${
              activeTab === 'Preview' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            <Eye className="w-4 h-4 inline mr-2" />
            Preview
          </button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Left Panel - AI Generation Log */}
        <div className="flex-1 bg-gray-900 flex flex-col">
          {/* AI Output Content */}
          <div className="flex-1 p-6 overflow-y-auto text-gray-300 font-mono text-sm leading-relaxed">
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-white font-semibold">Rork</span>
              </div>
              
              <p className="text-gray-300 mb-6">
                I'll create a beautiful Airbnb-style home screen with all the features you requested. This design will draw inspiration from Airbnb's clean, modern aesthetic with high-quality property cards, an intuitive search experience, and smooth interactions.
              </p>
              
              <div className="mb-6">
                <h3 className="text-white font-semibold mb-3">💭 Thoughts</h3>
                <ul className="space-y-2 text-gray-400">
                  <li>• Let's plan a comprehensive Airbnb-style home screen with:</li>
                  <li className="ml-4">• Search bar at the top</li>
                  <li className="ml-4">• Filter buttons for dates, guests, property types</li>
                  <li className="ml-4">• Property listings in a grid format</li>
                  <li className="ml-4">• Each listing with image carousel, price, rating, and details</li>
                </ul>
              </div>
              
              <div className="mb-6">
                <p className="text-gray-300 mb-3">For the implementation, I'll need:</p>
                <ul className="space-y-1 text-gray-400">
                  <li>• Components: SearchBar, FilterBar, PropertyCard, ImageCarousel, Rating</li>
                  <li>• Screens: HomeScreen, SearchResults, PropertyDetails</li>
                  <li>• Utils: Property filter</li>
                  <li>• Mock data to simulate listings, images, details, ratings</li>
                  <li>• Navigation: React Navigation for screen transitions</li>
                </ul>
              </div>
              
              <p className="text-gray-300 mb-6">
                Let's create a beautiful implementation with attention to detail.
              </p>
            </div>
            
            <div className="space-y-2">
              <div className="text-white font-semibold mb-3">Actions Taken:</div>
              
              {[
                'Created app/components/SearchBar.tsx',
                'Created app/components/FilterBar.tsx', 
                'Created app/components/PropertyCard.tsx',
                'Created app/components/ImageCarousel.tsx',
                'Created app/components/Rating.tsx',
                'Created app/screens/HomeScreen.tsx',
                'Created app/screens/SearchResults.tsx',
                'Created app/screens/PropertyDetails.tsx',
                'Created app/utils/mockData.ts',
                'Created app/utils/filters.ts',
                'Created app/styles/colors.ts',
                'Created app/styles/typography.ts',
                'Created app/navigation/AppNavigator.tsx',
                'Updated package.json'
              ].map((action, index) => (
                <div key={index} className="flex items-center gap-2 text-green-400">
                  <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                  <span>{action}</span>
                </div>
              ))}
              
              <div className="mt-6 p-4 bg-yellow-900/20 border border-yellow-600/30 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span className="text-yellow-400 font-semibold">Potential issue</span>
                </div>
                <p className="text-yellow-300 text-sm">
                  Browser preview apps cannot function & looks different. 
                  Test on device for the best results.
                </p>
              </div>
            </div>
          </div>
          
          {/* Bottom Input */}
          <div className="p-4 border-t border-gray-700">
            <div className="flex items-center gap-3">
              <div className="flex-1 bg-gray-800 rounded-lg px-4 py-3 border border-gray-600">
                <input 
                  type="text" 
                  placeholder="Describe the mobile app you want to build..."
                  className="w-full bg-transparent text-gray-300 placeholder-gray-500 outline-none"
                />
              </div>
              <button className="p-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                <ArrowLeft className="w-4 h-4 text-white rotate-180" />
              </button>
            </div>
          </div>
        </div>

        {/* Right Panel - Mobile Preview */}
        <div className="w-96 bg-gray-800 border-l border-gray-700 flex flex-col">
          {/* Preview Header */}
          <div className="p-6 border-b border-gray-700">
            <h3 className="text-white font-semibold mb-4">Test on your phone</h3>
            
            {/* QR Code */}
            <div className="bg-white p-4 rounded-lg mb-4 flex items-center justify-center">
              <div className="w-32 h-32 bg-black rounded grid grid-cols-8 gap-px p-2">
                {Array.from({ length: 64 }).map((_, i) => (
                  <div 
                    key={i} 
                    className={`${Math.random() > 0.5 ? 'bg-white' : 'bg-black'} rounded-sm`}
                  />
                ))}
              </div>
            </div>
            
            <div className="text-gray-400 text-sm space-y-1">
              <p className="font-medium text-white">Scan QR code to test</p>
              <p>1. Install Expo Go</p>
              <p>2. Open Camera app</p>
              <p>3. Scan the QR code above</p>
            </div>
            
            <div className="mt-4 p-3 bg-gray-700 rounded-lg">
              <div className="flex items-center gap-2 text-gray-400 text-sm">
                <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                <span>Browser preview apps cannot function & looks different. Test on device for the best results.</span>
              </div>
            </div>
          </div>

          {/* Phone Mockup */}
          <div className="flex-1 flex items-center justify-center p-6">
            <div className="relative">
              {/* iPhone Frame */}
              <div className="w-64 h-[520px] bg-black rounded-[2.5rem] p-2 shadow-2xl">
                <div className="w-full h-full bg-white rounded-[2rem] relative overflow-hidden">
                  {/* Status Bar */}
                  <div className="absolute top-0 left-0 right-0 h-12 bg-white flex items-center justify-between px-6 text-black text-sm z-10">
                    <span className="font-semibold">9:41</span>
                    <div className="flex items-center gap-1">
                      <div className="flex gap-1">
                        <div className="w-1 h-3 bg-black rounded-full"></div>
                        <div className="w-1 h-3 bg-black rounded-full"></div>
                        <div className="w-1 h-3 bg-black rounded-full"></div>
                        <div className="w-1 h-3 bg-gray-400 rounded-full"></div>
                      </div>
                      <div className="w-6 h-3 border border-black rounded-sm ml-1">
                        <div className="w-4 h-2 bg-black rounded-sm m-0.5"></div>
                      </div>
                    </div>
                  </div>

                  {/* App Content */}
                  <div className="pt-12 h-full bg-white overflow-hidden">
                    <div className="p-4">
                      {/* Header */}
                      <div className="mb-4">
                        <h1 className="text-xl font-bold text-gray-900 mb-1">Where to?</h1>
                        <p className="text-gray-600 text-sm">Find your perfect stay</p>
                      </div>
                      
                      {/* Search Bar */}
                      <div className="bg-gray-100 p-4 rounded-xl border border-gray-200 mb-4 shadow-sm">
                        <p className="text-gray-500 text-sm">Where are you going?</p>
                      </div>
                      
                      {/* Filter Buttons */}
                      <div className="flex gap-2 mb-4">
                        <div className="flex-1 bg-gray-50 p-3 rounded-lg border border-gray-200">
                          <p className="text-center text-xs text-gray-700">Any week</p>
                        </div>
                        <div className="flex-1 bg-gray-50 p-3 rounded-lg border border-gray-200">
                          <p className="text-center text-xs text-gray-700">Add guests</p>
                        </div>
                        <div className="bg-red-500 p-3 rounded-lg">
                          <div className="w-4 h-4 bg-white rounded"></div>
                        </div>
                      </div>
                      
                      {/* Categories */}
                      <div className="flex gap-2 mb-4 overflow-x-auto">
                        {['Cabins', 'Beachfront', 'Trending'].map((category, i) => (
                          <div key={i} className="flex-shrink-0 text-center">
                            <div className="w-12 h-12 bg-gray-100 rounded-lg mb-1"></div>
                            <p className="text-xs text-gray-600">{category}</p>
                          </div>
                        ))}
                      </div>
                      
                      {/* Property Card */}
                      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                        <div className="h-32 bg-gradient-to-br from-orange-200 via-red-200 to-pink-200 relative">
                          <div className="absolute top-2 right-2 bg-white rounded-full p-1">
                            <div className="w-4 h-4 bg-gray-300 rounded"></div>
                          </div>
                        </div>
                        <div className="p-3">
                          <div className="flex items-center justify-between mb-1">
                            <h3 className="font-semibold text-gray-900 text-sm">Modern Loft with City View</h3>
                            <div className="flex items-center gap-1">
                              <div className="w-3 h-3 bg-yellow-400 rounded"></div>
                              <span className="text-xs text-gray-600">4.9</span>
                            </div>
                          </div>
                          <p className="text-gray-600 text-xs mb-1">New York, NY</p>
                          <p className="text-gray-900 font-semibold text-sm">$199 <span className="font-normal text-gray-600">night</span></p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Home Indicator */}
                  <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black rounded-full opacity-30"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Actions */}
          <div className="p-4 border-t border-gray-700">
            <div className="flex gap-2">
              <button className="flex-1 flex items-center justify-center gap-2 py-2 bg-gray-700 text-gray-300 rounded text-sm hover:bg-gray-600 transition-colors">
                <Download className="w-4 h-4" />
                Export
              </button>
              <button className="flex-1 flex items-center justify-center gap-2 py-2 bg-gray-700 text-gray-300 rounded text-sm hover:bg-gray-600 transition-colors">
                <Share2 className="w-4 h-4" />
                Share
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileAppBuilder;