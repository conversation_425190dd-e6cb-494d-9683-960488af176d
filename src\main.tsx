import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { AppErrorBoundary } from './components/error/AppErrorBoundary';
import App from './App.tsx';
import { preloadCriticalComponents, preloadOnInteraction, preloadHeavyFeatures, analyzeBundleSize } from './utils/dynamicImports';
import './index.css';

// Preload critical components immediately
preloadCriticalComponents();

// Analyze bundle size in development
analyzeBundleSize();

// Register service worker for offline functionality
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Preload components on user interaction
const preloadOnFirstInteraction = () => {
  preloadOnInteraction();
  preloadHeavyFeatures();
  
  // Remove listeners after first interaction
  document.removeEventListener('mousedown', preloadOnFirstInteraction);
  document.removeEventListener('touchstart', preloadOnFirstInteraction);
  document.removeEventListener('keydown', preloadOnFirstInteraction);
};

// Add interaction listeners
document.addEventListener('mousedown', preloadOnFirstInteraction, { passive: true });
document.addEventListener('touchstart', preloadOnFirstInteraction, { passive: true });
document.addEventListener('keydown', preloadOnFirstInteraction, { passive: true });

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <AppErrorBoundary>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </AppErrorBoundary>
  </StrictMode>
);
