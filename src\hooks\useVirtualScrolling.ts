// Custom hook for virtual scrolling implementation

import { useState, useEffect, useMemo, useCallback, useRef } from 'react';

export interface VirtualScrollingOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number; // Number of items to render outside visible area
  enabled?: boolean;
}

export interface VirtualScrollingReturn<T> {
  containerRef: React.RefObject<HTMLDivElement>;
  visibleItems: Array<{
    item: T;
    index: number;
    offsetTop: number;
  }>;
  totalHeight: number;
  scrollToIndex: (index: number) => void;
  scrollToTop: () => void;
}

export function useVirtualScrolling<T>(
  items: T[],
  options: VirtualScrollingOptions
): VirtualScrollingReturn<T> {
  const {
    itemHeight,
    containerHeight,
    overscan = 5,
    enabled = true
  } = options;

  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    if (!enabled || items.length === 0) {
      return { start: 0, end: items.length };
    }

    const visibleStart = Math.floor(scrollTop / itemHeight);
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(containerHeight / itemHeight),
      items.length
    );

    // Add overscan
    const start = Math.max(0, visibleStart - overscan);
    const end = Math.min(items.length, visibleEnd + overscan);

    return { start, end };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan, enabled]);

  // Calculate visible items
  const visibleItems = useMemo(() => {
    if (!enabled) {
      return items.map((item, index) => ({
        item,
        index,
        offsetTop: index * itemHeight
      }));
    }

    const result = [];
    for (let i = visibleRange.start; i < visibleRange.end; i++) {
      result.push({
        item: items[i],
        index: i,
        offsetTop: i * itemHeight
      });
    }
    return result;
  }, [items, visibleRange, itemHeight, enabled]);

  // Total height for scrollbar
  const totalHeight = useMemo(() => {
    return items.length * itemHeight;
  }, [items.length, itemHeight]);

  // Handle scroll events
  const handleScroll = useCallback((event: Event) => {
    const target = event.target as HTMLDivElement;
    setScrollTop(target.scrollTop);
  }, []);

  // Attach scroll listener
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !enabled) return;

    container.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll, enabled]);

  // Scroll to specific index
  const scrollToIndex = useCallback((index: number) => {
    const container = containerRef.current;
    if (!container) return;

    const targetScrollTop = Math.max(0, index * itemHeight);
    container.scrollTop = targetScrollTop;
  }, [itemHeight]);

  // Scroll to top
  const scrollToTop = useCallback(() => {
    const container = containerRef.current;
    if (!container) return;

    container.scrollTop = 0;
  }, []);

  // Reset scroll position when items change significantly
  useEffect(() => {
    if (scrollTop > totalHeight) {
      scrollToTop();
    }
  }, [totalHeight, scrollTop, scrollToTop]);

  return {
    containerRef,
    visibleItems,
    totalHeight,
    scrollToIndex,
    scrollToTop
  };
}