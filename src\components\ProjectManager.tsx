import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Search, Filter } from 'lucide-react';
import { Project } from '../types/project';
import { ProjectCRUDService } from '../services/projectCRUD';
import { useApp } from '../context/AppContext';
import ProjectList from './ProjectList';
import ProjectForm from './ProjectForm';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import Button from './ui/Button';
import Input from './ui/Input';
import { animationVariants } from '../utils/animations';

interface ProjectManagerProps {
  className?: string;
}

const ProjectManager: React.FC<ProjectManagerProps> = ({ className }) => {
  const { 
    projects, 
    addProject, 
    updateProject, 
    deleteProject,
    addOptimisticUpdate, 
    removeOptimisticUpdate 
  } = useApp();

  // Modal states
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  // UI states
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  // Filter projects based on search
  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Handle project actions
  const handleProjectAction = async (projectId: string, action: 'star' | 'archive' | 'delete' | 'duplicate' | 'edit') => {
    const project = projects.find(p => p.id === projectId);
    if (!project) return;

    switch (action) {
      case 'edit':
        setSelectedProject(project);
        setShowEditForm(true);
        break;

      case 'delete':
        setSelectedProject(project);
        setShowDeleteModal(true);
        break;

      case 'duplicate':
        await handleDuplicateProject(project);
        break;

      case 'star':
      case 'archive':
        // These are handled directly in ProjectCard with optimistic updates
        break;
    }
  };

  // Handle project duplication
  const handleDuplicateProject = async (originalProject: Project) => {
    try {
      await ProjectCRUDService.duplicateProject(
        originalProject,
        // Optimistic update
        (duplicateProject) => {
          addOptimisticUpdate(duplicateProject.id, duplicateProject);
          addProject(duplicateProject);
        },
        // Success
        (duplicateProject) => {
          removeOptimisticUpdate(duplicateProject.id);
        },
        // Error
        (error) => {
          console.error('Failed to duplicate project:', error);
          // Remove optimistic update on error
          deleteProject(originalProject.id + '_duplicate'); // This would need proper ID tracking
        }
      );
    } catch (error) {
      console.error('Duplicate operation failed:', error);
    }
  };

  // Handle project click
  const handleProjectClick = (project: Project) => {
    // Navigate to project or open project details
    console.log('Project clicked:', project.name);
  };

  // Handle create new project
  const handleCreateProject = () => {
    setSelectedProject(null);
    setShowCreateForm(true);
  };

  // Close modals
  const closeModals = () => {
    setShowCreateForm(false);
    setShowEditForm(false);
    setShowDeleteModal(false);
    setSelectedProject(null);
  };

  return (
    <div className={className}>
      {/* Header */}
      <motion.div
        variants={animationVariants.slideUp}
        initial="initial"
        animate="animate"
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6"
      >
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Projects</h1>
          <p className="text-gray-600 mt-1">
            Manage your AI-generated projects and track their progress
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="primary"
            onClick={handleCreateProject}
            leftIcon={<Plus className="w-4 h-4" />}
            disabled={isCreating}
          >
            New Project
          </Button>
        </div>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        variants={animationVariants.slideUp}
        initial="initial"
        animate="animate"
        transition={{ delay: 0.1 }}
        className="flex flex-col sm:flex-row gap-4 mb-6"
      >
        <div className="flex-1">
          <Input
            type="text"
            placeholder="Search projects..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="w-4 h-4" />}
          />
        </div>
        
        <Button
          variant="outline"
          leftIcon={<Filter className="w-4 h-4" />}
        >
          Filters
        </Button>
      </motion.div>

      {/* Stats */}
      <motion.div
        variants={animationVariants.slideUp}
        initial="initial"
        animate="animate"
        transition={{ delay: 0.2 }}
        className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-8"
      >
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-2xl font-bold text-gray-900">{projects.length}</div>
          <div className="text-sm text-gray-600">Total Projects</div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-2xl font-bold text-green-600">
            {projects.filter(p => p.status === 'active').length}
          </div>
          <div className="text-sm text-gray-600">Active</div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-2xl font-bold text-blue-600">
            {projects.filter(p => p.status === 'completed').length}
          </div>
          <div className="text-sm text-gray-600">Completed</div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-2xl font-bold text-yellow-600">
            {projects.filter(p => p.isStarred).length}
          </div>
          <div className="text-sm text-gray-600">Starred</div>
        </div>
      </motion.div>

      {/* Project List */}
      <motion.div
        variants={animationVariants.slideUp}
        initial="initial"
        animate="animate"
        transition={{ delay: 0.3 }}
      >
        <ProjectList
          projects={filteredProjects}
          showDragHandle={true}
          enableReordering={true}
          enableDetailView={true}
          onProjectClick={handleProjectClick}
          onProjectAction={handleProjectAction}
          variant="default"
        />
      </motion.div>

      {/* Modals */}
      <ProjectForm
        isOpen={showCreateForm}
        onClose={closeModals}
        mode="create"
      />

      <ProjectForm
        isOpen={showEditForm}
        onClose={closeModals}
        project={selectedProject}
        mode="edit"
      />

      <DeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={closeModals}
        project={selectedProject}
      />
    </div>
  );
};

export default ProjectManager;