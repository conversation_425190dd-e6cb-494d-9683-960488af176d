import { Project } from '../types/project';

export class OfflineProjectService {
  private static instance: OfflineProjectService;
  private db: IDBDatabase | null = null;

  private constructor() {}

  static getInstance(): OfflineProjectService {
    if (!OfflineProjectService.instance) {
      OfflineProjectService.instance = new OfflineProjectService();
    }
    return OfflineProjectService.instance;
  }

  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('AppGeneratorDB', 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Projects store
        if (!db.objectStoreNames.contains('projects')) {
          const projectStore = db.createObjectStore('projects', { keyPath: 'id' });
          projectStore.createIndex('lastModified', 'lastModified');
          projectStore.createIndex('status', 'status');
        }
        
        // Offline actions store
        if (!db.objectStoreNames.contains('offline-actions')) {
          const actionStore = db.createObjectStore('offline-actions', { keyPath: 'id' });
          actionStore.createIndex('timestamp', 'timestamp');
        }
        
        // Cache store for generated content
        if (!db.objectStoreNames.contains('cache')) {
          const cacheStore = db.createObjectStore('cache', { keyPath: 'key' });
          cacheStore.createIndex('timestamp', 'timestamp');
        }
      };
    });
  }

  // Project CRUD operations with offline support
  async saveProject(project: Project, isOffline = false): Promise<void> {
    if (!this.db) await this.initialize();
    
    const transaction = this.db!.transaction(['projects'], 'readwrite');
    const store = transaction.objectStore('projects');
    
    await store.put({
      ...project,
      lastModified: new Date().toISOString(),
      syncStatus: isOffline ? 'pending' : 'synced'
    });

    // If offline, queue for sync
    if (isOffline) {
      await this.queueOfflineAction('UPDATE_PROJECT', project);
    }
  }

  async getProject(id: string): Promise<Project | null> {
    if (!this.db) await this.initialize();
    
    const transaction = this.db!.transaction(['projects'], 'readonly');
    const store = transaction.objectStore('projects');
    const result = await store.get(id);
    
    return result || null;
  }

  async getAllProjects(): Promise<Project[]> {
    if (!this.db) await this.initialize();
    
    const transaction = this.db!.transaction(['projects'], 'readonly');
    const store = transaction.objectStore('projects');
    const result = await store.getAll();
    
    return result || [];
  }

  async deleteProject(id: string, isOffline = false): Promise<void> {
    if (!this.db) await this.initialize();
    
    const transaction = this.db!.transaction(['projects'], 'readwrite');
    const store = transaction.objectStore('projects');
    
    await store.delete(id);

    // If offline, queue for sync
    if (isOffline) {
      await this.queueOfflineAction('DELETE_PROJECT', { id });
    }
  }

  // Cache management for offline content
  async cacheContent(key: string, content: any, ttl = 24 * 60 * 60 * 1000): Promise<void> {
    if (!this.db) await this.initialize();
    
    const transaction = this.db!.transaction(['cache'], 'readwrite');
    const store = transaction.objectStore('cache');
    
    await store.put({
      key,
      content,
      timestamp: Date.now(),
      expires: Date.now() + ttl
    });
  }

  async getCachedContent(key: string): Promise<any | null> {
    if (!this.db) await this.initialize();
    
    const transaction = this.db!.transaction(['cache'], 'readonly');
    const store = transaction.objectStore('cache');
    const result = await store.get(key);
    
    if (!result) return null;
    
    // Check if expired
    if (result.expires < Date.now()) {
      // Clean up expired cache
      const deleteTransaction = this.db!.transaction(['cache'], 'readwrite');
      const deleteStore = deleteTransaction.objectStore('cache');
      await deleteStore.delete(key);
      return null;
    }
    
    return result.content;
  }

  // Offline action queue management
  private async queueOfflineAction(type: string, data: any): Promise<void> {
    if (!this.db) await this.initialize();
    
    const transaction = this.db!.transaction(['offline-actions'], 'readwrite');
    const store = transaction.objectStore('offline-actions');
    
    await store.add({
      id: `${Date.now()}-${Math.random()}`,
      type,
      data,
      timestamp: Date.now()
    });
  }

  async getPendingActions(): Promise<any[]> {
    if (!this.db) await this.initialize();
    
    const transaction = this.db!.transaction(['offline-actions'], 'readonly');
    const store = transaction.objectStore('offline-actions');
    const result = await store.getAll();
    
    return result || [];
  }

  async clearProcessedActions(actionIds: string[]): Promise<void> {
    if (!this.db) await this.initialize();
    
    const transaction = this.db!.transaction(['offline-actions'], 'readwrite');
    const store = transaction.objectStore('offline-actions');
    
    await Promise.all(actionIds.map(id => store.delete(id)));
  }

  // Sync operations
  async syncWithServer(): Promise<void> {
    const pendingActions = await this.getPendingActions();
    const processedIds: string[] = [];
    
    for (const action of pendingActions) {
      try {
        await this.processAction(action);
        processedIds.push(action.id);
      } catch (error) {
        console.error('Failed to sync action:', action, error);
        // Continue with other actions
      }
    }
    
    if (processedIds.length > 0) {
      await this.clearProcessedActions(processedIds);
    }
  }

  private async processAction(action: any): Promise<void> {
    switch (action.type) {
      case 'UPDATE_PROJECT':
        // Sync project update with server
        console.log('Syncing project update:', action.data);
        break;
      case 'DELETE_PROJECT':
        // Sync project deletion with server
        console.log('Syncing project deletion:', action.data);
        break;
      default:
        console.warn('Unknown action type:', action.type);
    }
  }

  // Cleanup old cache entries
  async cleanupCache(): Promise<void> {
    if (!this.db) await this.initialize();
    
    const transaction = this.db!.transaction(['cache'], 'readwrite');
    const store = transaction.objectStore('cache');
    const index = store.index('timestamp');
    
    const cutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days ago
    const range = IDBKeyRange.upperBound(cutoff);
    
    const cursor = await index.openCursor(range);
    const toDelete: string[] = [];
    
    cursor?.onsuccess = (event) => {
      const cursor = (event.target as IDBRequest).result;
      if (cursor) {
        toDelete.push(cursor.value.key);
        cursor.continue();
      } else {
        // Delete expired entries
        toDelete.forEach(key => store.delete(key));
      }
    };
  }
}