@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Mobile-first responsive base styles */
  html {
    font-size: 16px;
    scroll-behavior: smooth;
  }
  
  body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }
  
  /* Responsive font scaling */
  @media (max-width: 640px) {
    html {
      font-size: 14px;
    }
  }
  
  @media (min-width: 1024px) {
    html {
      font-size: 16px;
    }
  }
  
  @media (min-width: 1280px) {
    html {
      font-size: 18px;
    }
  }
}

@layer components {
  /* Standardized Container System */
  .container-base {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }
  
  /* Mobile-first responsive container with consistent padding */
  .container-responsive {
    @apply container-base;
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: 100%;
  }
  
  @media (min-width: 640px) {
    .container-responsive {
      max-width: 640px;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }
  
  @media (min-width: 768px) {
    .container-responsive {
      max-width: 768px;
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
  
  @media (min-width: 1024px) {
    .container-responsive {
      max-width: 1024px;
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
  
  @media (min-width: 1280px) {
    .container-responsive {
      max-width: 1280px;
      padding-left: 2.5rem;
      padding-right: 2.5rem;
    }
  }
  
  @media (min-width: 1536px) {
    .container-responsive {
      max-width: 1536px;
      padding-left: 3rem;
      padding-right: 3rem;
    }
  }
  
  /* Container variants for different use cases */
  .container-tight {
    @apply container-base;
    max-width: 48rem; /* 768px */
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .container-wide {
    @apply container-base;
    max-width: 80rem; /* 1280px */
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .container-full {
    @apply container-base;
    max-width: none;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  /* Responsive grid system with consistent spacing */
  .grid-responsive {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
  }
  
  @media (min-width: 640px) {
    .grid-responsive {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
    }
  }
  
  @media (min-width: 1024px) {
    .grid-responsive {
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
    }
  }
  
  @media (min-width: 1280px) {
    .grid-responsive {
      grid-template-columns: repeat(4, 1fr);
      gap: 2rem;
    }
  }
  
  /* Responsive flexbox utilities */
  .flex-responsive {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  @media (min-width: 768px) {
    .flex-responsive {
      flex-direction: row;
      gap: 1.5rem;
    }
  }
  
  /* Standardized spacing utilities */
  .spacing-section {
    margin-bottom: 3rem;
  }
  
  @media (min-width: 768px) {
    .spacing-section {
      margin-bottom: 4rem;
    }
  }
  
  .spacing-component {
    margin-bottom: 1.5rem;
  }
  
  @media (min-width: 768px) {
    .spacing-component {
      margin-bottom: 2rem;
    }
  }
  
  .spacing-element {
    margin-bottom: 1rem;
  }
  
  /* Mobile-first sidebar layout */
  .sidebar-layout {
    display: flex;
    min-height: 100vh;
  }
  
  .sidebar-content {
    flex: 1;
    min-width: 0; /* Prevent flex overflow */
    transition: margin-left 0.3s ease-in-out;
  }
  
  /* Safe area utilities for mobile */
  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
  
  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-area-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
  
  /* Touch target utilities */
  .touch-target {
    min-width: 44px;
    min-height: 44px;
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideUp {
    from { 
      opacity: 0;
      transform: translateY(20px);
    }
    to { 
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes scaleIn {
    from { 
      opacity: 0;
      transform: scale(0.95);
    }
    to { 
      opacity: 1;
      transform: scale(1);
    }
  }
}

@layer utilities {
  /* Mobile optimizations */
  .mobile-optimized {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
  
  .touch-device * {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
  
  .touch-feedback {
    transform: scale(0.95);
    transition: transform 0.1s ease-out;
  }
  
  /* Optimize animations for mobile */
  @media (prefers-reduced-motion: no-preference) {
    .mobile-animate {
      transform: translateZ(0);
      backface-visibility: hidden;
      perspective: 1000px;
    }
  }
  
  /* Scroll optimizations */
  .smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Content visibility optimizations */
  .content-visibility-auto {
    content-visibility: auto;
  }
  
  /* Performance optimizations */
  .will-change-transform {
    will-change: transform;
  }
  
  .will-change-opacity {
    will-change: opacity;
  }
  
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
  }
  
  /* Accessibility utilities */
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2;
  }
  
  .sr-only-focusable {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
  }
  
  .sr-only-focusable:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: inherit !important;
    margin: inherit !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
  }
}
