// Fuzzy search utility for project search functionality

export interface SearchMatch {
  score: number;
  matches: Array<{
    field: string;
    value: string;
    indices: number[];
  }>;
}

export interface SearchResult<T> {
  item: T;
  match: SearchMatch;
}

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
}

/**
 * Calculate fuzzy match score between query and text
 */
function calculateFuzzyScore(query: string, text: string): number {
  if (!query || !text) return 0;
  
  const queryLower = query.toLowerCase();
  const textLower = text.toLowerCase();
  
  // Exact match gets highest score
  if (textLower === queryLower) return 1;
  
  // Starts with query gets high score
  if (textLower.startsWith(queryLower)) return 0.9;
  
  // Contains query gets medium score
  if (textLower.includes(queryLower)) return 0.7;
  
  // Calculate fuzzy score based on Levenshtein distance
  const distance = levenshteinDistance(queryLower, textLower);
  const maxLength = Math.max(queryLower.length, textLower.length);
  const similarity = 1 - (distance / maxLength);
  
  // Only return scores above threshold
  return similarity > 0.3 ? similarity * 0.6 : 0;
}

/**
 * Find matching indices in text for highlighting
 */
function findMatchIndices(query: string, text: string): number[] {
  if (!query || !text) return [];
  
  const queryLower = query.toLowerCase();
  const textLower = text.toLowerCase();
  const indices: number[] = [];
  
  // Find exact substring matches
  let index = textLower.indexOf(queryLower);
  while (index !== -1) {
    for (let i = 0; i < queryLower.length; i++) {
      indices.push(index + i);
    }
    index = textLower.indexOf(queryLower, index + 1);
  }
  
  // If no exact matches, find individual character matches
  if (indices.length === 0) {
    for (let i = 0; i < queryLower.length; i++) {
      const charIndex = textLower.indexOf(queryLower[i]);
      if (charIndex !== -1) {
        indices.push(charIndex);
      }
    }
  }
  
  return [...new Set(indices)].sort((a, b) => a - b);
}

/**
 * Perform fuzzy search on a collection of items
 */
export function fuzzySearch<T>(
  items: T[],
  query: string,
  searchFields: Array<keyof T | ((item: T) => string)>,
  options: {
    threshold?: number;
    limit?: number;
    includeMatches?: boolean;
  } = {}
): SearchResult<T>[] {
  const { threshold = 0.1, limit = 50, includeMatches = true } = options;
  
  if (!query.trim()) return items.map(item => ({ item, match: { score: 1, matches: [] } }));
  
  const results: SearchResult<T>[] = [];
  
  for (const item of items) {
    let bestScore = 0;
    const matches: SearchMatch['matches'] = [];
    
    for (const field of searchFields) {
      let text: string;
      let fieldName: string;
      
      if (typeof field === 'function') {
        text = field(item);
        fieldName = 'computed';
      } else {
        text = String(item[field] || '');
        fieldName = String(field);
      }
      
      const score = calculateFuzzyScore(query, text);
      
      if (score > bestScore) {
        bestScore = score;
      }
      
      if (score > threshold && includeMatches) {
        const indices = findMatchIndices(query, text);
        matches.push({
          field: fieldName,
          value: text,
          indices
        });
      }
    }
    
    if (bestScore > threshold) {
      results.push({
        item,
        match: {
          score: bestScore,
          matches
        }
      });
    }
  }
  
  // Sort by score (descending) and limit results
  return results
    .sort((a, b) => b.match.score - a.match.score)
    .slice(0, limit);
}

/**
 * Highlight matching text with HTML spans
 */
export function highlightMatches(text: string, indices: number[]): string {
  if (!indices.length) return text;
  
  let result = '';
  let lastIndex = 0;
  
  // Group consecutive indices
  const groups: Array<{ start: number; end: number }> = [];
  let currentGroup = { start: indices[0], end: indices[0] };
  
  for (let i = 1; i < indices.length; i++) {
    if (indices[i] === currentGroup.end + 1) {
      currentGroup.end = indices[i];
    } else {
      groups.push(currentGroup);
      currentGroup = { start: indices[i], end: indices[i] };
    }
  }
  groups.push(currentGroup);
  
  // Build highlighted string
  for (const group of groups) {
    result += text.slice(lastIndex, group.start);
    result += `<mark class="bg-yellow-200 dark:bg-yellow-800 px-0.5 rounded">${text.slice(group.start, group.end + 1)}</mark>`;
    lastIndex = group.end + 1;
  }
  result += text.slice(lastIndex);
  
  return result;
}