// Empty state for no search results

import React from 'react';
import { Search, RotateCcw, Plus, Filter } from 'lucide-react';
import EmptyState, { EmptyStateAction } from './EmptyState';

interface NoSearchResultsProps {
  query: string;
  hasFilters: boolean;
  onClearSearch: () => void;
  onResetFilters: () => void;
  onCreateProject?: () => void;
  onOpenFilters?: () => void;
  className?: string;
}

const NoSearchResults: React.FC<NoSearchResultsProps> = ({
  query,
  hasFilters,
  onClearSearch,
  onResetFilters,
  onCreateProject,
  onOpenFilters,
  className
}) => {
  const getTitle = () => {
    if (query && hasFilters) {
      return 'No projects match your search and filters';
    } else if (query) {
      return `No results for "${query}"`;
    } else if (hasFilters) {
      return 'No projects match your filters';
    }
    return 'No projects found';
  };

  const getDescription = () => {
    if (query && hasFilters) {
      return 'Try adjusting your search terms or removing some filters to see more results.';
    } else if (query) {
      return 'Try searching for something else or check your spelling. You can also create a new project with this idea.';
    } else if (hasFilters) {
      return 'Try removing some filters or adjusting your criteria to see more projects.';
    }
    return 'Get started by creating your first project or adjusting your search criteria.';
  };

  const actions: EmptyStateAction[] = [];

  // Add clear search action
  if (query) {
    actions.push({
      label: 'Clear search',
      onClick: onClearSearch,
      variant: 'secondary',
      icon: RotateCcw
    });
  }

  // Add reset filters action
  if (hasFilters) {
    actions.push({
      label: 'Reset filters',
      onClick: onResetFilters,
      variant: 'secondary',
      icon: Filter
    });
  }

  // Add open filters action if not already filtered
  if (!hasFilters && onOpenFilters) {
    actions.push({
      label: 'Open filters',
      onClick: onOpenFilters,
      variant: 'secondary',
      icon: Filter
    });
  }

  // Add create project action
  if (onCreateProject) {
    actions.push({
      label: query ? `Create "${query}" project` : 'Create new project',
      onClick: onCreateProject,
      variant: 'primary',
      icon: Plus
    });
  }

  return (
    <EmptyState
      icon={Search}
      title={getTitle()}
      description={getDescription()}
      actions={actions}
      className={className}
    />
  );
};

export default NoSearchResults;