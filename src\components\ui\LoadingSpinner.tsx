import React from 'react';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { cn } from '../../utils/helpers';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'dots' | 'pulse' | 'bars';
  color?: 'primary' | 'secondary' | 'white' | 'gray';
  className?: string;
  text?: string;
}

const spinVariants = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: "linear"
    }
  }
};

const pulseVariants = {
  animate: {
    scale: [1, 1.2, 1],
    opacity: [1, 0.7, 1],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

const dotsVariants = {
  animate: {
    transition: {
      staggerChildren: 0.2,
      repeat: Infinity,
      repeatType: "loop" as const
    }
  }
};

const dotVariants = {
  animate: {
    y: [0, -10, 0],
    transition: {
      duration: 0.6,
      ease: "easeInOut"
    }
  }
};

const barsVariants = {
  animate: {
    transition: {
      staggerChildren: 0.1,
      repeat: Infinity,
      repeatType: "loop" as const
    }
  }
};

const barVariants = {
  animate: {
    scaleY: [1, 2, 1],
    transition: {
      duration: 0.8,
      ease: "easeInOut"
    }
  }
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  color = 'primary',
  className,
  text
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  };

  const colorClasses = {
    primary: 'text-blue-600',
    secondary: 'text-gray-600',
    white: 'text-white',
    gray: 'text-gray-400'
  };

  const dotSizeClasses = {
    sm: 'h-1 w-1',
    md: 'h-1.5 w-1.5',
    lg: 'h-2 w-2',
    xl: 'h-3 w-3'
  };

  const barSizeClasses = {
    sm: 'h-3 w-0.5',
    md: 'h-4 w-1',
    lg: 'h-6 w-1',
    xl: 'h-8 w-1.5'
  };

  const renderSpinner = () => {
    switch (variant) {
      case 'dots':
        return (
          <motion.div
            variants={dotsVariants}
            animate="animate"
            className="flex space-x-1"
          >
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                variants={dotVariants}
                className={cn(
                  'rounded-full bg-current',
                  dotSizeClasses[size]
                )}
              />
            ))}
          </motion.div>
        );

      case 'pulse':
        return (
          <motion.div
            variants={pulseVariants}
            animate="animate"
            className={cn(
              'rounded-full bg-current',
              sizeClasses[size]
            )}
          />
        );

      case 'bars':
        return (
          <motion.div
            variants={barsVariants}
            animate="animate"
            className="flex items-end space-x-0.5"
          >
            {[0, 1, 2, 3].map((i) => (
              <motion.div
                key={i}
                variants={barVariants}
                className={cn(
                  'bg-current origin-bottom',
                  barSizeClasses[size]
                )}
              />
            ))}
          </motion.div>
        );

      default:
        return (
          <motion.div
            variants={spinVariants}
            animate="animate"
          >
            <Loader2 className={sizeClasses[size]} />
          </motion.div>
        );
    }
  };

  return (
    <div className={cn(
      'flex flex-col items-center justify-center',
      colorClasses[color],
      className
    )}>
      {renderSpinner()}
      {text && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="mt-2 text-sm text-current"
        >
          {text}
        </motion.p>
      )}
    </div>
  );
};

export default LoadingSpinner;