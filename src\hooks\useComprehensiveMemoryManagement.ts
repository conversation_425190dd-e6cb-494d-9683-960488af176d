/**
 * Comprehensive Memory Management Hook
 * Addresses all memory management requirements including:
 * - Event listener cleanup
 * - Timer cleanup
 * - Animation frame cleanup
 * - Proper dependency arrays
 * - Memory leak prevention for animations
 * - Efficient data structures for large lists
 */

import { useEffect, useRef, useCallback, useMemo } from 'react';
import { 
  useMemoryManager, 
  useAnimationFrameManager,
  useObjectPool,
  useMemoryLeakDetector
} from './useMemoryManager';
import { MemoryEfficientList } from '../utils/memoryManagement';

// Define PerformanceMemory interface for better type safety
interface PerformanceMemory {
  readonly usedJSHeapSize: number;
  readonly totalJSHeapSize: number;
  readonly jsHeapSizeLimit: number;
}

export interface MemoryManagementOptions {
  enableLeakDetection?: boolean;
  enableMemoryMonitoring?: boolean;
  listVirtualization?: {
    itemHeight?: number;
    containerHeight?: number;
    overscan?: number;
  } | undefined;
  objectPooling?: {
    maxSize?: number;
  };
}

/**
 * Comprehensive memory management hook that provides all memory optimization features
 */
export function useComprehensiveMemoryManagement(options: MemoryManagementOptions = {}) {
  const {
    enableLeakDetection = process.env['NODE_ENV'] === 'development',
    enableMemoryMonitoring = false,
    listVirtualization,
    objectPooling
  } = options;

  // Core memory manager
  const memoryManager = useMemoryManager();
  
  // Animation frame manager for preventing animation memory leaks
  const animationManager = useAnimationFrameManager();
  
  // Memory leak detector for development
  const leakDetector = useMemoryLeakDetector();

  // Object pool for reusable objects
  const defaultObjectPool = useObjectPool(
    () => ({}),
    () => {},
    objectPooling?.maxSize || 100
  );

  // Memory monitoring state
  const memoryStatsRef = useRef({
    lastGCTime: Date.now(),
    peakMemoryUsage: 0,
    averageMemoryUsage: 0,
    memoryReadings: [] as number[]
  });

  /**
   * Enhanced event listener management with automatic cleanup
   */
  const addManagedEventListener = useCallback(<K extends keyof WindowEventMap>(
    element: EventTarget | null,
    eventName: K | string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ) => {
    if (!element) return () => {};

    const cleanup = memoryManager.addEventListener(element, eventName, handler, options);
    
    if (enableLeakDetection) {
      // Track for leak detection
      const { MemoryLeakDetector } = await import('../utils/memoryManagement');
      const detector = MemoryLeakDetector.getInstance();
      detector.trackEventListener(element, eventName, handler);
      
      // Return enhanced cleanup that also untracks
      return () => {
        cleanup.cleanup();
        detector.untrackEventListener(element, eventName, handler);
      };
    }
    
    return cleanup.cleanup;
  }, [memoryManager, enableLeakDetection]);

  /**
   * Enhanced timer management with leak prevention
   */
  const addManagedTimer = useCallback((
    callback: () => void,
    delay: number,
    type: 'timeout' | 'interval' = 'timeout'
  ) => {
    const timer = memoryManager.addTimer(callback, delay, type);
    
    if (enableLeakDetection) {
      import('../utils/memoryManagement').then(({ MemoryLeakDetector }) => {
        const detector = MemoryLeakDetector.getInstance();
        detector.trackTimer(timer.id, type);
        
        // Enhanced cleanup that also untracks
        const originalCleanup = timer.cleanup;
        timer.cleanup = () => {
          originalCleanup();
          detector.untrackTimer(timer.id, type);
        };
      });
    }
    
    return timer;
  }, [memoryManager, enableLeakDetection]);

  /**
   * Enhanced animation frame management
   */
  const requestManagedAnimationFrame = useCallback((callback: () => void) => {
    const frameId = animationManager.requestFrame(callback);
    
    if (enableLeakDetection) {
      import('../utils/memoryManagement').then(({ MemoryLeakDetector }) => {
        const detector = MemoryLeakDetector.getInstance();
        detector.trackAnimationFrame(frameId);
      });
    }
    
    return frameId;
  }, [animationManager, enableLeakDetection]);

  const cancelManagedAnimationFrame = useCallback((frameId: number) => {
    animationManager.cancelFrame(frameId);
    
    if (enableLeakDetection) {
      import('../utils/memoryManagement').then(({ MemoryLeakDetector }) => {
        const detector = MemoryLeakDetector.getInstance();
        detector.untrackAnimationFrame(frameId);
      });
    }
  }, [animationManager, enableLeakDetection]);

  /**
   * Memory-efficient list management
   */
  const createManagedList = useCallback(<T>(
    items: T[],
    options?: {
      itemHeight?: number;
      containerHeight?: number;
      overscan?: number;
    }
  ) => {
    const listManager = new MemoryEfficientList(items, {
      ...listVirtualization,
      ...options
    });
    
    // Add cleanup for the list manager
    memoryManager.addCleanup(() => {
      listManager.cleanup();
    });
    
    return {
      updateVisibleRange: (scrollTop: number) => listManager.updateVisibleRange(scrollTop),
      getVisibleItems: () => listManager.getVisibleItems(),
      getTotalHeight: () => listManager.getTotalHeight(),
      getOffsetY: () => listManager.getOffsetY(),
      getStats: () => listManager.getStats()
    };
  }, [listVirtualization, memoryManager]);

  /**
   * Force garbage collection (if available) with throttling
   */
  const forceGarbageCollection = useCallback(() => {
    const now = Date.now();
    const timeSinceLastGC = now - memoryStatsRef.current.lastGCTime;
    
    // Throttle GC calls to prevent performance issues
    if (timeSinceLastGC > 5000) { // 5 seconds minimum between GC calls
      if ('gc' in window) {
        (window as Window & { gc?: () => void }).gc?.();
        memoryStatsRef.current.lastGCTime = now;
      }
    }
  }, []);

  /**
   * Memory usage monitoring with statistics
   */
  const getMemoryStats = useCallback(() => {
    const memoryInfo = memoryManager.getStats();
    const animationStats = animationManager.getStats();
    const leakReport = enableLeakDetection ? leakDetector.getLeakReport() : null;
    
    // Get browser memory info if available
    let browserMemory = null;
    if ('memory' in performance) {
      browserMemory = (performance as Performance & { memory: PerformanceMemory }).memory;
      
      // Update memory statistics
      const currentUsage = browserMemory.usedJSHeapSize;
      memoryStatsRef.current.memoryReadings.push(currentUsage);
      
      // Keep only last 10 readings for average calculation
      if (memoryStatsRef.current.memoryReadings.length > 10) {
        memoryStatsRef.current.memoryReadings.shift();
      }
      
      // Update peak usage
      if (currentUsage > memoryStatsRef.current.peakMemoryUsage) {
        memoryStatsRef.current.peakMemoryUsage = currentUsage;
      }
      
      // Calculate average
      memoryStatsRef.current.averageMemoryUsage = 
        memoryStatsRef.current.memoryReadings.reduce((a, b) => a + b, 0) / 
        memoryStatsRef.current.memoryReadings.length;
    }

    return {
      memoryManager: memoryInfo,
      animationManager: animationStats,
      leakDetection: leakReport,
      browserMemory,
      statistics: {
        peakMemoryUsage: memoryStatsRef.current.peakMemoryUsage,
        averageMemoryUsage: memoryStatsRef.current.averageMemoryUsage,
        readingCount: memoryStatsRef.current.memoryReadings.length
      }
    };
  }, [memoryManager, animationManager, leakDetector, enableLeakDetection]);

  /**
   * Comprehensive cleanup function
   */
  const performComprehensiveCleanup = useCallback(() => {
    // Cancel all animation frames
    animationManager.cancelAllFrames();
    
    // Force garbage collection if available
    forceGarbageCollection();
    
    // Clear object pools
    // Note: Object pool cleanup is handled by the memory manager
    
    // Log memory leaks in development
    if (enableLeakDetection) {
      leakDetector.logLeaks();
    }
  }, [animationManager, forceGarbageCollection, defaultObjectPool, enableLeakDetection, leakDetector]);

  // Automatic memory monitoring
  useEffect(() => {
    if (!enableMemoryMonitoring) return;

    const monitoringInterval = addManagedTimer(() => {
      const stats = getMemoryStats();
      
      // Log memory warnings if usage is high
      if (stats.browserMemory && stats.browserMemory.usedJSHeapSize > stats.browserMemory.jsHeapSizeLimit * 0.8) {
        console.warn('High memory usage detected:', stats);
      }
    }, 10000, 'interval'); // Check every 10 seconds

    return monitoringInterval.cleanup;
  }, [enableMemoryMonitoring, addManagedTimer, getMemoryStats]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      performComprehensiveCleanup();
    };
  }, [performComprehensiveCleanup]);

  // Return comprehensive memory management API
  return useMemo(() => ({
    // Core memory management
    addCleanup: memoryManager.addCleanup,
    
    // Enhanced event listeners
    addEventListener: addManagedEventListener,
    
    // Enhanced timers
    addTimer: addManagedTimer,
    setTimeout: (callback: () => void, delay: number) => addManagedTimer(callback, delay, 'timeout'),
    setInterval: (callback: () => void, delay: number) => addManagedTimer(callback, delay, 'interval'),
    
    // Enhanced animation frames
    requestAnimationFrame: requestManagedAnimationFrame,
    cancelAnimationFrame: cancelManagedAnimationFrame,
    cancelAllAnimationFrames: animationManager.cancelAllFrames,
    
    // List management
    createManagedList,
    
    // Object pooling
    objectPool: defaultObjectPool,
    
    // Memory monitoring
    getMemoryStats,
    forceGarbageCollection,
    
    // Comprehensive cleanup
    cleanup: performComprehensiveCleanup,
    
    // Utility functions
    isDestroyed: memoryManager.isDestroyed
  }), [
    memoryManager,
    addManagedEventListener,
    addManagedTimer,
    requestManagedAnimationFrame,
    cancelManagedAnimationFrame,
    animationManager.cancelAllFrames,
    createManagedList,
    defaultObjectPool,
    getMemoryStats,
    forceGarbageCollection,
    performComprehensiveCleanup
  ]);
}

/**
 * Hook for components that need basic memory management
 */
export function useBasicMemoryManagement() {
  return useComprehensiveMemoryManagement({
    enableLeakDetection: false,
    enableMemoryMonitoring: false
  });
}

/**
 * Hook for components with animations that need memory leak prevention
 */
export function useAnimationMemoryManagement() {
  return useComprehensiveMemoryManagement({
    enableLeakDetection: true,
    enableMemoryMonitoring: false
  });
}

/**
 * Hook for components with large lists that need efficient data structures
 */
export function useListMemoryManagement<T>(
  items: T[],
  options?: {
    itemHeight?: number;
    containerHeight?: number;
    overscan?: number;
  }
) {
  const memoryManager = useComprehensiveMemoryManagement({
    enableLeakDetection: true,
    listVirtualization: options || undefined
  });

  const managedList = useMemo(() => 
    memoryManager.createManagedList(items, options),
    [memoryManager, items, options]
  );

  return {
    ...memoryManager,
    list: managedList
  };
}

/**
 * Hook for development with full memory leak detection
 */
export function useDevMemoryManagement() {
  return useComprehensiveMemoryManagement({
    enableLeakDetection: true,
    enableMemoryMonitoring: true
  });
}