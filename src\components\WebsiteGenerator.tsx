import React, { useState } from 'react';
import { ArrowLeft, Send, Sparkles, Globe, Smartphone, Code, Palette, Database, Zap, Settings, Download, Eye, Share2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import AnimatedBackground from './AnimatedBackground';

interface GeneratedWebsite {
  name: string;
  description: string;
  pages: string[];
  features: string[];
  tech: string[];
  design: string;
  category: string;
  estimatedTime: string;
}

const WebsiteGenerator: React.FC = () => {
  const [idea, setIdea] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedWebsite, setGeneratedWebsite] = useState<GeneratedWebsite | null>(null);

  const generateWebsite = async () => {
    if (!idea.trim()) return;
    
    setIsGenerating(true);
    
    // Simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const websiteNames = [
      `${idea.split(' ')[0]}Site`,
      `Modern${idea.split(' ')[0]}`,
      `${idea.split(' ')[0]}Hub`,
      `Next${idea.split(' ')[0]}`,
      `${idea.split(' ')[0]}Pro`,
      `Smart${idea.split(' ')[0]}`
    ];
    
    const techStacks = [
      ['React', 'Next.js', 'Tailwind CSS', 'TypeScript', 'Vercel'],
      ['Vue.js', 'Nuxt', 'SCSS', 'JavaScript', 'Netlify'],
      ['Svelte', 'SvelteKit', 'CSS3', 'TypeScript', 'Vercel'],
      ['React', 'Gatsby', 'Styled Components', 'GraphQL', 'AWS'],
      ['Angular', 'TypeScript', 'Material UI', 'RxJS', 'Firebase']
    ];
    
    const pageStructures = [
      ['Home', 'About', 'Services', 'Portfolio', 'Contact'],
      ['Landing', 'Features', 'Pricing', 'Testimonials', 'FAQ'],
      ['Home', 'Products', 'Blog', 'Support', 'Login'],
      ['Dashboard', 'Analytics', 'Settings', 'Profile', 'Help'],
      ['Home', 'Gallery', 'Events', 'News', 'Contact']
    ];
    
    const featureSets = [
      ['Responsive Design', 'SEO Optimized', 'Fast Loading', 'Mobile First', 'Accessibility'],
      ['Dark Mode', 'Animations', 'Contact Forms', 'Social Integration', 'Analytics'],
      ['E-commerce Ready', 'Payment Integration', 'User Authentication', 'Admin Panel', 'API Integration'],
      ['Progressive Web App', 'Offline Support', 'Push Notifications', 'Service Workers', 'App Shell'],
      ['Multi-language', 'CMS Integration', 'Blog System', 'Search Functionality', 'Newsletter']
    ];
    
    const designStyles = ['Modern Minimalist', 'Bold & Colorful', 'Professional Corporate', 'Creative Portfolio', 'Tech Startup'];
    const categories = ['Business', 'Portfolio', 'E-commerce', 'Blog', 'Landing Page', 'Dashboard'];
    
    const randomName = websiteNames[Math.floor(Math.random() * websiteNames.length)];
    const randomTech = techStacks[Math.floor(Math.random() * techStacks.length)];
    const randomPages = pageStructures[Math.floor(Math.random() * pageStructures.length)];
    const randomFeatures = featureSets[Math.floor(Math.random() * featureSets.length)];
    const randomDesign = designStyles[Math.floor(Math.random() * designStyles.length)];
    const randomCategory = categories[Math.floor(Math.random() * categories.length)];
    
    setGeneratedWebsite({
      name: randomName,
      description: `A stunning ${randomCategory.toLowerCase()} website that ${idea.toLowerCase()}. Built with modern web technologies and optimized for performance, SEO, and user experience.`,
      pages: randomPages,
      features: randomFeatures,
      tech: randomTech,
      design: randomDesign,
      category: randomCategory,
      estimatedTime: '1-2 weeks'
    });
    
    setIsGenerating(false);
  };

  const reset = () => {
    setIdea('');
    setGeneratedWebsite(null);
  };

  return (
    <div className="min-h-screen relative">
      <AnimatedBackground />
      
      <div className="relative z-10 max-w-6xl mx-auto px-6 py-12">
        {/* Header with Back Button */}
        <div className="flex items-center gap-4 mb-8">
          <Link 
            to="/"
            className="group flex items-center gap-2 px-4 py-2 bg-white/[0.08] backdrop-blur-xl border border-white/20 rounded-xl hover:bg-white/[0.15] hover:border-white/30 transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <ArrowLeft className="w-4 h-4 text-gray-300 group-hover:text-white group-hover:-translate-x-1 transition-all duration-200" />
            <span className="text-gray-300 text-sm group-hover:text-white transition-colors duration-200">Back to Apps</span>
          </Link>
        </div>

        {/* Header Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6 tracking-tight">
            Generate stunning websites
          </h1>
          <p className="text-gray-400 text-xl max-w-2xl mx-auto leading-relaxed">
            Create beautiful, responsive websites with AI. From landing pages to full applications.
          </p>
        </div>

        {/* Main Input Section */}
        <div className="mb-16">
          <div className="relative max-w-4xl mx-auto">
            <div className="relative backdrop-blur-2xl bg-white/[0.08] rounded-3xl border border-white/20 p-8 shadow-[0_8px_32px_0_rgba(31,38,135,0.37)] hover:bg-white/[0.12] hover:border-white/30 transition-all duration-500">
              <div className="relative">
                <textarea
                  value={idea}
                  onChange={(e) => setIdea(e.target.value)}
                  placeholder="Describe the website you want to create..."
                  className="w-full h-32 bg-transparent border-none outline-none text-white placeholder-gray-400 text-lg resize-none selection:bg-blue-500/30"
                  disabled={isGenerating}
                />
                
                <div className="flex items-center justify-between mt-6 pt-6 border-t border-white/20">
                  <div className="flex items-center gap-2 px-4 py-2 bg-white/[0.08] rounded-xl border border-white/20 backdrop-blur-xl shadow-lg">
                    <Globe className="w-4 h-4 text-blue-400" />
                    <span className="text-gray-300 text-sm font-medium">Website Mode</span>
                  </div>
                  
                  <button
                    onClick={generateWebsite}
                    disabled={!idea.trim() || isGenerating}
                    className="group relative px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-blue-500/25 backdrop-blur-xl border border-blue-500/30 rounded-xl active:scale-95 disabled:active:scale-100"
                  >
                    {isGenerating ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        <span className="text-white font-medium">Generating...</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Sparkles className="w-4 h-4 text-white group-hover:rotate-12 transition-transform duration-200" />
                        <span className="text-white font-medium">Generate Website</span>
                      </div>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Generated Website Display */}
        {generatedWebsite && (
          <div className="mb-16 animate-slideUp">
            <div className="relative backdrop-blur-2xl bg-white/[0.08] rounded-3xl border border-white/20 p-10 shadow-[0_8px_32px_0_rgba(31,38,135,0.37)]">
              <div className="flex items-start justify-between mb-8">
                <div>
                  <h2 className="text-4xl font-bold text-white mb-4">{generatedWebsite.name}</h2>
                  <div className="flex items-center gap-3 mb-4">
                    <span className="px-4 py-2 bg-blue-500/20 text-blue-300 rounded-xl text-sm border border-blue-500/30">
                      {generatedWebsite.category}
                    </span>
                    <span className="px-4 py-2 bg-purple-500/20 text-purple-300 rounded-xl text-sm border border-purple-500/30">
                      {generatedWebsite.design}
                    </span>
                  </div>
                  <p className="text-gray-300 text-lg leading-relaxed max-w-3xl">
                    {generatedWebsite.description}
                  </p>
                </div>
                <button
                  onClick={reset}
                  className="px-6 py-3 border border-white/20 text-white rounded-xl hover:bg-white/[0.12] hover:border-white/30 transition-all duration-300 backdrop-blur-xl shadow-lg"
                >
                  New Website
                </button>
              </div>

              <div className="grid lg:grid-cols-3 gap-8 mb-8">
                {/* Pages */}
                <div className="bg-white/[0.05] backdrop-blur-xl border border-white/15 rounded-2xl p-6">
                  <h3 className="text-white text-lg font-semibold mb-4 flex items-center gap-3">
                    <Globe className="w-5 h-5 text-blue-400" />
                    Pages
                  </h3>
                  <div className="space-y-2">
                    {generatedWebsite.pages.map((page, index) => (
                      <div key={index} className="flex items-center gap-3 text-gray-300">
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        <span>{page}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Features */}
                <div className="bg-white/[0.05] backdrop-blur-xl border border-white/15 rounded-2xl p-6">
                  <h3 className="text-white text-lg font-semibold mb-4 flex items-center gap-3">
                    <Zap className="w-5 h-5 text-green-400" />
                    Features
                  </h3>
                  <div className="space-y-2">
                    {generatedWebsite.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-3 text-gray-300">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Tech Stack */}
                <div className="bg-white/[0.05] backdrop-blur-xl border border-white/15 rounded-2xl p-6">
                  <h3 className="text-white text-lg font-semibold mb-4 flex items-center gap-3">
                    <Code className="w-5 h-5 text-purple-400" />
                    Tech Stack
                  </h3>
                  <div className="space-y-2">
                    {generatedWebsite.tech.map((tech, index) => (
                      <div key={index} className="px-3 py-1 bg-white/[0.08] border border-white/20 rounded-lg text-gray-300 text-sm">
                        {tech}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4 pt-6 border-t border-white/10">
                <button className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-4 px-8 rounded-2xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl">
                  <Code className="w-5 h-5" />
                  Start Building
                </button>
                <button className="px-8 py-4 border border-white/20 text-white rounded-2xl hover:bg-white/[0.12] hover:border-white/30 transition-all duration-300 backdrop-blur-xl shadow-lg">
                  <Eye className="w-5 h-5" />
                </button>
                <button className="px-8 py-4 border border-white/20 text-white rounded-2xl hover:bg-white/[0.12] hover:border-white/30 transition-all duration-300 backdrop-blur-xl shadow-lg">
                  <Download className="w-5 h-5" />
                </button>
                <button className="px-8 py-4 border border-white/20 text-white rounded-2xl hover:bg-white/[0.12] hover:border-white/30 transition-all duration-300 backdrop-blur-xl shadow-lg">
                  <Share2 className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Quick Website Types */}
        {!generatedWebsite && (
          <div className="text-center">
            <h3 className="text-white text-xl font-semibold mb-8">Popular Website Types</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
              {[
                { icon: Globe, label: 'Landing Page', desc: 'Convert visitors into customers' },
                { icon: Smartphone, label: 'Portfolio', desc: 'Showcase your work' },
                { icon: Database, label: 'E-commerce', desc: 'Sell products online' },
                { icon: Palette, label: 'Blog', desc: 'Share your thoughts' }
              ].map((type, index) => (
                <button
                  key={index}
                  onClick={() => setIdea(`Create a ${type.label.toLowerCase()} website`)}
                  className="group p-6 bg-white/[0.05] backdrop-blur-xl border border-white/15 rounded-2xl hover:bg-white/[0.12] hover:border-white/30 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105"
                >
                  <type.icon className="w-8 h-8 text-blue-400 mx-auto mb-3 group-hover:scale-110 transition-transform duration-200" />
                  <h4 className="text-white font-medium mb-1">{type.label}</h4>
                  <p className="text-gray-400 text-sm">{type.desc}</p>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
      
      <style jsx>{`
        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(2rem);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-slideUp {
          animation: slideUp 0.8s ease-out;
        }
      `}</style>
    </div>
  );
};

export default WebsiteGenerator;