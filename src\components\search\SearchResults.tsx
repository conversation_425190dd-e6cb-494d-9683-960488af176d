// Search results component with highlighting and ranking

import React from 'react';
import { motion } from 'framer-motion';
import { SearchResult } from '../../utils/fuzzySearch';
import { Project } from '../../types/project';
import { Star, Clock, Tag, TrendingUp } from 'lucide-react';

interface SearchResultsProps {
  results: SearchResult<Project>[];
  query: string;
  onProjectSelect: (project: Project) => void;
  highlightText: (text: string, indices?: number[]) => string;
  className?: string;
}

const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  query,
  onProjectSelect,
  highlightText,
  className = ""
}) => {
  // Get status color
  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'active':
        return 'text-green-400 bg-green-400/10';
      case 'completed':
        return 'text-blue-400 bg-blue-400/10';
      case 'draft':
        return 'text-yellow-400 bg-yellow-400/10';
      case 'archived':
        return 'text-gray-400 bg-gray-400/10';
      case 'error':
        return 'text-red-400 bg-red-400/10';
      default:
        return 'text-gray-400 bg-gray-400/10';
    }
  };

  // Get priority color
  const getPriorityColor = (priority: Project['priority']) => {
    switch (priority) {
      case 'high':
        return 'text-red-400';
      case 'medium':
        return 'text-yellow-400';
      case 'low':
        return 'text-green-400';
      default:
        return 'text-gray-400';
    }
  };

  // Format last modified date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 60) {
      return `${diffMinutes}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Get match info for highlighting
  const getMatchInfo = (result: SearchResult<Project>, field: keyof Project) => {
    const match = result.match.matches.find(m => m.field === field);
    return match ? match.indices : [];
  };

  if (results.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="text-gray-500">
          <TrendingUp className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium mb-2">No projects found</p>
          <p className="text-sm">
            {query ? `No results for "${query}"` : 'Start typing to search projects'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Results count */}
      <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
        <span>
          {results.length} result{results.length !== 1 ? 's' : ''} 
          {query && ` for "${query}"`}
        </span>
        <span className="text-xs">
          Sorted by relevance
        </span>
      </div>

      {/* Results list */}
      <div className="space-y-2">
        {results.map((result, index) => {
          const { item: project, match } = result;
          const nameIndices = getMatchInfo(result, 'name');
          const descriptionIndices = getMatchInfo(result, 'description');
          
          return (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05, duration: 0.3 }}
              onClick={() => onProjectSelect(project)}
              className="group p-4 bg-gray-800/30 hover:bg-gray-800/50 border border-gray-700/30 hover:border-gray-600/50 rounded-xl cursor-pointer transition-all duration-200"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 
                      className="font-semibold text-white group-hover:text-blue-300 transition-colors truncate"
                      dangerouslySetInnerHTML={{
                        __html: highlightText(project.name, nameIndices)
                      }}
                    />
                    {project.isStarred && (
                      <Star className="w-4 h-4 text-yellow-400 fill-current flex-shrink-0" />
                    )}
                  </div>
                  <p 
                    className="text-sm text-gray-400 line-clamp-2"
                    dangerouslySetInnerHTML={{
                      __html: highlightText(project.description, descriptionIndices)
                    }}
                  />
                </div>
                
                {/* Match score indicator */}
                <div className="ml-4 flex-shrink-0">
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <div 
                      className="w-2 h-2 rounded-full"
                      style={{
                        backgroundColor: `hsl(${match.score * 120}, 70%, 50%)`,
                        opacity: 0.7
                      }}
                    />
                    <span>{Math.round(match.score * 100)}%</span>
                  </div>
                </div>
              </div>

              {/* Metadata */}
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-3">
                  {/* Status */}
                  <span className={`px-2 py-1 rounded-full font-medium ${getStatusColor(project.status)}`}>
                    {project.status}
                  </span>
                  
                  {/* Priority */}
                  <span className={`font-medium ${getPriorityColor(project.priority)}`}>
                    {project.priority} priority
                  </span>
                  
                  {/* Progress */}
                  <span className="text-gray-400">
                    {project.progress}% complete
                  </span>
                </div>
                
                <div className="flex items-center gap-3 text-gray-500">
                  {/* Last modified */}
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>{formatDate(project.lastModified)}</span>
                  </div>
                </div>
              </div>

              {/* Tags */}
              {project.tags.length > 0 && (
                <div className="flex items-center gap-2 mt-3 pt-3 border-t border-gray-700/30">
                  <Tag className="w-3 h-3 text-gray-500" />
                  <div className="flex flex-wrap gap-1">
                    {project.tags.slice(0, 3).map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-2 py-0.5 bg-gray-700/50 text-gray-300 rounded text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                    {project.tags.length > 3 && (
                      <span className="px-2 py-0.5 bg-gray-700/30 text-gray-400 rounded text-xs">
                        +{project.tags.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Match details (for debugging - can be removed) */}
              {process.env.NODE_ENV === 'development' && match.matches.length > 0 && (
                <div className="mt-2 pt-2 border-t border-gray-700/20">
                  <div className="text-xs text-gray-500">
                    Matches: {match.matches.map(m => m.field).join(', ')}
                  </div>
                </div>
              )}
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default SearchResults;