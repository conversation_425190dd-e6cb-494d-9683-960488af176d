import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useMemoryManager, useSafeAsync } from './useMemoryManager';

/**
 * Custom hook that debounces a value
 * Useful for search inputs and API calls
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Update debounced value after delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Cancel the timeout if value changes (also on delay change or unmount)
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Custom hook that debounces a callback function
 * Returns a memoized debounced function with proper cleanup
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T & { cancel: () => void; flush: () => void } {
  const { createDebounced } = useMemoryManager();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useMemo(() => {
    return createDebounced(callback, delay);
  }, [createDebounced, callback, delay, ...deps]);
}

/**
 * Advanced debounce hook with immediate execution option
 */
export function useAdvancedDebounce<T>(
  value: T,
  delay: number,
  options: {
    leading?: boolean; // Execute immediately on first call
    trailing?: boolean; // Execute after delay (default behavior)
    maxWait?: number; // Maximum time to wait before forcing execution
  } = {}
): T {
  const { leading = false, trailing = true, maxWait } = options;
  
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const maxTimeoutRef = useRef<NodeJS.Timeout>();
  const lastCallTimeRef = useRef<number>(0);
  const lastInvokeTimeRef = useRef<number>(0);

  useEffect(() => {
    const currentTime = Date.now();
    const timeSinceLastCall = currentTime - lastCallTimeRef.current;
    const timeSinceLastInvoke = currentTime - lastInvokeTimeRef.current;

    lastCallTimeRef.current = currentTime;

    const shouldInvokeLeading = leading && timeSinceLastCall >= delay;
    const shouldInvokeMaxWait = maxWait && timeSinceLastInvoke >= maxWait;

    // Clear existing timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (maxTimeoutRef.current) {
      clearTimeout(maxTimeoutRef.current);
    }

    // Immediate execution for leading edge
    if (shouldInvokeLeading || shouldInvokeMaxWait) {
      setDebouncedValue(value);
      lastInvokeTimeRef.current = currentTime;
      return;
    }

    // Set up trailing execution
    if (trailing) {
      timeoutRef.current = setTimeout(() => {
        setDebouncedValue(value);
        lastInvokeTimeRef.current = Date.now();
      }, delay);
    }

    // Set up max wait timeout
    if (maxWait && !shouldInvokeMaxWait) {
      const remainingMaxWait = maxWait - timeSinceLastInvoke;
      maxTimeoutRef.current = setTimeout(() => {
        setDebouncedValue(value);
        lastInvokeTimeRef.current = Date.now();
      }, remainingMaxWait);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (maxTimeoutRef.current) {
        clearTimeout(maxTimeoutRef.current);
      }
    };
  }, [value, delay, leading, trailing, maxWait]);

  return debouncedValue;
}

/**
 * Hook for debounced search with loading state and proper cleanup
 */
export function useDebouncedSearch<T>(
  searchFunction: (query: string) => Promise<T[]>,
  delay: number = 300
) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { safeAsync, isMounted } = useSafeAsync();

  const debouncedQuery = useDebounce(query, delay);

  useEffect(() => {
    if (!debouncedQuery.trim()) {
      setResults([]);
      setIsLoading(false);
      setError(null);
      return;
    }

    const performSearch = async () => {
      if (!isMounted()) return;
      
      setIsLoading(true);
      setError(null);

      await safeAsync(
        () => searchFunction(debouncedQuery),
        (searchResults) => {
          setResults(searchResults);
          setIsLoading(false);
        },
        (err) => {
          setError(err.message || 'Search failed');
          setResults([]);
          setIsLoading(false);
        }
      );
    };

    performSearch();
  }, [debouncedQuery, searchFunction, safeAsync, isMounted]);

  const clearResults = useCallback(() => setResults([]), []);
  const clearError = useCallback(() => setError(null), []);

  return {
    query,
    setQuery,
    results,
    isLoading,
    error,
    clearResults,
    clearError
  };
}