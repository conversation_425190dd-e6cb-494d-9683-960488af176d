import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { WifiOff, Wifi, RefreshCw } from 'lucide-react';
import { useOffline, useServiceWorker } from '../../hooks/useOffline';
import { mobileAnimations } from '../../utils/mobileAnimations';

export const OfflineIndicator: React.FC = () => {
  const { isOnline, offlineActions } = useOffline();
  const { updateAvailable, updateServiceWorker, unregisterServiceWorker, registerServiceWorker, isRegistered } = useServiceWorker();

  return (
    <>
      {/* Service Worker Debug Info (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-4 right-4 z-50 bg-gray-800 text-white p-2 rounded text-xs">
          <div>Online: {isOnline ? '✅' : '❌'}</div>
          <div>SW Registered: {isRegistered ? '✅' : '❌'}</div>
          <div className="flex gap-1 mt-1">
            <button
              onClick={registerServiceWorker}
              className="bg-blue-600 px-2 py-1 rounded text-xs"
            >
              Enable SW
            </button>
            <button
              onClick={unregisterServiceWorker}
              className="bg-red-600 px-2 py-1 rounded text-xs"
            >
              Disable SW
            </button>
          </div>
        </div>
      )}

      {/* Offline Status Indicator */}
      <AnimatePresence>
        {!isOnline && (
          <motion.div
            className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50"
            variants={mobileAnimations.slideUp}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <div className="bg-orange-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2">
              <WifiOff size={16} />
              <span className="text-sm font-medium">You're offline</span>
              {offlineActions.length > 0 && (
                <span className="bg-orange-600 text-xs px-2 py-1 rounded-full">
                  {offlineActions.length} pending
                </span>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Back Online Indicator */}
      <AnimatePresence>
        {isOnline && offlineActions.length > 0 && (
          <motion.div
            className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50"
            variants={mobileAnimations.slideUp}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <div className="bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2">
              <Wifi size={16} />
              <span className="text-sm font-medium">Back online - syncing...</span>
              <RefreshCw size={14} className="animate-spin" />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Update Available Indicator */}
      <AnimatePresence>
        {updateAvailable && (
          <motion.div
            className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50"
            variants={mobileAnimations.slideUp}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <div className="bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-3">
              <RefreshCw size={16} />
              <span className="text-sm font-medium">Update available</span>
              <button
                onClick={updateServiceWorker}
                className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs font-medium transition-colors"
              >
                Update
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};