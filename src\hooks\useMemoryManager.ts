import { useEffect, useRef, useCallback, useMemo, useState } from 'react';
import { 
  ComponentMemoryManager, 
  CleanupFunction,
  TimerHandle,
  EventListenerHandle,
  createDebouncedFunction,
  createThrottledFunction
} from '../utils/memoryManagement';

/**
 * Hook for managing component memory and preventing leaks
 */
export function useMemoryManager() {
  const memoryManagerRef = useRef<ComponentMemoryManager>();

  // Initialize memory manager
  if (!memoryManagerRef.current) {
    memoryManagerRef.current = new ComponentMemoryManager();
  }

  const memoryManager = memoryManagerRef.current;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      memoryManager.cleanup();
    };
  }, [memoryManager]);

  // Memoized methods to prevent recreation
  const addCleanup = useCallback((cleanup: CleanupFunction) => {
    memoryManager.addCleanup(cleanup);
  }, [memoryManager]);

  const addTimer = useCallback((
    callback: () => void,
    delay: number,
    type: 'timeout' | 'interval' = 'timeout'
  ): TimerHandle => {
    return memoryManager.addTimer(callback, delay, type);
  }, [memoryManager]);

  const addAnimationFrame = useCallback((callback: () => void): number => {
    return memoryManager.addAnimationFrame(callback);
  }, [memoryManager]);

  const addEventListener = useCallback((
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ): EventListenerHandle => {
    return memoryManager.addEventListener(element, event, handler, options);
  }, [memoryManager]);

  const addObserver = useCallback(<T extends { disconnect: () => void }>(
    observer: T,
    customCleanup?: () => void
  ): T => {
    return memoryManager.addObserver(observer, customCleanup);
  }, [memoryManager]);

  const createDebounced = useCallback(<T extends (...args: unknown[]) => unknown>(
    func: T,
    delay: number
  ) => {
    return createDebouncedFunction(func, delay, memoryManager);
  }, [memoryManager]);

  const createThrottled = useCallback(<T extends (...args: unknown[]) => unknown>(
    func: T,
    delay: number
  ) => {
    return createThrottledFunction(func, delay, memoryManager);
  }, [memoryManager]);

  const getStats = useCallback(() => {
    return memoryManager.getStats();
  }, [memoryManager]);

  return useMemo(() => ({
    addCleanup,
    addTimer,
    addAnimationFrame,
    addEventListener,
    addObserver,
    createDebounced,
    createThrottled,
    getStats,
    isDestroyed: memoryManager.isDestroyed
  }), [
    addCleanup,
    addTimer,
    addAnimationFrame,
    addEventListener,
    addObserver,
    createDebounced,
    createThrottled,
    getStats,
    memoryManager.isDestroyed
  ]);
}

/**
 * Hook for safe async operations with cleanup
 */
export function useSafeAsync() {
  const { addCleanup } = useMemoryManager();
  const isMountedRef = useRef(true);

  useEffect(() => {
    isMountedRef.current = true;
    addCleanup(() => {
      isMountedRef.current = false;
    });
  }, [addCleanup]);

  const safeAsync = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    onSuccess?: (result: T) => void,
    onError?: (error: Error) => void
  ) => {
    try {
      const result = await asyncFn();
      if (isMountedRef.current && onSuccess) {
        onSuccess(result);
      }
      return result;
    } catch (error) {
      if (isMountedRef.current && onError) {
        onError(error as Error);
      }
      throw error;
    }
  }, []);

  const isMounted = useCallback(() => isMountedRef.current, []);

  return { safeAsync, isMounted };
}

/**
 * Hook for managing intervals with automatic cleanup
 */
export function useSafeInterval(
  callback: () => void,
  delay: number | null,
  immediate = false
) {
  const { addTimer } = useMemoryManager();
  const savedCallback = useRef(callback);

  // Remember the latest callback
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay === null) return;

    const tick = () => savedCallback.current();

    if (immediate) {
      tick();
    }

    const timer = addTimer(tick, delay, 'interval');
    
    return timer.cleanup;
  }, [delay, immediate, addTimer]);
}

/**
 * Hook for managing timeouts with automatic cleanup
 */
export function useSafeTimeout(
  callback: () => void,
  delay: number | null
) {
  const { addTimer } = useMemoryManager();
  const savedCallback = useRef(callback);

  // Remember the latest callback
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay === null) return;

    const timer = addTimer(() => savedCallback.current(), delay, 'timeout');
    
    return timer.cleanup;
  }, [delay, addTimer]);
}

/**
 * Hook for managing event listeners with automatic cleanup
 */
export function useSafeEventListener<K extends keyof WindowEventMap>(
  eventName: K,
  handler: (event: WindowEventMap[K]) => void,
  element?: EventTarget | null,
  options?: AddEventListenerOptions
): void;
export function useSafeEventListener(
  eventName: string,
  handler: EventListener,
  element?: EventTarget | null,
  options?: AddEventListenerOptions
): void;
export function useSafeEventListener(
  eventName: string,
  handler: EventListener,
  element: EventTarget | null = window,
  options?: AddEventListenerOptions
) {
  const { addEventListener } = useMemoryManager();
  const savedHandler = useRef(handler);

  // Remember the latest handler
  useEffect(() => {
    savedHandler.current = handler;
  }, [handler]);

  useEffect(() => {
    if (!element) return;

    const eventListener = addEventListener(
      element,
      eventName,
      (event) => savedHandler.current(event),
      options
    );

    return eventListener.cleanup;
  }, [eventName, element, options, addEventListener]);
}

/**
 * Hook for managing animation frames with automatic cleanup
 */
export function useSafeAnimationFrame(
  callback: () => void,
  deps: React.DependencyList = []
) {
  const { addAnimationFrame } = useMemoryManager();
  const savedCallback = useRef(callback);

  // Remember the latest callback
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    const frame = addAnimationFrame(() => savedCallback.current());
    
    // Return cleanup function (though it's already handled by memory manager)
    return () => cancelAnimationFrame(frame);
  }, [addAnimationFrame, ...deps]); // Remove callback from deps since we use ref
}

/**
 * Hook for managing observers with automatic cleanup
 */
export function useSafeObserver<T extends { disconnect: () => void }>(
  createObserver: () => T,
  deps: React.DependencyList = []
) {
  const { addObserver } = useMemoryManager();
  const observerRef = useRef<T | null>(null);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const memoizedCreateObserver = useCallback(createObserver, deps);

  useEffect(() => {
    const observer = memoizedCreateObserver();
    observerRef.current = observer;
    
    addObserver(observer);

    return () => {
      observer.disconnect();
      observerRef.current = null;
    };
  }, [addObserver, memoizedCreateObserver]);

  return observerRef.current;
}

/**
 * Hook for creating debounced functions with cleanup
 */
export function useDebouncedCallback<T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T & { cancel: () => void; flush: () => void } {
  const { createDebounced } = useMemoryManager();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useMemo(() => {
    return createDebounced(callback, delay);
  }, [createDebounced, callback, delay, ...deps]);
}

/**
 * Hook for creating throttled functions with cleanup
 */
export function useThrottledCallback<T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T & { cancel: () => void } {
  const { createThrottled } = useMemoryManager();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useMemo(() => {
    return createThrottled(callback, delay);
  }, [createThrottled, callback, delay, ...deps]);
}

// Define PerformanceMemory interface for better type safety
interface PerformanceMemory {
  readonly usedJSHeapSize: number;
  readonly totalJSHeapSize: number;
  readonly jsHeapSizeLimit: number;
}

/**
 * Hook for memory usage monitoring
 */
export function useMemoryMonitor(enabled = false) {
  const [memoryInfo, setMemoryInfo] = useState<PerformanceMemory | null>(null);
  const { addTimer } = useMemoryManager();

  useEffect(() => {
    if (!enabled) return;

    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        setMemoryInfo((performance as Performance & { memory: PerformanceMemory }).memory);
      }
    };

    // Update immediately
    updateMemoryInfo();

    // Update every 5 seconds
    const timer = addTimer(updateMemoryInfo, 5000, 'interval');

    return timer.cleanup;
  }, [enabled, addTimer]);

  return memoryInfo;
}

/**
 * Hook for managing large lists with memory optimization
 */
export function useMemoryEfficientList<T>(
  items: T[],
  options: {
    itemHeight?: number;
    containerHeight?: number;
    overscan?: number;
  } = {}
) {
  const { addCleanup } = useMemoryManager();
  const listManagerRef = useRef<import('../utils/memoryManagement').MemoryEfficientList<T>>();

  // Initialize list manager
  if (!listManagerRef.current) {
    import('../utils/memoryManagement').then(({ MemoryEfficientList }) => {
      if (!listManagerRef.current) {
        listManagerRef.current = new MemoryEfficientList(items, options);
      }
    });
    return {
      updateVisibleRange: () => {},
      getVisibleItems: () => [],
      getTotalHeight: () => 0,
      getOffsetY: () => 0,
      getStats: () => ({ totalItems: 0, visibleItems: 0, visibleRange: { start: 0, end: 0 }, poolStats: { available: 0, inUse: 0, total: 0 } })
    };
  }

  const listManager = listManagerRef.current;

  // Update items when they change
  useEffect(() => {
    listManager.setItems(items);
  }, [items, listManager]);

  // Cleanup on unmount
  useEffect(() => {
    addCleanup(() => {
      listManager.cleanup();
    });
  }, [addCleanup, listManager]);

  const updateVisibleRange = useCallback((scrollTop: number) => {
    listManager.updateVisibleRange(scrollTop);
  }, [listManager]);

  const getVisibleItems = useCallback(() => {
    return listManager.getVisibleItems();
  }, [listManager]);

  const getTotalHeight = useCallback(() => {
    return listManager.getTotalHeight();
  }, [listManager]);

  const getOffsetY = useCallback(() => {
    return listManager.getOffsetY();
  }, [listManager]);

  const getStats = useCallback(() => {
    return listManager.getStats();
  }, [listManager]);

  return {
    updateVisibleRange,
    getVisibleItems,
    getTotalHeight,
    getOffsetY,
    getStats
  };
}

/**
 * Hook for managing animation frames with memory leak prevention
 */
export function useAnimationFrameManager() {
  const { addCleanup } = useMemoryManager();
  const managerRef = useRef<import('../utils/memoryManagement').AnimationFrameManager>();

  // Initialize animation frame manager
  if (!managerRef.current) {
    import('../utils/memoryManagement').then(({ AnimationFrameManager }) => {
      if (!managerRef.current) {
        managerRef.current = new AnimationFrameManager();
      }
    });
    return {
      requestFrame: () => -1,
      cancelFrame: () => {},
      cancelAllFrames: () => {},
      getStats: () => ({ activeFrames: 0, isDestroyed: false })
    };
  }

  const manager = managerRef.current;

  // Cleanup on unmount
  useEffect(() => {
    addCleanup(() => {
      manager.destroy();
    });
  }, [addCleanup, manager]);

  const requestFrame = useCallback((callback: () => void) => {
    return manager.requestFrame(callback);
  }, [manager]);

  const cancelFrame = useCallback((frameId: number) => {
    manager.cancelFrame(frameId);
  }, [manager]);

  const cancelAllFrames = useCallback(() => {
    manager.cancelAllFrames();
  }, [manager]);

  const getStats = useCallback(() => {
    return manager.getStats();
  }, [manager]);

  return {
    requestFrame,
    cancelFrame,
    cancelAllFrames,
    getStats
  };
}

/**
 * Hook for object pooling to reduce garbage collection
 */
export function useObjectPool<T>(
  factory: () => T,
  reset: (obj: T) => void = () => {},
  maxSize = 100
) {
  const { addCleanup } = useMemoryManager();
  const poolRef = useRef<import('../utils/memoryManagement').ObjectPool<T>>();

  // Initialize object pool
  if (!poolRef.current) {
    import('../utils/memoryManagement').then(({ ObjectPool }) => {
      if (!poolRef.current) {
        poolRef.current = new ObjectPool(factory, reset, maxSize);
      }
    });
    return {
      acquire: factory,
      release: () => {},
      getStats: () => ({ available: 0, inUse: 0, total: 0 })
    };
  }

  const pool = poolRef.current;

  // Cleanup on unmount
  useEffect(() => {
    addCleanup(() => {
      pool.clear();
    });
  }, [addCleanup, pool]);

  const acquire = useCallback(() => {
    return pool.acquire();
  }, [pool]);

  const release = useCallback((obj: T) => {
    pool.release(obj);
  }, [pool]);

  const getStats = useCallback(() => {
    return pool.getStats();
  }, [pool]);

  return {
    acquire,
    release,
    getStats
  };
}

/**
 * Hook for detecting memory leaks in development
 */
export function useMemoryLeakDetector() {
  const detectorRef = useRef<import('../utils/memoryManagement').MemoryLeakDetector>();

  // Initialize detector
  if (!detectorRef.current) {
    import('../utils/memoryManagement').then(({ MemoryLeakDetector }) => {
      if (!detectorRef.current) {
        detectorRef.current = MemoryLeakDetector.getInstance();
      }
    });
    return {
      getLeakReport: () => null,
      logLeaks: () => {}
    };
  }

  const detector = detectorRef.current;

  const getLeakReport = useCallback(() => {
    return detector.getLeakReport();
  }, [detector]);

  const logLeaks = useCallback(() => {
    detector.logLeaks();
  }, [detector]);

  // Log leaks on unmount in development
  useEffect(() => {
    return () => {
      if (process.env['NODE_ENV'] === 'development') {
        detector.logLeaks();
      }
    };
  }, [detector]);

  return {
    getLeakReport,
    logLeaks
  };
}