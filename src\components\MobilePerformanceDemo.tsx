import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { LazyImage } from './ui/LazyImage';
import { MobileAnimatedContainer, MobileStaggerContainer } from './ui/MobileAnimatedContainer';
import { useOffline } from '../hooks/useOffline';
import { useTouchOptimization } from '../hooks/useTouchOptimization';
import { mobileAnimations } from '../utils/mobileAnimations';
import { Wifi, WifiOff, Smartphone, Zap, Image as ImageIcon } from 'lucide-react';

const MobilePerformanceDemo: React.FC = () => {
  const { isOnline, offlineActions } = useOffline();
  const { createTouchHandler } = useTouchOptimization();
  const [demoImages, setDemoImages] = useState<string[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0
  });

  useEffect(() => {
    // Generate demo images for lazy loading
    const images = Array.from({ length: 10 }, (_, i) => 
      `https://picsum.photos/300/200?random=${i}`
    );
    setDemoImages(images);

    // Simulate performance metrics
    const startTime = performance.now();
    setTimeout(() => {
      setPerformanceMetrics({
        loadTime: Math.round(performance.now() - startTime),
        renderTime: Math.round(Math.random() * 50 + 10),
        memoryUsage: Math.round(Math.random() * 20 + 5)
      });
    }, 100);
  }, []);

  const handleTouchDemo = createTouchHandler((event, element) => {
    element.style.transform = 'scale(0.95)';
    setTimeout(() => {
      element.style.transform = 'scale(1)';
    }, 150);
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <MobileAnimatedContainer animation="fadeIn" className="max-w-4xl mx-auto">
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center gap-3">
            <Smartphone className="text-blue-600" />
            Mobile Performance Demo
          </h1>
          <p className="text-gray-600 mb-6">
            Showcasing mobile-optimized features: lazy loading, touch optimization, 
            offline functionality, and performance enhancements.
          </p>

          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <MobileAnimatedContainer animation="slideUp" delay={0.1}>
              <div className="bg-green-50 p-4 rounded-xl border border-green-200">
                <div className="flex items-center gap-2 mb-2">
                  <Zap className="text-green-600" size={20} />
                  <h3 className="font-semibold text-green-800">Load Time</h3>
                </div>
                <p className="text-2xl font-bold text-green-600">
                  {performanceMetrics.loadTime}ms
                </p>
              </div>
            </MobileAnimatedContainer>

            <MobileAnimatedContainer animation="slideUp" delay={0.2}>
              <div className="bg-blue-50 p-4 rounded-xl border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <ImageIcon className="text-blue-600" size={20} />
                  <h3 className="font-semibold text-blue-800">Render Time</h3>
                </div>
                <p className="text-2xl font-bold text-blue-600">
                  {performanceMetrics.renderTime}ms
                </p>
              </div>
            </MobileAnimatedContainer>

            <MobileAnimatedContainer animation="slideUp" delay={0.3}>
              <div className="bg-purple-50 p-4 rounded-xl border border-purple-200">
                <div className="flex items-center gap-2 mb-2">
                  {isOnline ? (
                    <Wifi className="text-purple-600" size={20} />
                  ) : (
                    <WifiOff className="text-purple-600" size={20} />
                  )}
                  <h3 className="font-semibold text-purple-800">Status</h3>
                </div>
                <p className="text-lg font-bold text-purple-600">
                  {isOnline ? 'Online' : 'Offline'}
                  {offlineActions.length > 0 && (
                    <span className="text-sm ml-2">
                      ({offlineActions.length} pending)
                    </span>
                  )}
                </p>
              </div>
            </MobileAnimatedContainer>
          </div>

          {/* Touch Optimization Demo */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Touch Optimization</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[1, 2, 3, 4].map((i) => (
                <motion.button
                  key={i}
                  ref={handleTouchDemo}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-xl font-medium shadow-lg"
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.1 }}
                >
                  Touch Me {i}
                </motion.button>
              ))}
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Buttons have optimized touch feedback with passive event listeners
            </p>
          </div>

          {/* Lazy Loading Demo */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Lazy Loading Images</h2>
            <MobileStaggerContainer className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {demoImages.map((src, index) => (
                <div key={index} className="aspect-square">
                  <LazyImage
                    src={src}
                    alt={`Demo image ${index + 1}`}
                    className="w-full h-full rounded-lg shadow-md"
                    onLoad={() => console.log(`Image ${index + 1} loaded`)}
                  />
                </div>
              ))}
            </MobileStaggerContainer>
            <p className="text-sm text-gray-600 mt-2">
              Images load only when they enter the viewport with intersection observer
            </p>
          </div>

          {/* Animation Demo */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Mobile-Optimized Animations</h2>
            <div className="space-y-4">
              <MobileAnimatedContainer animation="fadeIn">
                <div className="bg-gradient-to-r from-pink-500 to-rose-500 text-white p-4 rounded-xl">
                  Fade In Animation (Optimized for mobile)
                </div>
              </MobileAnimatedContainer>
              
              <MobileAnimatedContainer animation="slideUp" delay={0.2}>
                <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white p-4 rounded-xl">
                  Slide Up Animation (Reduced duration on mobile)
                </div>
              </MobileAnimatedContainer>
              
              <MobileAnimatedContainer animation="mobileModal" delay={0.4}>
                <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white p-4 rounded-xl">
                  Modal Animation (Mobile-specific easing)
                </div>
              </MobileAnimatedContainer>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Animations respect user's reduced motion preferences and are optimized for mobile performance
            </p>
          </div>

          {/* Offline Features */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Offline Functionality</h2>
            <div className="bg-gray-50 p-4 rounded-xl">
              <div className="flex items-center gap-2 mb-2">
                {isOnline ? (
                  <Wifi className="text-green-600" size={20} />
                ) : (
                  <WifiOff className="text-orange-600" size={20} />
                )}
                <span className="font-medium">
                  Connection Status: {isOnline ? 'Online' : 'Offline'}
                </span>
              </div>
              
              {offlineActions.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-gray-600">
                    Pending offline actions: {offlineActions.length}
                  </p>
                </div>
              )}
              
              <div className="mt-4 text-sm text-gray-600">
                <h4 className="font-medium mb-2">Offline Features:</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li>Service worker caching for offline access</li>
                  <li>Background sync for pending actions</li>
                  <li>IndexedDB for local data storage</li>
                  <li>Offline fallback pages</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </MobileAnimatedContainer>
    </div>
  );
};

export default MobilePerformanceDemo;