import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Smartphone, 
  Globe, 
  FileText,
  MoreHorizontal 
} from 'lucide-react';
import { useResponsiveLayout } from '../../hooks/useResponsiveLayout';

interface NavigationItem {
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  shortLabel?: string;
}

const navigationItems: NavigationItem[] = [
  { path: '/', icon: Home, label: 'Home', shortLabel: 'Home' },
  { path: '/build', icon: Smartphone, label: 'Mobile Apps', shortLabel: 'Mobile' },
  { path: '/generate', icon: Globe, label: 'Websites', shortLabel: 'Web' },
  { path: '/form-demo', icon: FileText, label: 'Forms', shortLabel: 'Forms' },
];

interface ResponsiveNavigationProps {
  isCollapsed?: boolean;
  orientation?: 'vertical' | 'horizontal';
  className?: string;
}

const ResponsiveNavigation: React.FC<ResponsiveNavigationProps> = ({
  isCollapsed = false,
  orientation = 'vertical',
  className = '',
}) => {
  const location = useLocation();
  const { isMobile, isTablet } = useResponsiveLayout();

  const isActive = (path: string) => location.pathname === path;

  const getLabel = (item: NavigationItem) => {
    if (isCollapsed) return '';
    if (isMobile && item.shortLabel) return item.shortLabel;
    return item.label;
  };

  const getItemClasses = (path: string) => {
    const baseClasses = `
      relative flex items-center gap-3 rounded-xl transition-all duration-300 
      hover:scale-105 group touch-target
      ${orientation === 'horizontal' ? 'flex-col gap-1 p-2' : 'p-3'}
      ${isCollapsed ? 'justify-center' : ''}
    `;

    if (isActive(path)) {
      return `${baseClasses} bg-gradient-to-br from-blue-500/20 to-purple-500/20 text-white shadow-lg border border-blue-500/30`;
    }

    return `${baseClasses} text-gray-400 hover:bg-gray-700/50 hover:text-white`;
  };

  const getIconSize = () => {
    if (orientation === 'horizontal') return 'w-5 h-5';
    if (isMobile) return 'w-4 h-4';
    return 'w-5 h-5';
  };

  const getTextSize = () => {
    if (orientation === 'horizontal') return 'text-xs';
    if (isMobile) return 'text-xs';
    return 'text-sm';
  };

  return (
    <nav className={`
      ${orientation === 'vertical' ? 'flex flex-col space-y-2' : 'flex space-x-2'}
      ${className}
    `}>
      {navigationItems.map((item) => {
        const Icon = item.icon;
        const label = getLabel(item);
        
        return (
          <Link
            key={item.path}
            to={item.path}
            className={getItemClasses(item.path)}
            aria-label={item.label}
            title={isCollapsed ? item.label : undefined}
          >
            <Icon className={`${getIconSize()} flex-shrink-0`} />
            {label && (
              <span className={`font-medium ${getTextSize()}`}>
                {label}
              </span>
            )}
            {isActive(item.path) && orientation === 'vertical' && !isCollapsed && (
              <div className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-blue-500 rounded-full"></div>
            )}
            {isActive(item.path) && orientation === 'horizontal' && (
              <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-1 bg-blue-500 rounded-full"></div>
            )}
          </Link>
        );
      })}
      
      {/* More menu for mobile horizontal navigation */}
      {orientation === 'horizontal' && isMobile && (
        <button
          className="relative flex flex-col items-center gap-1 p-2 text-gray-400 hover:bg-gray-700/50 hover:text-white rounded-xl transition-all duration-300 touch-target"
          aria-label="More options"
        >
          <MoreHorizontal className="w-5 h-5" />
          <span className="text-xs font-medium">More</span>
        </button>
      )}
    </nav>
  );
};

export default ResponsiveNavigation;