import React from 'react';
import { cn } from '../../utils/helpers';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full' | 'tight' | 'wide';
  className?: string;
}

const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  size = 'lg',
  className = '',
}) => {
  
  const getContainerClass = () => {
    switch (size) {
      case 'tight': return 'container-tight';
      case 'wide': return 'container-wide';
      case 'full': return 'container-full';
      case 'sm':
      case 'md':
      case 'lg':
      case 'xl':
      default: return 'container-responsive';
    }
  };

  return (
    <div className={cn(getContainerClass(), className)}>
      {children}
    </div>
  );
};

export default ResponsiveContainer;