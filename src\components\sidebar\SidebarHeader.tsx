import React from 'react';
import { <PERSON>, X, Menu } from 'lucide-react';
import { useApp } from '../../context/AppContext';

interface SidebarHeaderProps {
  isOpen: boolean;
  notifications: number;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({ isOpen, notifications }) => {
  const { toggleSidebar } = useApp();

  return (
    <header className="flex items-center justify-between p-4 border-b border-gray-800/50 min-h-[64px] bg-gray-800/30">
      <div className="flex items-center gap-3">
        <div className="relative">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <div className="w-4 h-4 bg-white rounded-md"></div>
          </div>
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-400 rounded-full border-2 border-gray-900 animate-pulse" aria-label="Online status"></div>
        </div>
        {isOpen && (
          <div>
            <span className="text-white font-semibold text-lg">Rork</span>
            <div className="flex items-center gap-1 mt-0.5">
              <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full animate-pulse"></div>
              <span className="text-emerald-400 text-xs font-medium">Online</span>
            </div>
          </div>
        )}
      </div>
      
      {/* Header Actions */}
      <div className="flex items-center gap-2">
        {isOpen && (
          <>
            <button 
              className="relative p-2 hover:bg-gray-700/50 rounded-lg transition-all duration-200 group"
              aria-label={`${notifications} notifications`}
            >
              <Bell className="w-4 h-4 text-gray-400 group-hover:text-white group-hover:scale-110 transition-all" />
              {notifications > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
                  {notifications}
                </span>
              )}
            </button>
            <button 
              onClick={toggleSidebar}
              className="p-2 hover:bg-gray-700/50 rounded-lg transition-all duration-200 lg:hidden group"
              aria-label="Close sidebar"
            >
              <X className="w-4 h-4 text-gray-400 group-hover:text-white group-hover:rotate-90 transition-all" />
            </button>
          </>
        )}
        {!isOpen && (
          <button 
            onClick={toggleSidebar}
            className="p-2 hover:bg-gray-700/50 rounded-lg transition-all duration-200 hidden lg:block group"
            aria-label="Open sidebar"
          >
            <Menu className="w-4 h-4 text-gray-400 group-hover:text-white group-hover:scale-110 transition-all" />
          </button>
        )}
      </div>
    </header>
  );
};

export default SidebarHeader;