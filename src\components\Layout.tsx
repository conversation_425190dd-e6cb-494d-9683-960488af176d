import React, { useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import { useLocation } from 'react-router-dom';
import { useApp } from '../context/AppContext';
import { useResponsiveLayout } from '../hooks/useResponsiveLayout';
import { useSwipeGestures } from '../hooks/useSwipeGestures';
import Sidebar from './Sidebar';
import Loading from './ui/Loading';
import PullToRefresh from './ui/PullToRefresh';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const { sidebarOpen, toggleSidebar, setSidebarOpen, isLoading, refreshProjects } = useApp();
  const {
    isMobile,
    isTablet,
    isDesktop,
    showMobileMenu,
    toggleMobileMenu,
    closeMobileMenu,
  } = useResponsiveLayout();

  // Routes that handle their own container styling
  const fullWidthRoutes = ['/', '/mobile-performance'];
  const isFullWidthRoute = fullWidthRoutes.includes(location.pathname);

  // Sync responsive layout state with app context
  useEffect(() => {
    if (isMobile && sidebarOpen) {
      setSidebarOpen(false);
    }
  }, [isMobile, sidebarOpen, setSidebarOpen]);

  // Swipe gestures for mobile navigation
  const { attachToElement } = useSwipeGestures({
    onSwipeRight: isMobile && !showMobileMenu ? toggleMobileMenu : () => {},
    onSwipeLeft: isMobile && showMobileMenu ? closeMobileMenu : () => {},
    threshold: 100,
    enabled: isMobile
  });

  // Handle mobile menu toggle
  const handleMobileMenuToggle = () => {
    if (isMobile) {
      toggleMobileMenu();
    } else {
      toggleSidebar();
    }
  };

  // Handle refresh for pull-to-refresh
  const handleRefresh = async () => {
    if (refreshProjects) {
      await refreshProjects();
    }
  };

  return (
    <div 
      className="min-h-screen bg-gray-950 sidebar-layout"
      ref={attachToElement}
    >
      {/* Mobile/Desktop Menu Button */}
      <button
        onClick={handleMobileMenuToggle}
        className={`
          fixed top-4 z-50 touch-target bg-gray-900/90 backdrop-blur-xl 
          border border-gray-700/50 rounded-xl shadow-2xl 
          hover:bg-gray-800/90 transition-all duration-300 hover:scale-105
          flex items-center justify-center focus-visible-ring
          ${isMobile ? 
            (showMobileMenu ? 'left-[336px]' : 'left-4') : 
            'lg:hidden left-4'
          }
        `}
        aria-label={
          isMobile 
            ? (showMobileMenu ? 'Close menu' : 'Open menu')
            : (sidebarOpen ? 'Close sidebar' : 'Open sidebar')
        }
      >
        {(isMobile ? showMobileMenu : sidebarOpen) ? (
          <X className="w-5 h-5 text-gray-300 hover:text-white transition-colors" />
        ) : (
          <Menu className="w-5 h-5 text-gray-300 hover:text-white transition-colors" />
        )}
      </button>

      {/* Sidebar */}
      <Sidebar 
        isOpen={isMobile ? showMobileMenu : sidebarOpen} 
        onToggle={isMobile ? toggleMobileMenu : toggleSidebar} 
        onClose={() => {
          if (isMobile) {
            closeMobileMenu();
          } else {
            setSidebarOpen(false);
          }
        }}
        isMobile={isMobile}
        isTablet={isTablet}
        isDesktop={isDesktop}
      />

      {/* Main Content Area */}
      <div className={`
        sidebar-content transition-all duration-300 ease-in-out
        ${!isMobile && sidebarOpen ? 
          (isTablet ? 'ml-72' : 'ml-80') : 
          (!isMobile ? 'ml-16' : '')
        }
      `}>
        <main className="min-h-screen safe-area-top">
          <div className={`
            ${isMobile ? 'pt-20' : isTablet ? 'pt-6' : 'pt-8'}
            ${isMobile ? 'pb-4' : 'pb-8'}
          `}>
            {isMobile ? (
              <PullToRefresh 
                onRefresh={handleRefresh}
                enabled={true}
                className="min-h-screen"
              >
                {isFullWidthRoute ? (
                  children
                ) : (
                  <div className="container-responsive">
                    {children}
                  </div>
                )}
              </PullToRefresh>
            ) : (
              isFullWidthRoute ? (
                children
              ) : (
                <div className="container-responsive">
                  {children}
                </div>
              )
            )}
          </div>
        </main>
      </div>

      {/* Global Loading Overlay */}
      {isLoading && (
        <Loading 
          fullScreen 
          text="Generating your app..." 
          size="lg" 
        />
      )}
    </div>
  );
};

export default Layout;