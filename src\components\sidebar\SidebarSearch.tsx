import React from 'react';
import { Filter } from 'lucide-react';
import { SortOption, FilterOption } from '../../types';
import AdvancedSearchInput from '../search/AdvancedSearchInput';
import { useAdvancedSearch } from '../../hooks/useAdvancedSearch';
import { useApp } from '../../context/AppContext';

interface SidebarSearchProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  sortBy: SortOption;
  setSortBy: (sort: SortOption) => void;
  filterBy: FilterOption;
  setFilterBy: (filter: FilterOption) => void;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
  statusCounts: Record<string, number>;
}

const SidebarSearch: React.FC<SidebarSearchProps> = ({
  searchQuery,
  setSearchQuery,
  sortBy,
  setSortBy,
  filterBy,
  setFilterBy,
  showFilters,
  setShowFilters,
  statusCounts
}) => {
  const { projects } = useApp();
  
  const {
    suggestions,
    history,
    showSuggestions,
    setShowSuggestions,
    isSearching,
    clearSearch,
    selectSuggestion,
    removeFromHistory
  } = useAdvancedSearch(projects, {
    debounceMs: 200,
    maxSuggestions: 3,
    maxHistory: 5
  });

  const handleQueryChange = (query: string) => {
    setSearchQuery(query);
  };

  const handleClearSearch = () => {
    clearSearch();
    setSearchQuery('');
  };

  const handleSelectSuggestion = (suggestion: string) => {
    selectSuggestion(suggestion);
    setSearchQuery(suggestion);
  };

  return (
    <div className="p-4 border-b border-gray-800/30 space-y-3">
      <AdvancedSearchInput
        query={searchQuery}
        setQuery={handleQueryChange}
        suggestions={suggestions}
        history={history}
        showSuggestions={showSuggestions}
        setShowSuggestions={setShowSuggestions}
        isSearching={isSearching}
        onClearSearch={handleClearSearch}
        onSelectSuggestion={handleSelectSuggestion}
        onRemoveFromHistory={removeFromHistory}
        placeholder="Search projects... (⌘K)"
      />

      {/* Filter and Sort Controls */}
      <div className="flex items-center gap-2">
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`flex items-center gap-2 px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 ${
            showFilters ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30' : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
          }`}
          aria-expanded={showFilters}
          aria-label="Toggle filters"
        >
          <Filter className="w-3 h-3" />
          Filters
        </button>
        
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as SortOption)}
          className="px-3 py-1.5 bg-gray-700/50 border border-gray-600/50 rounded-lg text-xs text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
          aria-label="Sort projects by"
        >
          <option value="date">Sort by Date</option>
          <option value="name">Sort by Name</option>
          <option value="status">Sort by Status</option>
          <option value="progress">Sort by Progress</option>
        </select>
      </div>

      {/* Filter Options */}
      {showFilters && (
        <div className="grid grid-cols-2 gap-2 pt-2 border-t border-gray-700/30">
          {(['all', 'active', 'completed', 'draft', 'archived'] as FilterOption[]).map((filter) => (
            <button
              key={filter}
              onClick={() => setFilterBy(filter)}
              className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 ${
                filterBy === filter 
                  ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30' 
                  : 'bg-gray-700/30 text-gray-400 hover:bg-gray-600/30 hover:text-gray-300'
              }`}
            >
              {filter.charAt(0).toUpperCase() + filter.slice(1)}
              {filter !== 'all' && statusCounts[filter] && (
                <span className="ml-1 px-1.5 py-0.5 bg-gray-600/50 rounded text-xs">
                  {statusCounts[filter]}
                </span>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default SidebarSearch;