import React, { ReactNode } from 'react';
import { BaseErrorBoundary } from './BaseErrorBoundary';
import { ErrorType } from '../../types/error';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  featureName: string;
  fallbackComponent?: ReactNode;
}

export const FeatureErrorBoundary: React.FC<Props> = ({ 
  children, 
  featureName, 
  fallbackComponent 
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    console.warn(`Feature error in ${featureName}:`, error, errorInfo);
  };

  const defaultFallback = (
    <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div className="flex items-center mb-3">
        <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
        <h3 className="text-lg font-medium text-yellow-800">
          {featureName} Unavailable
        </h3>
      </div>
      <p className="text-yellow-700 mb-4">
        This feature is temporarily unavailable. You can continue using other parts of the app.
      </p>
      <button
        onClick={() => window.location.reload()}
        className="flex items-center px-3 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors text-sm"
      >
        <RefreshCw className="h-4 w-4 mr-2" />
        Retry Feature
      </button>
    </div>
  );

  return (
    <BaseErrorBoundary
      errorType={ErrorType.COMPONENT_ERROR}
      onError={handleError}
      maxRetries={2}
      fallback={fallbackComponent || defaultFallback}
    >
      {children}
    </BaseErrorBoundary>
  );
};