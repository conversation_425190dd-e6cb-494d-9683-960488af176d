import { useState, useEffect, useCallback } from 'react';

interface OfflineAction {
  id: string;
  type: string;
  data: any;
  timestamp: number;
}

export function useOffline() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [offlineActions, setOfflineActions] = useState<OfflineAction[]>([]);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      // Trigger background sync when coming back online
      if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
        navigator.serviceWorker.ready.then((registration) => {
          return registration.sync.register('sync-projects');
        });
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Queue action for offline processing
  const queueOfflineAction = useCallback(async (type: string, data: any) => {
    const action: OfflineAction = {
      id: `${Date.now()}-${Math.random()}`,
      type,
      data,
      timestamp: Date.now()
    };

    try {
      // Store in IndexedDB
      const db = await openOfflineDB();
      const transaction = db.transaction(['offline-actions'], 'readwrite');
      const store = transaction.objectStore('offline-actions');
      await store.add(action);

      setOfflineActions(prev => [...prev, action]);
    } catch (error) {
      console.error('Failed to queue offline action:', error);
    }
  }, []);

  // Get pending offline actions
  const getPendingActions = useCallback(async () => {
    try {
      const db = await openOfflineDB();
      const transaction = db.transaction(['offline-actions'], 'readonly');
      const store = transaction.objectStore('offline-actions');
      const actions = await store.getAll();
      setOfflineActions(actions);
      return actions;
    } catch (error) {
      console.error('Failed to get pending actions:', error);
      return [];
    }
  }, []);

  // Clear processed actions
  const clearProcessedActions = useCallback(async (actionIds: string[]) => {
    try {
      const db = await openOfflineDB();
      const transaction = db.transaction(['offline-actions'], 'readwrite');
      const store = transaction.objectStore('offline-actions');
      
      await Promise.all(actionIds.map(id => store.delete(id)));
      
      setOfflineActions(prev => prev.filter(action => !actionIds.includes(action.id)));
    } catch (error) {
      console.error('Failed to clear processed actions:', error);
    }
  }, []);

  return {
    isOnline,
    offlineActions,
    queueOfflineAction,
    getPendingActions,
    clearProcessedActions
  };
}

// Service Worker registration hook
export function useServiceWorker() {
  const [isRegistered, setIsRegistered] = useState(false);
  const [updateAvailable, setUpdateAvailable] = useState(false);

  useEffect(() => {
    // Disable service worker for now to fix offline issue
    // if ('serviceWorker' in navigator) {
    //   registerServiceWorker();
    // }
  }, []);

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      
      setIsRegistered(true);
      
      // Check for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              setUpdateAvailable(true);
            }
          });
        }
      });

      console.log('Service Worker registered successfully');
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  };

  const updateServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration) {
        registration.update();
        window.location.reload();
      }
    }
  };

  const unregisterServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration) {
        await registration.unregister();
        console.log('Service Worker unregistered');
        window.location.reload();
      }
    }
  };

  return {
    isRegistered,
    updateAvailable,
    updateServiceWorker,
    unregisterServiceWorker,
    registerServiceWorker
  };
}

// Utility function to open IndexedDB
function openOfflineDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('AppGeneratorDB', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      
      if (!db.objectStoreNames.contains('offline-actions')) {
        const store = db.createObjectStore('offline-actions', { keyPath: 'id' });
        store.createIndex('timestamp', 'timestamp');
      }
    };
  });
}