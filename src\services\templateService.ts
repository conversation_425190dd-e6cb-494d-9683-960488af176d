// Template service for app generation
import { ProjectType, Complexity, Feature, TechStack } from '../types/project';
import { AppGenerationRequest } from '../types/api';

export interface AppTemplate {
  id: string;
  name: string;
  description: string;
  type: ProjectType;
  complexity: Complexity;
  features: Feature[];
  techStack: TechStack;
  estimatedHours: number;
  tags: string[];
  preview?: string;
  customizable: boolean;
  popularity: number;
}

export interface TemplateCategory {
  name: string;
  description: string;
  templates: AppTemplate[];
  icon: string;
}

export class TemplateService {
  private static templates: AppTemplate[] = [
    // Mobile Templates
    {
      id: 'mobile-social-simple',
      name: 'Social Media App',
      description: 'A simple social media app with user profiles, posts, and basic interactions',
      type: 'mobile',
      complexity: 'simple',
      features: [
        {
          id: 'user-auth',
          name: 'User Authentication',
          description: 'Secure user registration and login',
          complexity: 'simple',
          estimatedHours: 16
        },
        {
          id: 'user-profiles',
          name: 'User Profiles',
          description: 'Customizable user profiles with photos',
          complexity: 'simple',
          estimatedHours: 12
        },
        {
          id: 'post-creation',
          name: 'Post Creation',
          description: 'Create and share posts with images',
          complexity: 'medium',
          estimatedHours: 20
        },
        {
          id: 'social-feed',
          name: 'Social Feed',
          description: 'Timeline of posts from followed users',
          complexity: 'medium',
          estimatedHours: 24
        }
      ],
      techStack: {
        frontend: ['React Native', 'TypeScript', 'Expo'],
        backend: ['Firebase', 'Node.js'],
        database: ['Firebase Firestore'],
        deployment: ['App Store', 'Google Play'],
        tools: ['Expo CLI', 'Firebase Console']
      },
      estimatedHours: 120,
      tags: ['social', 'mobile', 'firebase', 'react-native'],
      customizable: true,
      popularity: 8
    },
    {
      id: 'mobile-ecommerce-medium',
      name: 'E-commerce Mobile App',
      description: 'Full-featured mobile shopping app with payments and order management',
      type: 'mobile',
      complexity: 'medium',
      features: [
        {
          id: 'product-catalog',
          name: 'Product Catalog',
          description: 'Browse and search products with filters',
          complexity: 'medium',
          estimatedHours: 32
        },
        {
          id: 'shopping-cart',
          name: 'Shopping Cart',
          description: 'Add items to cart and manage quantities',
          complexity: 'simple',
          estimatedHours: 16
        },
        {
          id: 'payment-processing',
          name: 'Payment Processing',
          description: 'Secure payment integration with Stripe',
          complexity: 'complex',
          estimatedHours: 40
        },
        {
          id: 'order-tracking',
          name: 'Order Tracking',
          description: 'Track order status and delivery',
          complexity: 'medium',
          estimatedHours: 28
        }
      ],
      techStack: {
        frontend: ['React Native', 'TypeScript', 'Redux Toolkit'],
        backend: ['Node.js', 'Express', 'Stripe API'],
        database: ['MongoDB', 'Redis'],
        deployment: ['AWS', 'App Store', 'Google Play'],
        tools: ['Stripe Dashboard', 'MongoDB Atlas']
      },
      estimatedHours: 300,
      tags: ['ecommerce', 'mobile', 'payments', 'stripe'],
      customizable: true,
      popularity: 9
    },

    // Web Templates
    {
      id: 'web-portfolio-simple',
      name: 'Portfolio Website',
      description: 'Professional portfolio website with project showcase',
      type: 'web',
      complexity: 'simple',
      features: [
        {
          id: 'responsive-design',
          name: 'Responsive Design',
          description: 'Mobile-first responsive layout',
          complexity: 'simple',
          estimatedHours: 16
        },
        {
          id: 'project-showcase',
          name: 'Project Showcase',
          description: 'Gallery of projects with details',
          complexity: 'simple',
          estimatedHours: 12
        },
        {
          id: 'contact-form',
          name: 'Contact Form',
          description: 'Contact form with email integration',
          complexity: 'simple',
          estimatedHours: 8
        },
        {
          id: 'seo-optimization',
          name: 'SEO Optimization',
          description: 'Search engine optimization setup',
          complexity: 'simple',
          estimatedHours: 6
        }
      ],
      techStack: {
        frontend: ['React', 'TypeScript', 'Tailwind CSS'],
        backend: ['Next.js', 'API Routes'],
        database: ['SQLite', 'Prisma'],
        deployment: ['Vercel', 'Netlify'],
        tools: ['ESLint', 'Prettier']
      },
      estimatedHours: 80,
      tags: ['portfolio', 'web', 'nextjs', 'tailwind'],
      customizable: true,
      popularity: 7
    },
    {
      id: 'web-saas-complex',
      name: 'SaaS Platform',
      description: 'Complete SaaS platform with user management, billing, and analytics',
      type: 'web',
      complexity: 'complex',
      features: [
        {
          id: 'user-management',
          name: 'User Management',
          description: 'Complete user lifecycle management',
          complexity: 'complex',
          estimatedHours: 60
        },
        {
          id: 'subscription-billing',
          name: 'Subscription Billing',
          description: 'Recurring billing with Stripe',
          complexity: 'complex',
          estimatedHours: 80
        },
        {
          id: 'analytics-dashboard',
          name: 'Analytics Dashboard',
          description: 'Real-time analytics and reporting',
          complexity: 'complex',
          estimatedHours: 100
        },
        {
          id: 'api-management',
          name: 'API Management',
          description: 'RESTful API with rate limiting',
          complexity: 'complex',
          estimatedHours: 70
        }
      ],
      techStack: {
        frontend: ['React', 'TypeScript', 'Next.js'],
        backend: ['Node.js', 'GraphQL', 'Microservices'],
        database: ['PostgreSQL', 'Redis', 'MongoDB'],
        deployment: ['AWS', 'Kubernetes', 'CloudFront'],
        tools: ['Docker', 'Terraform', 'Monitoring']
      },
      estimatedHours: 500,
      tags: ['saas', 'web', 'subscription', 'analytics'],
      customizable: true,
      popularity: 9
    },

    // AI Templates
    {
      id: 'ai-chatbot-medium',
      name: 'AI Chatbot Platform',
      description: 'Intelligent chatbot with natural language processing',
      type: 'ai',
      complexity: 'medium',
      features: [
        {
          id: 'nlp-processing',
          name: 'NLP Processing',
          description: 'Natural language understanding',
          complexity: 'complex',
          estimatedHours: 80
        },
        {
          id: 'conversation-flow',
          name: 'Conversation Flow',
          description: 'Manage conversation states and context',
          complexity: 'medium',
          estimatedHours: 60
        },
        {
          id: 'training-interface',
          name: 'Training Interface',
          description: 'Interface to train and improve the bot',
          complexity: 'medium',
          estimatedHours: 50
        },
        {
          id: 'analytics-reporting',
          name: 'Analytics & Reporting',
          description: 'Conversation analytics and insights',
          complexity: 'medium',
          estimatedHours: 40
        }
      ],
      techStack: {
        frontend: ['React', 'TypeScript', 'D3.js'],
        backend: ['Python', 'FastAPI', 'OpenAI API'],
        database: ['PostgreSQL', 'Vector DB'],
        deployment: ['AWS', 'Docker'],
        tools: ['Jupyter', 'Pandas', 'TensorFlow']
      },
      estimatedHours: 400,
      tags: ['ai', 'chatbot', 'nlp', 'machine-learning'],
      customizable: true,
      popularity: 8
    },

    // Form Templates
    {
      id: 'form-survey-simple',
      name: 'Survey Builder',
      description: 'Simple survey creation and response collection tool',
      type: 'form',
      complexity: 'simple',
      features: [
        {
          id: 'form-builder',
          name: 'Form Builder',
          description: 'Drag-and-drop form creation',
          complexity: 'medium',
          estimatedHours: 40
        },
        {
          id: 'response-collection',
          name: 'Response Collection',
          description: 'Collect and store form responses',
          complexity: 'simple',
          estimatedHours: 20
        },
        {
          id: 'basic-analytics',
          name: 'Basic Analytics',
          description: 'Simple response analytics and charts',
          complexity: 'simple',
          estimatedHours: 24
        },
        {
          id: 'export-data',
          name: 'Export Data',
          description: 'Export responses to CSV/Excel',
          complexity: 'simple',
          estimatedHours: 16
        }
      ],
      techStack: {
        frontend: ['React', 'TypeScript', 'React Hook Form'],
        backend: ['Next.js', 'API Routes'],
        database: ['SQLite', 'Prisma'],
        deployment: ['Vercel', 'Netlify'],
        tools: ['Chart.js', 'CSV Export']
      },
      estimatedHours: 100,
      tags: ['forms', 'survey', 'data-collection', 'analytics'],
      customizable: true,
      popularity: 6
    }
  ];

  static getAllTemplates(): AppTemplate[] {
    return this.templates;
  }

  static getTemplatesByType(type: ProjectType): AppTemplate[] {
    return this.templates.filter(template => template.type === type);
  }

  static getTemplatesByComplexity(complexity: Complexity): AppTemplate[] {
    return this.templates.filter(template => template.complexity === complexity);
  }

  static getTemplateById(id: string): AppTemplate | undefined {
    return this.templates.find(template => template.id === id);
  }

  static getPopularTemplates(limit: number = 5): AppTemplate[] {
    return this.templates
      .sort((a, b) => b.popularity - a.popularity)
      .slice(0, limit);
  }

  static searchTemplates(query: string): AppTemplate[] {
    const searchTerm = query.toLowerCase();
    return this.templates.filter(template =>
      template.name.toLowerCase().includes(searchTerm) ||
      template.description.toLowerCase().includes(searchTerm) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  }

  static getTemplateCategories(): TemplateCategory[] {
    const categories: TemplateCategory[] = [
      {
        name: 'Mobile Apps',
        description: 'Native and cross-platform mobile applications',
        icon: '📱',
        templates: this.getTemplatesByType('mobile')
      },
      {
        name: 'Web Applications',
        description: 'Modern web applications and websites',
        icon: '🌐',
        templates: this.getTemplatesByType('web')
      },
      {
        name: 'AI & Machine Learning',
        description: 'AI-powered applications and ML solutions',
        icon: '🤖',
        templates: this.getTemplatesByType('ai')
      },
      {
        name: 'E-commerce',
        description: 'Online stores and marketplace solutions',
        icon: '🛒',
        templates: this.getTemplatesByType('ecommerce')
      },
      {
        name: 'Forms & Surveys',
        description: 'Data collection and form management tools',
        icon: '📋',
        templates: this.getTemplatesByType('form')
      }
    ];

    return categories.filter(category => category.templates.length > 0);
  }

  static customizeTemplate(templateId: string, customizations: Partial<AppGenerationRequest>): AppTemplate | null {
    const template = this.getTemplateById(templateId);
    if (!template || !template.customizable) {
      return null;
    }

    const customizedTemplate: AppTemplate = {
      ...template,
      id: `${template.id}-custom-${Date.now()}`,
      name: `Custom ${template.name}`,
      description: customizations.description || template.description
    };

    // Apply customizations
    if (customizations.features) {
      const additionalFeatures = customizations.features.map((featureName, index) => ({
        id: `custom-feature-${index}`,
        name: featureName,
        description: `Custom feature: ${featureName}`,
        complexity: 'medium' as Complexity,
        estimatedHours: 20
      }));
      customizedTemplate.features = [...template.features, ...additionalFeatures];
    }

    if (customizations.complexity && customizations.complexity !== template.complexity) {
      customizedTemplate.complexity = customizations.complexity;
      // Adjust estimated hours based on complexity change
      const multiplier = customizations.complexity === 'complex' ? 1.5 : 
                        customizations.complexity === 'simple' ? 0.7 : 1;
      customizedTemplate.estimatedHours = Math.round(template.estimatedHours * multiplier);
    }

    return customizedTemplate;
  }

  static getRecommendedTemplates(request: AppGenerationRequest): AppTemplate[] {
    const typeTemplates = this.getTemplatesByType(request.type);
    const complexityTemplates = typeTemplates.filter(t => t.complexity === request.complexity);
    
    if (complexityTemplates.length > 0) {
      return complexityTemplates.slice(0, 3);
    }
    
    return typeTemplates.slice(0, 3);
  }

  static addCustomTemplate(template: Omit<AppTemplate, 'id' | 'popularity'>): AppTemplate {
    const newTemplate: AppTemplate = {
      ...template,
      id: `custom-${Date.now()}`,
      popularity: 1
    };
    
    this.templates.push(newTemplate);
    return newTemplate;
  }

  static updateTemplate(id: string, updates: Partial<AppTemplate>): AppTemplate | null {
    const templateIndex = this.templates.findIndex(t => t.id === id);
    if (templateIndex === -1) {
      return null;
    }

    this.templates[templateIndex] = {
      ...this.templates[templateIndex],
      ...updates
    };

    return this.templates[templateIndex];
  }

  static deleteTemplate(id: string): boolean {
    const templateIndex = this.templates.findIndex(t => t.id === id);
    if (templateIndex === -1) {
      return false;
    }

    this.templates.splice(templateIndex, 1);
    return true;
  }

  static getTemplateStats() {
    const stats = {
      total: this.templates.length,
      byType: {} as Record<ProjectType, number>,
      byComplexity: {} as Record<Complexity, number>,
      averageHours: 0,
      mostPopular: this.getPopularTemplates(1)[0]
    };

    // Calculate stats
    this.templates.forEach(template => {
      stats.byType[template.type] = (stats.byType[template.type] || 0) + 1;
      stats.byComplexity[template.complexity] = (stats.byComplexity[template.complexity] || 0) + 1;
    });

    stats.averageHours = Math.round(
      this.templates.reduce((sum, t) => sum + t.estimatedHours, 0) / this.templates.length
    );

    return stats;
  }
}