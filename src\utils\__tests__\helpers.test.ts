import { describe, it, expect } from 'vitest';
import { 
  generateId, 
  truncateText, 
  formatDate, 
  cn,
  getStatusStyles,
  getPriorityStyles 
} from '../helpers';

describe('Helper Functions', () => {
  describe('generateId', () => {
    it('generates a unique ID', () => {
      const id1 = generateId();
      const id2 = generateId();
      
      expect(id1).toBeTruthy();
      expect(id2).toBeTruthy();
      expect(id1).not.toBe(id2);
    });

    it('generates ID with correct format', () => {
      const id = generateId();
      expect(typeof id).toBe('string');
      expect(id.length).toBeGreaterThan(0);
    });
  });

  describe('truncateText', () => {
    it('truncates text longer than max length', () => {
      const text = 'This is a very long text that should be truncated';
      const result = truncateText(text, 20);

      expect(result).toBe('This is a very long ...');
      expect(result.length).toBe(23); // 20 + '...'
    });

    it('returns original text if shorter than max length', () => {
      const text = 'Short text';
      const result = truncateText(text, 20);
      
      expect(result).toBe(text);
    });

    it('handles empty string', () => {
      const result = truncateText('', 10);
      expect(result).toBe('');
    });
  });

  describe('formatDate', () => {
    it('formats ISO date string correctly', () => {
      const date = '2024-01-15T10:30:00.000Z';
      const result = formatDate(date);
      
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
    });

    it('handles invalid date', () => {
      const result = formatDate('invalid-date');
      expect(result).toBe('Invalid Date');
    });
  });

  describe('cn (className utility)', () => {
    it('combines class names correctly', () => {
      const result = cn('class1', 'class2', 'class3');
      expect(result).toContain('class1');
      expect(result).toContain('class2');
      expect(result).toContain('class3');
    });

    it('handles conditional classes', () => {
      const result = cn('base', true && 'conditional', false && 'hidden');
      expect(result).toContain('base');
      expect(result).toContain('conditional');
      expect(result).not.toContain('hidden');
    });

    it('handles undefined and null values', () => {
      const result = cn('base', undefined, null, 'valid');
      expect(result).toContain('base');
      expect(result).toContain('valid');
    });
  });

  describe('getStatusStyles', () => {
    it('returns correct styles for active status', () => {
      const styles = getStatusStyles('active');
      expect(styles).toBeTruthy();
      expect(typeof styles).toBe('object');
      expect(styles).toHaveProperty('dot');
      expect(styles).toHaveProperty('badge');
      expect(styles).toHaveProperty('text');
    });

    it('returns correct styles for completed status', () => {
      const styles = getStatusStyles('completed');
      expect(styles).toBeTruthy();
      expect(typeof styles).toBe('object');
    });

    it('returns correct styles for draft status', () => {
      const styles = getStatusStyles('draft');
      expect(styles).toBeTruthy();
      expect(typeof styles).toBe('object');
    });

    it('returns correct styles for archived status', () => {
      const styles = getStatusStyles('archived');
      expect(styles).toBeTruthy();
      expect(typeof styles).toBe('object');
    });
  });

  describe('getPriorityStyles', () => {
    it('returns correct styles for high priority', () => {
      const styles = getPriorityStyles('high');
      expect(styles).toBeTruthy();
      expect(typeof styles).toBe('string');
      expect(styles).toContain('text-red-400');
    });

    it('returns correct styles for medium priority', () => {
      const styles = getPriorityStyles('medium');
      expect(styles).toBeTruthy();
      expect(typeof styles).toBe('string');
      expect(styles).toContain('text-yellow-400');
    });

    it('returns correct styles for low priority', () => {
      const styles = getPriorityStyles('low');
      expect(styles).toBeTruthy();
      expect(typeof styles).toBe('string');
      expect(styles).toContain('text-green-400');
    });
  });
});
