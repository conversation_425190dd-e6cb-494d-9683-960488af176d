// Comprehensive search and filter results component

import React, { useState } from 'react';
import { Project } from '../../types/project';
import { SearchResult } from '../../utils/fuzzySearch';
import { useAdvancedSearch } from '../../hooks/useAdvancedSearch';
import { useFilters } from '../../hooks/useFilters';
import AdvancedSearchInput from './AdvancedSearchInput';
import FilterPanel from '../filters/FilterPanel';
import ResultManager from '../results/ResultManager';
import NoSearchResults from '../empty-states/NoSearchResults';
import NoProjects from '../empty-states/NoProjects';
import ProjectCard from '../ProjectCard';
import { Filter, Settings } from 'lucide-react';

interface SearchAndFilterResultsProps {
  projects: Project[];
  onProjectSelect: (project: Project) => void;
  onCreateProject: () => void;
  onImportProjects?: () => void;
  onOpenGenerator?: () => void;
  className?: string;
}

const SearchAndFilterResults: React.FC<SearchAndFilterResultsProps> = ({
  projects,
  onProjectSelect,
  onCreateProject,
  onImportProjects,
  onOpenGenerator,
  className = ""
}) => {
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  // Advanced search hook
  const {
    query,
    setQuery,
    results: searchResults,
    isSearching,
    suggestions,
    history,
    showSuggestions,
    setShowSuggestions,
    clearSearch,
    selectSuggestion,
    removeFromHistory
  } = useAdvancedSearch(projects);

  // Filters hook
  const {
    criteria,
    setCriteria,
    sortCriteria,
    setSortCriteria,
    filteredProjects,
    stats,
    resetFilters,
    isFilterEmpty,
    presets,
    savePreset,
    loadPreset,
    deletePreset
  } = useFilters(searchResults.map(r => r.item));

  // Handle filter panel actions
  const handleSavePreset = (name: string, description: string) => {
    savePreset(name, description);
  };

  const handleLoadPreset = (preset: any) => {
    loadPreset(preset);
  };

  const handleDeletePreset = (id: string) => {
    deletePreset(id);
  };

  const handleResetFilters = () => {
    resetFilters();
    setShowFilterPanel(false);
  };

  const handleClearAll = () => {
    clearSearch();
    resetFilters();
  };

  // Get final results (search + filter)
  const finalResults = filteredProjects;

  // Render project item
  const renderProjectItem = (project: Project, index: number) => (
    <ProjectCard
      key={project.id}
      project={project}
      onClick={() => onProjectSelect(project)}
      className="w-full"
    />
  );

  // Render empty state
  const renderEmptyState = () => {
    if (projects.length === 0) {
      return (
        <NoProjects
          onCreateProject={onCreateProject}
          onImportProjects={onImportProjects}
          onOpenGenerator={onOpenGenerator}
        />
      );
    }

    return (
      <NoSearchResults
        query={query}
        hasFilters={!isFilterEmpty}
        onClearSearch={clearSearch}
        onResetFilters={resetFilters}
        onCreateProject={onCreateProject}
        onOpenFilters={() => setShowFilterPanel(true)}
      />
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search and Filter Header */}
      <div className="space-y-4">
        {/* Search Input */}
        <AdvancedSearchInput
          query={query}
          setQuery={setQuery}
          suggestions={suggestions}
          history={history}
          showSuggestions={showSuggestions}
          setShowSuggestions={setShowSuggestions}
          isSearching={isSearching}
          onClearSearch={clearSearch}
          onSelectSuggestion={selectSuggestion}
          onRemoveFromHistory={removeFromHistory}
          placeholder="Search projects by name, description, tags..."
        />

        {/* Filter Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowFilterPanel(true)}
              className={`flex items-center gap-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                !isFilterEmpty
                  ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30'
                  : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 border border-gray-600/50'
              }`}
            >
              <Filter className="w-4 h-4" />
              Filters
              {!isFilterEmpty && (
                <span className="px-2 py-0.5 bg-blue-500 text-white text-xs rounded-full">
                  Active
                </span>
              )}
            </button>

            {(query || !isFilterEmpty) && (
              <button
                onClick={handleClearAll}
                className="px-3 py-2 text-sm text-gray-400 hover:text-white transition-colors"
              >
                Clear all
              </button>
            )}
          </div>

          <div className="flex items-center gap-2 text-sm text-gray-400">
            <span>
              {finalResults.length} of {projects.length} projects
            </span>
          </div>
        </div>

        {/* Active Filters Summary */}
        {!isFilterEmpty && (
          <div className="flex items-center gap-2 p-3 bg-gray-800/30 rounded-xl border border-gray-700/30">
            <Settings className="w-4 h-4 text-gray-400 flex-shrink-0" />
            <div className="flex-1 text-sm text-gray-300">
              <span className="font-medium">Active filters:</span>
              <div className="mt-1 flex flex-wrap gap-2">
                {criteria.status.length > 0 && (
                  <span className="px-2 py-1 bg-gray-700/50 rounded text-xs">
                    Status: {criteria.status.join(', ')}
                  </span>
                )}
                {criteria.type.length > 0 && (
                  <span className="px-2 py-1 bg-gray-700/50 rounded text-xs">
                    Type: {criteria.type.join(', ')}
                  </span>
                )}
                {criteria.priority.length > 0 && (
                  <span className="px-2 py-1 bg-gray-700/50 rounded text-xs">
                    Priority: {criteria.priority.join(', ')}
                  </span>
                )}
                {criteria.tags.length > 0 && (
                  <span className="px-2 py-1 bg-gray-700/50 rounded text-xs">
                    Tags: {criteria.tags.join(', ')}
                  </span>
                )}
                {criteria.isStarred && (
                  <span className="px-2 py-1 bg-gray-700/50 rounded text-xs">
                    Starred only
                  </span>
                )}
              </div>
            </div>
            <button
              onClick={resetFilters}
              className="text-xs text-gray-400 hover:text-white transition-colors"
            >
              Clear
            </button>
          </div>
        )}
      </div>

      {/* Results */}
      <ResultManager
        items={finalResults}
        renderItem={renderProjectItem}
        renderEmpty={renderEmptyState}
        itemsPerPage={20}
        enablePagination={finalResults.length > 20}
        enableVirtualScrolling={finalResults.length > 100}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        itemHeight={120}
        containerHeight={600}
      />

      {/* Filter Panel */}
      {showFilterPanel && (
        <FilterPanel
          isOpen={showFilterPanel}
          onClose={() => setShowFilterPanel(false)}
          criteria={criteria}
          onCriteriaChange={setCriteria}
          sortCriteria={sortCriteria}
          onSortChange={setSortCriteria}
          stats={stats}
          presets={presets}
          onSavePreset={handleSavePreset}
          onLoadPreset={handleLoadPreset}
          onDeletePreset={handleDeletePreset}
          onResetFilters={handleResetFilters}
        />
      )}
    </div>
  );
};

export default SearchAndFilterResults;