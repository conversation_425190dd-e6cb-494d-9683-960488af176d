import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { AppContext, AppActions, User, Project, FilterOptions, GenerationOptions } from '../types';
import { generateId } from '../utils/helpers';

// Initial state
const initialState: AppContext = {
  // User data
  user: {
    id: '1',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    plan: 'pro'
  },
  
  // Project management
  projects: [
    {
      id: '1',
      name: 'AI Personal Assistant',
      description: 'Ask me anything by voice or text. I\'m here to help.',
      type: 'ai',
      status: 'active',
      progress: 85,
      lastModified: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 min ago
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      route: '/',
      priority: 'high',
      isStarred: true,
      tags: ['AI', 'Voice', 'Assistant'],
      complexity: 'complex'
    },
    {
      id: '2',
      name: 'E-commerce Platform',
      description: 'Complete online shopping platform with payment integration.',
      type: 'ecommerce',
      status: 'completed',
      progress: 100,
      lastModified: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // 1 hour ago
      createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
      route: '/generate',
      priority: 'high',
      tags: ['E-commerce', 'Payment', 'Shopping'],
      complexity: 'complex'
    },
    {
      id: '3',
      name: 'Form Validation System',
      description: 'Advanced form validation with real-time feedback.',
      type: 'form',
      status: 'completed',
      progress: 100,
      lastModified: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
      createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(),
      route: '/form-demo',
      priority: 'medium',
      tags: ['Forms', 'Validation', 'UI'],
      complexity: 'medium'
    },
    {
      id: '4',
      name: 'Mobile Banking App',
      description: 'Secure mobile banking application with biometric authentication.',
      type: 'mobile',
      status: 'active',
      progress: 60,
      lastModified: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      route: '/build',
      priority: 'high',
      isStarred: true,
      tags: ['Banking', 'Security', 'Mobile'],
      complexity: 'complex'
    },
    {
      id: '5',
      name: 'Social Media Dashboard',
      description: 'Analytics dashboard for social media management.',
      type: 'web',
      status: 'draft',
      progress: 25,
      lastModified: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      priority: 'low',
      tags: ['Social Media', 'Analytics', 'Dashboard'],
      complexity: 'medium'
    }
  ],
  currentProject: null,
  projectsLoading: false,
  
  // UI state
  sidebarOpen: false,
  activeModal: null,
  theme: 'system',
  
  // Generation state
  isGenerating: false,
  generationProgress: 0,
  generationStatus: '',
  generationError: null,
  generationOptions: {},
  
  // Search and filters
  searchQuery: '',
  activeFilters: {
    status: [],
    type: [],
    priority: [],
    complexity: [],
    tags: []
  },
  searchResults: [],
  searchLoading: false,
  
  // Real-time updates
  lastSyncTime: null,
  pendingChanges: [],
  optimisticUpdates: {},
  
  // Loading and error states
  isLoading: false,
  error: null,
  
  // Data persistence
  storageQuota: 0,
  storageUsed: 0,
  syncStatus: 'idle'
};

// Action types
type ActionType =
  // User actions
  | { type: 'SET_USER'; payload: User | null }
  
  // Project management actions
  | { type: 'ADD_PROJECT'; payload: Omit<Project, 'id' | 'createdAt'> }
  | { type: 'ADD_PROJECT_WITH_ID'; payload: Project }
  | { type: 'UPDATE_PROJECT'; payload: { id: string; updates: Partial<Project> } }
  | { type: 'DELETE_PROJECT'; payload: string }
  | { type: 'CLEAR_PROJECTS' }
  | { type: 'SET_CURRENT_PROJECT'; payload: Project | null }
  | { type: 'SET_PROJECTS_LOADING'; payload: boolean }
  
  // Optimistic updates
  | { type: 'ADD_OPTIMISTIC_UPDATE'; payload: { id: string; updates: Partial<Project> } }
  | { type: 'REMOVE_OPTIMISTIC_UPDATE'; payload: string }
  | { type: 'CLEAR_OPTIMISTIC_UPDATES' }
  
  // UI state actions
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_SIDEBAR_OPEN'; payload: boolean }
  | { type: 'SET_ACTIVE_MODAL'; payload: string | null }
  | { type: 'SET_THEME'; payload: 'light' | 'dark' | 'system' }
  
  // Generation actions
  | { type: 'SET_GENERATING'; payload: boolean }
  | { type: 'SET_GENERATION_PROGRESS'; payload: number }
  | { type: 'SET_GENERATION_STATUS'; payload: string }
  | { type: 'SET_GENERATION_ERROR'; payload: string | null }
  | { type: 'SET_GENERATION_OPTIONS'; payload: GenerationOptions }
  
  // Search and filter actions
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_ACTIVE_FILTERS'; payload: FilterOptions }
  | { type: 'SET_SEARCH_RESULTS'; payload: Project[] }
  | { type: 'SET_SEARCH_LOADING'; payload: boolean }
  
  // Real-time update actions
  | { type: 'SET_LAST_SYNC_TIME'; payload: string }
  | { type: 'ADD_PENDING_CHANGE'; payload: string }
  | { type: 'REMOVE_PENDING_CHANGE'; payload: string }
  | { type: 'CLEAR_PENDING_CHANGES' }
  
  // Storage actions
  | { type: 'SET_STORAGE_QUOTA'; payload: number }
  | { type: 'SET_STORAGE_USED'; payload: number }
  | { type: 'SET_SYNC_STATUS'; payload: 'idle' | 'syncing' | 'error' }
  
  // General loading and error actions
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

// Reducer with optimistic updates support
const appReducer = (state: AppContext, action: ActionType): AppContext => {
  switch (action.type) {
    // User actions
    case 'SET_USER':
      return { ...state, user: action.payload };
    
    // Project management actions
    case 'ADD_PROJECT':
      { const newProject: Project = {
        ...action.payload,
        id: generateId(),
        createdAt: new Date().toISOString(),
        lastModified: new Date().toISOString()
      };
      return { 
        ...state, 
        projects: [newProject, ...state.projects],
        lastSyncTime: new Date().toISOString()
      }; }
    
    case 'ADD_PROJECT_WITH_ID':
      return {
        ...state,
        projects: [action.payload, ...state.projects],
        lastSyncTime: new Date().toISOString()
      };
    
    case 'UPDATE_PROJECT':
      return {
        ...state,
        projects: state.projects.map(project =>
          project.id === action.payload.id
            ? { ...project, ...action.payload.updates, lastModified: new Date().toISOString() }
            : project
        ),
        lastSyncTime: new Date().toISOString()
      };
    
    case 'DELETE_PROJECT':
      return {
        ...state,
        projects: state.projects.filter(project => project.id !== action.payload),
        currentProject: state.currentProject?.id === action.payload ? null : state.currentProject,
        lastSyncTime: new Date().toISOString()
      };
    
    case 'CLEAR_PROJECTS':
      return {
        ...state,
        projects: [],
        currentProject: null,
        lastSyncTime: new Date().toISOString()
      };
    
    case 'SET_CURRENT_PROJECT':
      return { ...state, currentProject: action.payload };
    
    case 'SET_PROJECTS_LOADING':
      return { ...state, projectsLoading: action.payload };
    
    // Optimistic updates
    case 'ADD_OPTIMISTIC_UPDATE':
      return {
        ...state,
        optimisticUpdates: {
          ...state.optimisticUpdates,
          [action.payload.id]: action.payload.updates
        }
      };
    
    case 'REMOVE_OPTIMISTIC_UPDATE':
      { const { [action.payload]: removed, ...remainingUpdates } = state.optimisticUpdates;
      return {
        ...state,
        optimisticUpdates: remainingUpdates
      }; }
    
    case 'CLEAR_OPTIMISTIC_UPDATES':
      return { ...state, optimisticUpdates: {} };
    
    // UI state actions
    case 'TOGGLE_SIDEBAR':
      return { ...state, sidebarOpen: !state.sidebarOpen };
    
    case 'SET_SIDEBAR_OPEN':
      return { ...state, sidebarOpen: action.payload };
    
    case 'SET_ACTIVE_MODAL':
      return { ...state, activeModal: action.payload };
    
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    
    // Generation actions
    case 'SET_GENERATING':
      return { ...state, isGenerating: action.payload };
    
    case 'SET_GENERATION_PROGRESS':
      return { ...state, generationProgress: action.payload };
    
    case 'SET_GENERATION_STATUS':
      return { ...state, generationStatus: action.payload };
    
    case 'SET_GENERATION_ERROR':
      return { ...state, generationError: action.payload };
    
    case 'SET_GENERATION_OPTIONS':
      return { ...state, generationOptions: action.payload };
    
    // Search and filter actions
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };
    
    case 'SET_ACTIVE_FILTERS':
      return { ...state, activeFilters: action.payload };
    
    case 'SET_SEARCH_RESULTS':
      return { ...state, searchResults: action.payload };
    
    case 'SET_SEARCH_LOADING':
      return { ...state, searchLoading: action.payload };
    
    // Real-time update actions
    case 'SET_LAST_SYNC_TIME':
      return { ...state, lastSyncTime: action.payload };
    
    case 'ADD_PENDING_CHANGE':
      return {
        ...state,
        pendingChanges: [...state.pendingChanges, action.payload]
      };
    
    case 'REMOVE_PENDING_CHANGE':
      return {
        ...state,
        pendingChanges: state.pendingChanges.filter(id => id !== action.payload)
      };
    
    case 'CLEAR_PENDING_CHANGES':
      return { ...state, pendingChanges: [] };
    
    // Storage actions
    case 'SET_STORAGE_QUOTA':
      return { ...state, storageQuota: action.payload };
    
    case 'SET_STORAGE_USED':
      return { ...state, storageUsed: action.payload };
    
    case 'SET_SYNC_STATUS':
      return { ...state, syncStatus: action.payload };
    
    // General loading and error actions
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    default:
      return state;
  }
};

// Context
const AppContextProvider = createContext<(AppContext & AppActions) | undefined>(undefined);

// Provider component
export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load state from localStorage on mount with corruption handling
  useEffect(() => {
    const loadSavedState = () => {
      try {
        const savedState = localStorage.getItem('rork-app-state');
        if (savedState) {
          const parsedState = JSON.parse(savedState);
          
          // Validate the structure of saved state
          if (validateSavedState(parsedState)) {
            // Restore projects if they exist and are valid
            if (parsedState.projects && Array.isArray(parsedState.projects)) {
              // Clear initial projects first
              dispatch({ type: 'CLEAR_PROJECTS' });
              
              parsedState.projects.forEach((project: Project) => {
                if (validateProject(project)) {
                  dispatch({ type: 'ADD_PROJECT', payload: project });
                }
              });
            }
            
            // Restore user if valid
            if (parsedState.user && validateUser(parsedState.user)) {
              dispatch({ type: 'SET_USER', payload: parsedState.user });
            }
            
            // Restore other state
            if (parsedState.theme) {
              dispatch({ type: 'SET_THEME', payload: parsedState.theme });
            }
            
            dispatch({ type: 'SET_LAST_SYNC_TIME', payload: new Date().toISOString() });
          } else {
            console.warn('Saved state structure is invalid, using initial state');
            handleCorruptedState();
          }
        }
      } catch (error) {
        console.error('Failed to parse saved state:', error);
        handleCorruptedState();
      }
    };

    loadSavedState();
  }, []);

  // Save state to localStorage when it changes with error handling
  useEffect(() => {
    try {
      const stateToSave = {
        projects: state.projects,
        user: state.user,
        theme: state.theme,
        lastSyncTime: state.lastSyncTime,
        version: 1 // Add version for future migrations
      };
      
      localStorage.setItem('rork-app-state', JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Failed to save state to localStorage:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to save data locally' });
    }
  }, [state.projects, state.user, state.theme, state.lastSyncTime]);

  // Handle corrupted state
  const handleCorruptedState = () => {
    try {
      // Clear corrupted data
      localStorage.removeItem('rork-app-state');
      
      // Set error state
      dispatch({ type: 'SET_ERROR', payload: 'Local data was corrupted and has been reset' });
      
      // Try to recover from backup if available
      const backupState = localStorage.getItem('rork-app-state-backup');
      if (backupState) {
        const parsedBackup = JSON.parse(backupState);
        if (validateSavedState(parsedBackup)) {
          localStorage.setItem('rork-app-state', backupState);
          window.location.reload(); // Reload to apply backup
        }
      }
    } catch (error) {
      console.error('Failed to handle corrupted state:', error);
    }
  };

  // Validate saved state structure
  const validateSavedState = (state: any): boolean => {
    return (
      state &&
      typeof state === 'object' &&
      (state.projects === undefined || Array.isArray(state.projects)) &&
      (state.user === undefined || typeof state.user === 'object')
    );
  };

  // Validate project structure
  const validateProject = (project: any): project is Project => {
    return (
      project &&
      typeof project === 'object' &&
      typeof project.id === 'string' &&
      typeof project.name === 'string' &&
      typeof project.description === 'string' &&
      typeof project.type === 'string' &&
      typeof project.status === 'string' &&
      typeof project.progress === 'number' &&
      typeof project.lastModified === 'string' &&
      typeof project.createdAt === 'string'
    );
  };

  // Validate user structure
  const validateUser = (user: any): user is User => {
    return (
      user &&
      typeof user === 'object' &&
      typeof user.id === 'string' &&
      typeof user.name === 'string' &&
      typeof user.email === 'string'
    );
  };

  // Create backup periodically
  useEffect(() => {
    const createBackup = () => {
      try {
        const currentState = localStorage.getItem('rork-app-state');
        if (currentState) {
          localStorage.setItem('rork-app-state-backup', currentState);
        }
      } catch (error) {
        console.error('Failed to create backup:', error);
      }
    };

    // Create backup every 5 minutes
    const backupInterval = setInterval(createBackup, 5 * 60 * 1000);
    
    return () => clearInterval(backupInterval);
  }, []);

  const actions: AppActions = {
    // User actions
    setUser: (user) => dispatch({ type: 'SET_USER', payload: user }),
    
    // Project management actions
    addProject: (project) => dispatch({ type: 'ADD_PROJECT', payload: project }),
    addProjectWithId: (project: Project) => dispatch({ type: 'ADD_PROJECT_WITH_ID', payload: project }),
    updateProject: (id, updates) => dispatch({ type: 'UPDATE_PROJECT', payload: { id, updates } }),
    deleteProject: (id) => dispatch({ type: 'DELETE_PROJECT', payload: id }),
    setCurrentProject: (project) => dispatch({ type: 'SET_CURRENT_PROJECT', payload: project }),
    setProjectsLoading: (loading) => dispatch({ type: 'SET_PROJECTS_LOADING', payload: loading }),
    
    // Optimistic updates
    addOptimisticUpdate: (id, updates) => dispatch({ type: 'ADD_OPTIMISTIC_UPDATE', payload: { id, updates } }),
    removeOptimisticUpdate: (id) => dispatch({ type: 'REMOVE_OPTIMISTIC_UPDATE', payload: id }),
    clearOptimisticUpdates: () => dispatch({ type: 'CLEAR_OPTIMISTIC_UPDATES' }),
    
    // UI state actions
    toggleSidebar: () => dispatch({ type: 'TOGGLE_SIDEBAR' }),
    setSidebarOpen: (open) => dispatch({ type: 'SET_SIDEBAR_OPEN', payload: open }),
    setActiveModal: (modal) => dispatch({ type: 'SET_ACTIVE_MODAL', payload: modal }),
    setTheme: (theme) => dispatch({ type: 'SET_THEME', payload: theme }),
    
    // Generation actions
    setGenerating: (generating) => dispatch({ type: 'SET_GENERATING', payload: generating }),
    setGenerationProgress: (progress) => dispatch({ type: 'SET_GENERATION_PROGRESS', payload: progress }),
    setGenerationStatus: (status) => dispatch({ type: 'SET_GENERATION_STATUS', payload: status }),
    setGenerationError: (error) => dispatch({ type: 'SET_GENERATION_ERROR', payload: error }),
    setGenerationOptions: (options) => dispatch({ type: 'SET_GENERATION_OPTIONS', payload: options }),
    
    // Search and filter actions
    setSearchQuery: (query) => dispatch({ type: 'SET_SEARCH_QUERY', payload: query }),
    setActiveFilters: (filters) => dispatch({ type: 'SET_ACTIVE_FILTERS', payload: filters }),
    setSearchResults: (results) => dispatch({ type: 'SET_SEARCH_RESULTS', payload: results }),
    setSearchLoading: (loading) => dispatch({ type: 'SET_SEARCH_LOADING', payload: loading }),
    
    // Real-time update actions
    setLastSyncTime: (time) => dispatch({ type: 'SET_LAST_SYNC_TIME', payload: time }),
    addPendingChange: (changeId) => dispatch({ type: 'ADD_PENDING_CHANGE', payload: changeId }),
    removePendingChange: (changeId) => dispatch({ type: 'REMOVE_PENDING_CHANGE', payload: changeId }),
    clearPendingChanges: () => dispatch({ type: 'CLEAR_PENDING_CHANGES' }),
    
    // Storage actions
    setStorageQuota: (quota) => dispatch({ type: 'SET_STORAGE_QUOTA', payload: quota }),
    setStorageUsed: (used) => dispatch({ type: 'SET_STORAGE_USED', payload: used }),
    setSyncStatus: (status) => dispatch({ type: 'SET_SYNC_STATUS', payload: status }),
    
    // General loading and error actions
    setLoading: (loading) => dispatch({ type: 'SET_LOADING', payload: loading }),
    setError: (error) => dispatch({ type: 'SET_ERROR', payload: error }),
    
    // Refresh projects (for pull-to-refresh)
    refreshProjects: async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      try {
        // Simulate refresh delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Update last sync time
        dispatch({ type: 'SET_LAST_SYNC_TIME', payload: new Date().toISOString() });
        
        // Clear any errors
        dispatch({ type: 'SET_ERROR', payload: null });
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: 'Failed to refresh projects' });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    }
  };

  return (
    <AppContextProvider.Provider value={{ ...state, ...actions }}>
      {children}
    </AppContextProvider.Provider>
  );
};

// Hook to use the context
export const useApp = () => {
  const context = useContext(AppContextProvider);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppProvider;