// Custom hook for advanced search functionality

import { useState, useEffect, useMemo, useCallback } from 'react';
import { Project } from '../types/project';
import { useDebounce } from './useDebounce';
import SearchService, { SearchHistoryItem, SearchSuggestion } from '../services/searchService';
import { SearchResult } from '../utils/fuzzySearch';

export interface UseAdvancedSearchOptions {
  debounceMs?: number;
  threshold?: number;
  maxSuggestions?: number;
  maxHistory?: number;
  enableHistory?: boolean;
  enableSuggestions?: boolean;
}

export interface UseAdvancedSearchReturn {
  // Search state
  query: string;
  setQuery: (query: string) => void;
  results: SearchResult<Project>[];
  isSearching: boolean;
  
  // Suggestions and history
  suggestions: SearchSuggestion[];
  history: SearchHistoryItem[];
  showSuggestions: boolean;
  setShowSuggestions: (show: boolean) => void;
  
  // Actions
  clearSearch: () => void;
  clearHistory: () => void;
  removeFromHistory: (id: string) => void;
  selectSuggestion: (suggestion: string) => void;
  
  // Analytics
  searchAnalytics: {
    totalSearches: number;
    uniqueQueries: number;
    averageResultCount: number;
    popularQueries: Array<{ query: string; count: number }>;
  };
  
  // Highlighting
  highlightText: (text: string, indices?: number[]) => string;
}

export function useAdvancedSearch(
  projects: Project[],
  options: UseAdvancedSearchOptions = {}
): UseAdvancedSearchReturn {
  const {
    debounceMs = 300,
    threshold = 0.1,
    maxSuggestions = 5,
    maxHistory = 10,
    enableHistory = true,
    enableSuggestions = true
  } = options;

  // State
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult<Project>[]>([]);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [history, setHistory] = useState<SearchHistoryItem[]>([]);

  // Debounced query for search
  const debouncedQuery = useDebounce(query, debounceMs);

  // Search service instance
  const searchService = useMemo(() => SearchService.getInstance(), []);

  // Perform search when debounced query changes
  useEffect(() => {
    const performSearch = async () => {
      setIsSearching(true);
      
      try {
        const searchOptions = {
          includeHistory: enableHistory,
          includeSuggestions: enableSuggestions,
          maxHistory,
          maxSuggestions,
          threshold
        };

        const result = searchService.search(projects, debouncedQuery, searchOptions);
        
        setSearchResults(result.results);
        setSuggestions(result.suggestions);
        setHistory(result.history);
      } catch (error) {
        console.error('Search failed:', error);
        setSearchResults([]);
        setSuggestions([]);
      } finally {
        setIsSearching(false);
      }
    };

    performSearch();
  }, [
    debouncedQuery, 
    projects, 
    searchService, 
    enableHistory, 
    enableSuggestions, 
    maxHistory, 
    maxSuggestions, 
    threshold
  ]);

  // Update suggestions when query changes (for immediate feedback)
  useEffect(() => {
    if (query.trim() === '') {
      setShowSuggestions(false);
    } else if (enableSuggestions) {
      setShowSuggestions(true);
    }
  }, [query, enableSuggestions]);

  // Clear search
  const clearSearch = useCallback(() => {
    setQuery('');
    setShowSuggestions(false);
    setSearchResults([]);
  }, []);

  // Clear history
  const clearHistory = useCallback(() => {
    searchService.clearHistory();
    setHistory([]);
  }, [searchService]);

  // Remove from history
  const removeFromHistory = useCallback((id: string) => {
    searchService.removeFromHistory(id);
    setHistory(prev => prev.filter(item => item.id !== id));
  }, [searchService]);

  // Select suggestion
  const selectSuggestion = useCallback((suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
  }, []);

  // Get search analytics
  const searchAnalytics = useMemo(() => {
    return searchService.getSearchAnalytics();
  }, [searchService, history]); // Re-compute when history changes

  // Highlight text function
  const highlightText = useCallback((text: string, indices?: number[]): string => {
    if (!indices || indices.length === 0) return text;
    
    let result = '';
    let lastIndex = 0;
    
    // Group consecutive indices
    const groups: Array<{ start: number; end: number }> = [];
    let currentGroup = { start: indices[0], end: indices[0] };
    
    for (let i = 1; i < indices.length; i++) {
      if (indices[i] === currentGroup.end + 1) {
        currentGroup.end = indices[i];
      } else {
        groups.push(currentGroup);
        currentGroup = { start: indices[i], end: indices[i] };
      }
    }
    groups.push(currentGroup);
    
    // Build highlighted string
    for (const group of groups) {
      result += text.slice(lastIndex, group.start);
      result += `<mark class="bg-yellow-200 dark:bg-yellow-800 px-0.5 rounded">${text.slice(group.start, group.end + 1)}</mark>`;
      lastIndex = group.end + 1;
    }
    result += text.slice(lastIndex);
    
    return result;
  }, []);

  // Return search results or all projects if no query
  const results = useMemo(() => {
    if (!debouncedQuery.trim()) {
      return projects.map(project => ({
        item: project,
        match: { score: 1, matches: [] }
      }));
    }
    return searchResults;
  }, [debouncedQuery, projects, searchResults]);

  return {
    // Search state
    query,
    setQuery,
    results,
    isSearching,
    
    // Suggestions and history
    suggestions,
    history,
    showSuggestions,
    setShowSuggestions,
    
    // Actions
    clearSearch,
    clearHistory,
    removeFromHistory,
    selectSuggestion,
    
    // Analytics
    searchAnalytics,
    
    // Highlighting
    highlightText
  };
}