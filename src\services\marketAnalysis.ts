// Market analysis generator service
import { ProjectType, Complexity, MarketAnalysis } from '../types/project';

export interface DetailedMarketAnalysis extends MarketAnalysis {
  marketTrends: string[];
  growthRate: string;
  barriers: string[];
  opportunities: string[];
  riskFactors: string[];
  timeToMarket: string;
  competitiveAdvantage: string[];
}

export interface CompetitorAnalysis {
  name: string;
  marketShare: string;
  strengths: string[];
  weaknesses: string[];
  pricing: string;
  targetAudience: string;
}

export class MarketAnalysisService {
  private static marketData = {
    mobile: {
      marketSize: '$365B by 2025',
      growthRate: '8.2% CAGR',
      trends: [
        'Increased mobile commerce adoption',
        'Rise of super apps',
        'Focus on user privacy',
        'AI-powered personalization',
        '5G enabling new experiences'
      ],
      barriers: [
        'App store approval process',
        'High user acquisition costs',
        'Platform fragmentation',
        'Security concerns'
      ],
      opportunities: [
        'Emerging markets growth',
        'IoT integration',
        'AR/VR capabilities',
        'Voice interfaces'
      ],
      risks: [
        'Platform policy changes',
        'Increased competition',
        'Privacy regulations',
        'Technical debt'
      ],
      competitors: [
        {
          name: 'Native Development',
          marketShare: '45%',
          strengths: ['Best performance', 'Platform-specific features'],
          weaknesses: ['Higher development cost', 'Longer time to market'],
          pricing: '$50k-200k',
          targetAudience: 'Enterprise clients'
        },
        {
          name: 'Flutter Apps',
          marketShare: '25%',
          strengths: ['Single codebase', 'Fast development'],
          weaknesses: ['Limited platform features', 'Dart language'],
          pricing: '$30k-100k',
          targetAudience: 'Startups and SMBs'
        }
      ]
    },
    web: {
      marketSize: '$40B by 2025',
      growthRate: '5.4% CAGR',
      trends: [
        'JAMstack architecture adoption',
        'Progressive Web Apps growth',
        'Headless CMS popularity',
        'Micro-frontends architecture',
        'Web3 integration'
      ],
      barriers: [
        'Browser compatibility issues',
        'SEO complexity',
        'Performance optimization',
        'Security vulnerabilities'
      ],
      opportunities: [
        'PWA adoption',
        'Edge computing',
        'WebAssembly potential',
        'No-code/low-code integration'
      ],
      risks: [
        'Framework fatigue',
        'Rapid technology changes',
        'Security threats',
        'Performance bottlenecks'
      ],
      competitors: [
        {
          name: 'WordPress',
          marketShare: '40%',
          strengths: ['Large ecosystem', 'Easy to use'],
          weaknesses: ['Security issues', 'Performance problems'],
          pricing: '$500-10k',
          targetAudience: 'Small businesses'
        },
        {
          name: 'Custom Development',
          marketShare: '30%',
          strengths: ['Full customization', 'Optimal performance'],
          weaknesses: ['High cost', 'Long development time'],
          pricing: '$20k-200k',
          targetAudience: 'Enterprise clients'
        }
      ]
    },
    ai: {
      marketSize: '$126B by 2025',
      growthRate: '22.6% CAGR',
      trends: [
        'Generative AI mainstream adoption',
        'Edge AI deployment',
        'AI democratization',
        'Ethical AI focus',
        'Multimodal AI systems'
      ],
      barriers: [
        'High computational costs',
        'Data privacy concerns',
        'Skill shortage',
        'Regulatory uncertainty'
      ],
      opportunities: [
        'Industry-specific AI solutions',
        'AI-as-a-Service growth',
        'Open-source AI tools',
        'AI automation potential'
      ],
      risks: [
        'Regulatory changes',
        'Ethical concerns',
        'Technical limitations',
        'Market saturation'
      ],
      competitors: [
        {
          name: 'OpenAI',
          marketShare: '35%',
          strengths: ['Advanced models', 'Strong API'],
          weaknesses: ['High costs', 'Rate limits'],
          pricing: '$0.002-0.12/1k tokens',
          targetAudience: 'Developers and enterprises'
        },
        {
          name: 'Google AI',
          marketShare: '25%',
          strengths: ['Free tier', 'Integration with Google services'],
          weaknesses: ['Complex setup', 'Limited customization'],
          pricing: 'Freemium model',
          targetAudience: 'Developers and researchers'
        }
      ]
    },
    ecommerce: {
      marketSize: '$6.2T by 2025',
      growthRate: '14.7% CAGR',
      trends: [
        'Social commerce growth',
        'Voice commerce adoption',
        'Sustainable shopping focus',
        'Personalization at scale',
        'Omnichannel experiences'
      ],
      barriers: [
        'High customer acquisition costs',
        'Payment security concerns',
        'Logistics complexity',
        'Regulatory compliance'
      ],
      opportunities: [
        'Emerging markets expansion',
        'B2B e-commerce growth',
        'Subscription models',
        'AR/VR shopping experiences'
      ],
      risks: [
        'Economic downturns',
        'Supply chain disruptions',
        'Cybersecurity threats',
        'Platform dependency'
      ],
      competitors: [
        {
          name: 'Shopify',
          marketShare: '32%',
          strengths: ['Easy setup', 'App ecosystem'],
          weaknesses: ['Transaction fees', 'Limited customization'],
          pricing: '$29-299/month',
          targetAudience: 'SMBs and entrepreneurs'
        },
        {
          name: 'WooCommerce',
          marketShare: '28%',
          strengths: ['Free and open source', 'WordPress integration'],
          weaknesses: ['Requires technical knowledge', 'Hosting costs'],
          pricing: 'Free + hosting',
          targetAudience: 'WordPress users'
        }
      ]
    },
    form: {
      marketSize: '$2.5B by 2025',
      growthRate: '12.3% CAGR',
      trends: [
        'Conversational forms rise',
        'AI-powered form optimization',
        'Mobile-first form design',
        'Integration with CRM systems',
        'Privacy-focused data collection'
      ],
      barriers: [
        'Data privacy regulations',
        'Form abandonment rates',
        'Integration complexity',
        'User experience challenges'
      ],
      opportunities: [
        'No-code form builders',
        'Advanced analytics integration',
        'Multi-step form optimization',
        'Voice-enabled forms'
      ],
      risks: [
        'Privacy regulation changes',
        'Security breaches',
        'Platform competition',
        'User behavior changes'
      ],
      competitors: [
        {
          name: 'Typeform',
          marketShare: '25%',
          strengths: ['Beautiful UI', 'Conversational approach'],
          weaknesses: ['Limited free plan', 'Pricing'],
          pricing: '$25-83/month',
          targetAudience: 'Marketing teams'
        },
        {
          name: 'Google Forms',
          marketShare: '40%',
          strengths: ['Free', 'Google integration'],
          weaknesses: ['Limited customization', 'Basic features'],
          pricing: 'Free',
          targetAudience: 'General users'
        }
      ]
    }
  };

  static generateDetailedAnalysis(
    projectType: ProjectType,
    complexity: Complexity,
    description: string
  ): DetailedMarketAnalysis {
    const marketInfo = this.marketData[projectType];
    
    return {
      targetAudience: this.generateTargetAudience(projectType, complexity, description),
      marketSize: marketInfo.marketSize,
      competitors: this.selectRelevantCompetitors(projectType, complexity),
      uniqueSellingPoints: this.generateUSPs(projectType, complexity, description),
      revenueModel: this.selectRevenueModel(projectType, complexity),
      estimatedRevenue: this.estimateRevenue(projectType, complexity),
      marketTrends: marketInfo.trends,
      growthRate: marketInfo.growthRate,
      barriers: marketInfo.barriers,
      opportunities: marketInfo.opportunities,
      riskFactors: marketInfo.risks,
      timeToMarket: this.estimateTimeToMarket(complexity),
      competitiveAdvantage: this.generateCompetitiveAdvantages(projectType, complexity)
    };
  }

  static getCompetitorAnalysis(projectType: ProjectType): CompetitorAnalysis[] {
    return this.marketData[projectType].competitors;
  }

  private static generateTargetAudience(
    projectType: ProjectType,
    complexity: Complexity,
    description: string
  ): string {
    const baseAudiences = {
      mobile: 'Mobile users aged 18-45',
      web: 'Web users across all demographics',
      ai: 'Businesses and developers seeking AI solutions',
      ecommerce: 'Online shoppers and businesses',
      form: 'Businesses needing data collection'
    };

    const complexityModifiers = {
      simple: 'Small businesses and individuals',
      medium: 'Growing businesses and teams',
      complex: 'Enterprise clients and large organizations'
    };

    // Analyze description for specific audience indicators
    const keywords = description.toLowerCase();
    let specificAudience = '';

    if (keywords.includes('student') || keywords.includes('education')) {
      specificAudience = 'Students and educational institutions';
    } else if (keywords.includes('healthcare') || keywords.includes('medical')) {
      specificAudience = 'Healthcare professionals and patients';
    } else if (keywords.includes('finance') || keywords.includes('banking')) {
      specificAudience = 'Financial services and banking customers';
    } else if (keywords.includes('retail') || keywords.includes('shopping')) {
      specificAudience = 'Retail customers and merchants';
    }

    return specificAudience || `${baseAudiences[projectType]}, targeting ${complexityModifiers[complexity]}`;
  }

  private static selectRelevantCompetitors(projectType: ProjectType, complexity: Complexity): string[] {
    const allCompetitors = this.marketData[projectType].competitors;
    
    // Select competitors based on complexity
    if (complexity === 'simple') {
      return allCompetitors.filter(c => c.pricing.includes('Free') || c.pricing.includes('$')).map(c => c.name);
    } else if (complexity === 'complex') {
      return allCompetitors.filter(c => c.targetAudience.includes('Enterprise')).map(c => c.name);
    }
    
    return allCompetitors.map(c => c.name);
  }

  private static generateUSPs(projectType: ProjectType, complexity: Complexity, description: string): string[] {
    const baseUSPs = {
      mobile: ['Native performance', 'Offline functionality', 'Push notifications'],
      web: ['Cross-platform compatibility', 'SEO optimized', 'Fast loading'],
      ai: ['Advanced AI capabilities', 'Custom model training', 'Real-time processing'],
      ecommerce: ['Secure payments', 'Inventory management', 'Mobile-first design'],
      form: ['Drag-and-drop builder', 'Advanced validation', 'Integration ready']
    };

    const complexityUSPs = {
      simple: ['Easy to use', 'Quick setup', 'Cost-effective'],
      medium: ['Scalable architecture', 'Advanced features', 'Professional design'],
      complex: ['Enterprise-grade', 'Highly customizable', 'Advanced analytics']
    };

    // Add description-based USPs
    const descriptionUSPs: string[] = [];
    const keywords = description.toLowerCase();
    
    if (keywords.includes('real-time') || keywords.includes('live')) {
      descriptionUSPs.push('Real-time capabilities');
    }
    if (keywords.includes('secure') || keywords.includes('privacy')) {
      descriptionUSPs.push('Enhanced security features');
    }
    if (keywords.includes('ai') || keywords.includes('smart')) {
      descriptionUSPs.push('AI-powered intelligence');
    }

    return [
      ...baseUSPs[projectType],
      ...complexityUSPs[complexity],
      ...descriptionUSPs
    ].slice(0, 6); // Limit to 6 USPs
  }

  private static selectRevenueModel(projectType: ProjectType, complexity: Complexity): string {
    const revenueModels = {
      mobile: ['Freemium', 'Subscription', 'In-app purchases', 'Advertising'],
      web: ['SaaS', 'One-time purchase', 'Subscription', 'Commission'],
      ai: ['API usage', 'Subscription', 'Enterprise licensing'],
      ecommerce: ['Transaction fees', 'Subscription', 'Commission'],
      form: ['Freemium', 'Subscription', 'Enterprise plans']
    };

    const models = revenueModels[projectType];
    
    // Select based on complexity
    if (complexity === 'simple') {
      return models.includes('Freemium') ? 'Freemium' : models[0];
    } else if (complexity === 'complex') {
      return models.includes('Enterprise licensing') ? 'Enterprise licensing' : 
             models.includes('Subscription') ? 'Subscription' : models[models.length - 1];
    }
    
    return models[Math.floor(models.length / 2)]; // Middle option for medium complexity
  }

  private static estimateRevenue(projectType: ProjectType, complexity: Complexity): string {
    const revenueRanges = {
      simple: {
        mobile: '$2k-15k/month',
        web: '$1k-10k/month',
        ai: '$5k-25k/month',
        ecommerce: '$3k-20k/month',
        form: '$1k-8k/month'
      },
      medium: {
        mobile: '$15k-75k/month',
        web: '$10k-50k/month',
        ai: '$25k-150k/month',
        ecommerce: '$20k-100k/month',
        form: '$8k-40k/month'
      },
      complex: {
        mobile: '$75k-500k/month',
        web: '$50k-300k/month',
        ai: '$150k-1M/month',
        ecommerce: '$100k-800k/month',
        form: '$40k-200k/month'
      }
    };

    return revenueRanges[complexity][projectType];
  }

  private static estimateTimeToMarket(complexity: Complexity): string {
    const timeEstimates = {
      simple: '2-4 weeks',
      medium: '2-4 months',
      complex: '6-12 months'
    };

    return timeEstimates[complexity];
  }

  private static generateCompetitiveAdvantages(projectType: ProjectType, complexity: Complexity): string[] {
    const advantages = {
      mobile: [
        'Cross-platform development efficiency',
        'Native performance optimization',
        'Seamless offline functionality',
        'Advanced push notification system'
      ],
      web: [
        'Superior SEO optimization',
        'Lightning-fast loading speeds',
        'Progressive Web App capabilities',
        'Advanced accessibility features'
      ],
      ai: [
        'Custom AI model training',
        'Real-time processing capabilities',
        'Advanced data analytics',
        'Ethical AI implementation'
      ],
      ecommerce: [
        'Omnichannel integration',
        'Advanced inventory management',
        'Personalized shopping experience',
        'Secure payment processing'
      ],
      form: [
        'Intuitive drag-and-drop interface',
        'Advanced conditional logic',
        'Seamless third-party integrations',
        'Real-time collaboration features'
      ]
    };

    const complexityBonus = {
      simple: ['Rapid deployment', 'Cost-effective solution'],
      medium: ['Scalable architecture', 'Professional support'],
      complex: ['Enterprise-grade security', 'Custom integrations', 'Dedicated support']
    };

    return [
      ...advantages[projectType].slice(0, 3),
      ...complexityBonus[complexity]
    ];
  }
}