// Dynamic imports for heavy features and services
export const dynamicImports = {
  // Heavy components
  async loadMobilePerformanceDemo() {
    const { default: MobilePerformanceDemo } = await import('../components/MobilePerformanceDemo');
    return MobilePerformanceDemo;
  },

  async loadMemoryManagementDemo() {
    const { default: MemoryManagementDemo } = await import('../components/MemoryManagementDemo');
    return MemoryManagementDemo;
  },

  // Services
  async loadExportService() {
    const exportService = await import('../services/exportService');
    return exportService;
  },

  async loadTemplateService() {
    const templateService = await import('../services/templateService');
    return templateService;
  },

  async loadMarketAnalysis() {
    const marketAnalysis = await import('../services/marketAnalysis');
    return marketAnalysis;
  },

  // UI Components (for on-demand loading)
  async loadModal() {
    const { default: Modal } = await import('../components/ui/Modal');
    return Modal;
  },

  async loadProgressBar() {
    const { ProgressBar } = await import('../components/ui/ProgressBar');
    return ProgressBar;
  },

  async loadVirtualScrollList() {
    const { VirtualScrollList } = await import('../components/ui/VirtualScrollList');
    return VirtualScrollList;
  },

  // Feature components
  async loadProjectManager() {
    const { default: ProjectManager } = await import('../components/ProjectManager');
    return ProjectManager;
  },

  async loadAppGenerator() {
    const { default: AppGenerator } = await import('../components/AppGenerator');
    return AppGenerator;
  },

  // Hooks
  async loadUseProgressiveLoading() {
    const { useProgressiveLoading } = await import('../hooks/useProgressiveLoading');
    return useProgressiveLoading;
  },

  async loadUseVirtualScrolling() {
    const { useVirtualScrolling } = await import('../hooks/useProgressiveLoading');
    return useVirtualScrolling;
  },
};

// Preload critical components
export async function preloadCriticalComponents() {
  try {
    // Preload components that are likely to be used soon
    await Promise.all([
      import('../components/ui/Modal'),
      import('../components/ui/LoadingSpinner'),
      import('../components/ui/Button'),
    ]);
  } catch (error) {
    console.warn('Failed to preload critical components:', error);
  }
}

// Preload based on user interaction
export async function preloadOnInteraction() {
  try {
    // Preload components that might be needed after user interaction
    await Promise.all([
      import('../components/ProjectManager'),
      import('../services/projectCRUD'),
      import('../hooks/useProjectManager'),
    ]);
  } catch (error) {
    console.warn('Failed to preload interaction components:', error);
  }
}

// Preload heavy features on idle
export async function preloadHeavyFeatures() {
  if ('requestIdleCallback' in window) {
    requestIdleCallback(async () => {
      try {
        await Promise.all([
          import('../components/MobilePerformanceDemo'),
          import('../components/MemoryManagementDemo'),
          import('../services/exportService'),
        ]);
      } catch (error) {
        console.warn('Failed to preload heavy features:', error);
      }
    });
  }
}

// Bundle analyzer helper (development only)
export function analyzeBundleSize() {
  if (process.env.NODE_ENV === 'development') {
    console.group('Bundle Analysis');
    console.log('Dynamic imports available:', Object.keys(dynamicImports));
    console.log('Use browser dev tools to analyze chunk sizes');
    console.groupEnd();
  }
}

// Chunk loading error handler
export function handleChunkLoadError(error: Error, chunkName: string) {
  console.error(`Failed to load chunk: ${chunkName}`, error);
  
  // Attempt to reload the page if chunk loading fails
  if (error.message.includes('Loading chunk')) {
    console.warn('Chunk loading failed, attempting page reload...');
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  }
}

// Progressive enhancement loader
export class ProgressiveEnhancementLoader {
  private loadedFeatures = new Set<string>();
  private loadingPromises = new Map<string, Promise<any>>();

  async loadFeature(featureName: keyof typeof dynamicImports) {
    if (this.loadedFeatures.has(featureName)) {
      return;
    }

    if (this.loadingPromises.has(featureName)) {
      return this.loadingPromises.get(featureName);
    }

    const loadPromise = dynamicImports[featureName]()
      .then((module) => {
        this.loadedFeatures.add(featureName);
        this.loadingPromises.delete(featureName);
        return module;
      })
      .catch((error) => {
        this.loadingPromises.delete(featureName);
        handleChunkLoadError(error, featureName);
        throw error;
      });

    this.loadingPromises.set(featureName, loadPromise);
    return loadPromise;
  }

  isFeatureLoaded(featureName: string): boolean {
    return this.loadedFeatures.has(featureName);
  }

  getLoadedFeatures(): string[] {
    return Array.from(this.loadedFeatures);
  }
}

// Global instance
export const progressiveLoader = new ProgressiveEnhancementLoader();