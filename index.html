<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI App Generator - Transform Ideas into Apps</title>
    <meta name="description" content="Transform your ideas into detailed app concepts with AI-powered generation. Modern glassmorphism UI with beautiful animations." />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Debug: Add service worker unregistration for development -->
    <script>
      // Check if we need to unregister service workers (for debugging)
      if (window.location.search.includes('unregister-sw')) {
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.getRegistrations().then(function(registrations) {
            for(let registration of registrations) {
              registration.unregister().then(function(boolean) {
                console.log('Service Worker unregistered:', boolean);
              });
            }
            // Remove the query parameter and reload
            const url = new URL(window.location);
            url.searchParams.delete('unregister-sw');
            window.history.replaceState({}, document.title, url.pathname);
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          });
        }
      }
    </script>
  </body>
</html>
