import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../utils/helpers';

interface AnimatedCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'hover' | 'press' | 'glow';
  animation?: 'fadeIn' | 'slideUp' | 'scale' | 'none';
  delay?: number;
  duration?: number;
  onClick?: () => void;
  onHover?: (isHovered: boolean) => void;
  disabled?: boolean;
  isDragging?: boolean;
}

const cardVariants = {
  default: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
  },
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  },
  slideUp: {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -30 }
  },
  scale: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 }
  }
};

const hoverVariants = {
  default: {
    scale: 1,
    y: 0,
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
  },
  hover: {
    scale: 1.02,
    y: -2,
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    transition: {
      duration: 0.2,
      ease: 'easeOut'
    }
  },
  press: {
    scale: 0.98,
    y: 0,
    transition: {
      duration: 0.1,
      ease: 'easeInOut'
    }
  },
  glow: {
    scale: 1.02,
    y: -2,
    boxShadow: '0 0 20px rgba(59, 130, 246, 0.3), 0 20px 25px -5px rgba(0, 0, 0, 0.1)',
    transition: {
      duration: 0.2,
      ease: 'easeOut'
    }
  },
  dragging: {
    scale: 1.05,
    rotate: 2,
    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    transition: {
      duration: 0.2,
      ease: 'easeOut'
    }
  }
};

const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  className,
  variant = 'default',
  animation = 'slideUp',
  delay = 0,
  duration = 0.3,
  onClick,
  onHover,
  disabled = false,
  isDragging = false
}) => {
  const animationVariant = animation === 'none' ? {} : cardVariants[animation] || cardVariants.default;

  const getHoverState = () => {
    if (isDragging) return 'dragging';
    if (variant === 'glow') return 'glow';
    if (variant === 'hover' || onClick) return 'hover';
    return 'default';
  };

  return (
    <motion.div
      variants={animationVariant}
      initial="initial"
      animate="animate"
      exit="exit"
      whileHover={disabled ? undefined : getHoverState()}
      whileTap={disabled ? undefined : 'press'}
      transition={{
        duration,
        delay,
        ease: 'easeOut'
      }}
      className={cn(
        'relative bg-white rounded-xl border border-gray-200 overflow-hidden',
        'transition-colors duration-200',
        onClick && !disabled && 'cursor-pointer',
        disabled && 'opacity-50 cursor-not-allowed',
        isDragging && 'z-50',
        className
      )}
      onClick={disabled ? undefined : onClick}
      onHoverStart={() => onHover?.(true)}
      onHoverEnd={() => onHover?.(false)}
      style={{
        transformOrigin: 'center center'
      }}
    >
      {children}
      
      {/* Subtle gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent pointer-events-none" />
      
      {/* Glow effect for special states */}
      {variant === 'glow' && (
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 pointer-events-none" />
      )}
    </motion.div>
  );
};

export default AnimatedCard;