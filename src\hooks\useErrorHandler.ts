import { useCallback, useState } from 'react';
import { ErrorType } from '../types/error';
import { errorService } from '../services/errorService';

interface UseErrorHandlerReturn {
  error: Error | null;
  hasError: boolean;
  handleError: (error: Error, errorType?: ErrorType) => void;
  clearError: () => void;
  retry: (fn: () => void | Promise<void>) => Promise<void>;
}

export const useErrorHandler = (
  defaultErrorType: ErrorType = ErrorType.COMPONENT_ERROR
): UseErrorHandlerReturn => {
  const [error, setError] = useState<Error | null>(null);

  const handleError = useCallback((error: Error, errorType: ErrorType = defaultErrorType) => {
    setError(error);
    errorService.logError(error, errorType);
  }, [defaultErrorType]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const retry = useCallback(async (fn: () => void | Promise<void>) => {
    try {
      clearError();
      await fn();
    } catch (error) {
      handleError(error as Error);
    }
  }, [handleError, clearError]);

  return {
    error,
    hasError: error !== null,
    handleError,
    clearError,
    retry
  };
};