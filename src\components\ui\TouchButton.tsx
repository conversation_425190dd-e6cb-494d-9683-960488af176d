import React, { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../utils/helpers';
import { useTouchFeedback } from '../../hooks/useTouchFeedback';

interface TouchButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  loading?: boolean;
  disabled?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  hapticFeedback?: boolean;
  visualFeedback?: boolean;
  pressAnimation?: boolean;
  className?: string;
}

const TouchButton: React.FC<TouchButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  loading = false,
  disabled = false,
  leftIcon,
  rightIcon,
  hapticFeedback = true,
  visualFeedback = true,
  pressAnimation = true,
  className,
  onClick,
  ...props
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { attachTouchFeedback } = useTouchFeedback({
    hapticFeedback,
    visualFeedback: visualFeedback && pressAnimation,
    feedbackDuration: 150
  });

  useEffect(() => {
    if (buttonRef.current && !disabled && !loading) {
      return attachTouchFeedback(buttonRef.current);
    }
  }, [attachTouchFeedback, disabled, loading]);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;
    onClick?.(e);
  };

  const getVariantClasses = () => {
    const variants = {
      primary: 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white border-blue-600 shadow-lg shadow-blue-600/25',
      secondary: 'bg-gray-100 hover:bg-gray-200 active:bg-gray-300 text-gray-900 border-gray-200 shadow-sm',
      ghost: 'bg-transparent hover:bg-gray-100 active:bg-gray-200 text-gray-700 border-transparent',
      danger: 'bg-red-600 hover:bg-red-700 active:bg-red-800 text-white border-red-600 shadow-lg shadow-red-600/25'
    };

    if (disabled || loading) {
      return 'bg-gray-300 text-gray-500 border-gray-300 cursor-not-allowed';
    }

    return variants[variant];
  };

  const getSizeClasses = () => {
    const sizes = {
      sm: 'px-3 py-2 text-sm min-h-[36px]',
      md: 'px-4 py-2.5 text-sm min-h-[44px]',
      lg: 'px-6 py-3 text-base min-h-[48px]',
      xl: 'px-8 py-4 text-lg min-h-[56px]'
    };
    return sizes[size];
  };

  const buttonClasses = cn(
    // Base styles
    'relative inline-flex items-center justify-center gap-2 font-medium rounded-xl border transition-all duration-200 ease-out',
    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
    'select-none touch-manipulation',
    // Ensure minimum touch target size (44px)
    'min-w-[44px]',
    // Variant styles
    getVariantClasses(),
    // Size styles
    getSizeClasses(),
    // Width
    fullWidth && 'w-full',
    // Custom classes
    className
  );

  const iconClasses = cn(
    'flex-shrink-0',
    size === 'sm' && 'w-4 h-4',
    size === 'md' && 'w-4 h-4',
    size === 'lg' && 'w-5 h-5',
    size === 'xl' && 'w-6 h-6'
  );

  return (
    <motion.button
      ref={buttonRef}
      className={buttonClasses}
      disabled={disabled || loading}
      onClick={handleClick}
      whileTap={pressAnimation && !disabled && !loading ? { scale: 0.95 } : undefined}
      transition={{ duration: 0.1 }}
      {...props}
    >
      {/* Loading spinner */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className={cn(
            'animate-spin rounded-full border-2 border-current border-t-transparent',
            size === 'sm' && 'w-4 h-4',
            size === 'md' && 'w-4 h-4',
            size === 'lg' && 'w-5 h-5',
            size === 'xl' && 'w-6 h-6'
          )} />
        </div>
      )}

      {/* Content */}
      <div className={cn('flex items-center justify-center gap-2', loading && 'opacity-0')}>
        {leftIcon && (
          <span className={iconClasses}>
            {leftIcon}
          </span>
        )}
        
        <span className="truncate">
          {children}
        </span>
        
        {rightIcon && (
          <span className={iconClasses}>
            {rightIcon}
          </span>
        )}
      </div>
    </motion.button>
  );
};

export default TouchButton;