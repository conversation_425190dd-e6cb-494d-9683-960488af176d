import { useEffect, useRef } from 'react';
import { useInView, useAnimation } from 'framer-motion';
import { createSlideVariant } from '../utils/animations';

export interface UseSlideInOptions {
  direction?: 'up' | 'down' | 'left' | 'right';
  distance?: number;
  duration?: number;
  delay?: number;
  threshold?: number;
  triggerOnce?: boolean;
  rootMargin?: string;
}

export const useSlideIn = (options: UseSlideInOptions = {}) => {
  const {
    direction = 'up',
    distance = 20,
    duration = 0.3,
    delay = 0,
    threshold = 0.1,
    triggerOnce = true,
    rootMargin = '0px'
  } = options;

  const ref = useRef<HTMLElement>(null);
  const isInView = useInView(ref, {
    threshold,
    once: triggerOnce,
    margin: rootMargin
  });
  const controls = useAnimation();

  const variants = createSlideVariant(direction, distance, duration, delay);

  useEffect(() => {
    if (isInView) {
      controls.start('animate');
    } else if (!triggerOnce) {
      controls.start('initial');
    }
  }, [isInView, controls, triggerOnce]);

  return {
    ref,
    variants,
    animate: controls,
    isInView
  };
};

export default useSlideIn;