import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronDown,
  ChevronUp,
  Edit,
  Save,
  X,
  Calendar,
  Clock,
  User,
  Tag,
  TrendingUp,
  Activity,
  BarChart3,
  FileText,
  Star,
  Archive,
  Trash2,
  Copy
} from 'lucide-react';
import { Project } from '../types/project';
import { ProjectCRUDService } from '../services/projectCRUD';
import { useApp } from '../context/AppContext';
import { useDebounce } from '../hooks/useDebounce';
import { formatDate, getStatusStyles, getPriorityStyles } from '../utils/helpers';
import Button from './ui/Button';
import Input from './ui/Input';
import Badge from './ui/Badge';
import { cn } from '../utils/helpers';

interface ProjectDetailViewProps {
  project: Project;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onAction: (projectId: string, action: 'star' | 'archive' | 'delete' | 'duplicate' | 'edit') => void;
  className?: string;
}

interface ActivityItem {
  id: string;
  type: 'created' | 'updated' | 'status_changed' | 'starred' | 'archived';
  message: string;
  timestamp: string;
  user?: string;
}

const ProjectDetailView: React.FC<ProjectDetailViewProps> = ({
  project,
  isExpanded,
  onToggleExpanded,
  onAction,
  className
}) => {
  const { updateProject, addOptimisticUpdate, removeOptimisticUpdate } = useApp();
  
  // Editing states
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    name: project.name,
    description: project.description,
    tags: project.tags.join(', ')
  });
  const [isSaving, setIsSaving] = useState(false);
  const [editErrors, setEditErrors] = useState<Record<string, string>>({});

  // Auto-save functionality
  const debouncedEditData = useDebounce(editData, 1000);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Mock activity data - in real app this would come from API
  const [activities] = useState<ActivityItem[]>([
    {
      id: '1',
      type: 'created',
      message: 'Project created',
      timestamp: project.createdAt,
      user: 'You'
    },
    {
      id: '2',
      type: 'updated',
      message: 'Project description updated',
      timestamp: project.lastModified,
      user: 'You'
    },
    {
      id: '3',
      type: 'status_changed',
      message: `Status changed to ${project.status}`,
      timestamp: project.lastModified,
      user: 'You'
    }
  ]);

  // Mock analytics data
  const [analytics] = useState({
    viewCount: Math.floor(Math.random() * 100) + 10,
    editCount: Math.floor(Math.random() * 20) + 5,
    lastViewed: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    timeSpent: Math.floor(Math.random() * 120) + 30 // minutes
  });

  // Reset edit data when project changes
  useEffect(() => {
    setEditData({
      name: project.name,
      description: project.description,
      tags: project.tags.join(', ')
    });
    setHasUnsavedChanges(false);
    setEditErrors({});
  }, [project]);

  // Auto-save when debounced data changes
  useEffect(() => {
    if (isEditing && hasUnsavedChanges && !isSaving) {
      handleAutoSave();
    }
  }, [debouncedEditData, isEditing, hasUnsavedChanges, isSaving, handleAutoSave]);

  // Handle edit mode toggle
  const handleEditToggle = () => {
    if (isEditing && hasUnsavedChanges) {
      handleSave();
    } else {
      setIsEditing(!isEditing);
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof typeof editData, value: string) => {
    setEditData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
    
    // Clear field error
    if (editErrors[field]) {
      setEditErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Auto-save functionality
  const handleAutoSave = async () => {
    if (!hasUnsavedChanges) return;

    setIsSaving(true);
    
    try {
      const tagsArray = editData.tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      await ProjectCRUDService.updateProject(
        {
          id: project.id,
          name: editData.name,
          description: editData.description,
          tags: tagsArray
        },
        // Optimistic update
        (id, updates) => {
          addOptimisticUpdate(id, updates);
          updateProject(id, updates);
        },
        // Success
        () => {
          removeOptimisticUpdate(project.id);
          setHasUnsavedChanges(false);
        },
        // Error
        (error) => {
          console.error('Auto-save failed:', error);
          setEditErrors({ general: 'Auto-save failed' });
        }
      );
    } catch (error) {
      console.error('Auto-save error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle manual save
  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      const tagsArray = editData.tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      await ProjectCRUDService.updateProject(
        {
          id: project.id,
          name: editData.name,
          description: editData.description,
          tags: tagsArray
        },
        // Optimistic update
        (id, updates) => {
          addOptimisticUpdate(id, updates);
          updateProject(id, updates);
        },
        // Success
        () => {
          removeOptimisticUpdate(project.id);
          setHasUnsavedChanges(false);
          setIsEditing(false);
        },
        // Error
        (error) => {
          setEditErrors({ general: error });
        }
      );
    } catch (error) {
      setEditErrors({ general: 'Save failed' });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditData({
      name: project.name,
      description: project.description,
      tags: project.tags.join(', ')
    });
    setIsEditing(false);
    setHasUnsavedChanges(false);
    setEditErrors({});
  };

  const statusStyles = getStatusStyles(project.status);
  const priorityColor = getPriorityStyles(project.priority);

  return (
    <motion.div
      layout
      className={cn(
        'bg-white rounded-xl border border-gray-200 overflow-hidden',
        className
      )}
    >
      {/* Header - Always Visible */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4 flex-1 min-w-0">
            {/* Project Icon */}
            <div className="w-12 h-12 bg-blue-100 rounded-xl border-2 border-blue-200 flex items-center justify-center flex-shrink-0">
              <div className="w-6 h-6 bg-blue-600 rounded opacity-80" />
            </div>

            {/* Project Info */}
            <div className="flex-1 min-w-0">
              {isEditing ? (
                <div className="space-y-3">
                  <Input
                    value={editData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Project name"
                    error={editErrors.name}
                    className="text-lg font-semibold"
                  />
                  <textarea
                    value={editData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Project description"
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  />
                  {editErrors.description && (
                    <p className="text-sm text-red-600">{editErrors.description}</p>
                  )}
                </div>
              ) : (
                <>
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-xl font-semibold text-gray-900 truncate">
                      {project.name}
                    </h3>
                    {project.isStarred && (
                      <Star className="w-5 h-5 text-yellow-500 fill-current flex-shrink-0" />
                    )}
                    {isSaving && (
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                        <span>Saving...</span>
                      </div>
                    )}
                  </div>
                  <p className="text-gray-600 mb-3">{project.description}</p>
                </>
              )}

              {/* Status and Progress */}
              <div className="flex items-center gap-4">
                <Badge 
                  variant={
                    project.status === 'active' ? 'success' : 
                    project.status === 'completed' ? 'info' : 
                    project.status === 'error' ? 'error' : 'warning'
                  }
                >
                  {project.status}
                </Badge>
                
                <div className="flex items-center gap-2">
                  <div className={cn('w-2 h-2 rounded-full', priorityColor.replace('text-', 'bg-'))} />
                  <span className="text-sm text-gray-500 capitalize">{project.priority}</span>
                </div>

                {project.progress > 0 && (
                  <div className="flex items-center gap-2">
                    <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <motion.div
                        className="h-full bg-blue-500 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${project.progress}%` }}
                        transition={{ duration: 0.8, ease: 'easeOut' }}
                      />
                    </div>
                    <span className="text-sm text-gray-500">{project.progress}%</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2 ml-4">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelEdit}
                  leftIcon={<X className="w-4 h-4" />}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleSave}
                  loading={isSaving}
                  leftIcon={<Save className="w-4 h-4" />}
                >
                  Save
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleEditToggle}
                  leftIcon={<Edit className="w-4 h-4" />}
                >
                  Edit
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onToggleExpanded}
                  leftIcon={isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                >
                  {isExpanded ? 'Less' : 'More'}
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Tags */}
        {isEditing ? (
          <div className="mt-4">
            <Input
              value={editData.tags}
              onChange={(e) => handleInputChange('tags', e.target.value)}
              placeholder="Tags (comma separated)"
              error={editErrors.tags}
            />
          </div>
        ) : (
          project.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-4">
              {project.tags.map((tag, index) => (
                <motion.span
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-700 rounded-md text-sm"
                >
                  <Tag className="w-3 h-3" />
                  {tag}
                </motion.span>
              ))}
            </div>
          )
        )}
      </div>

      {/* Expanded Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="overflow-hidden"
          >
            <div className="p-6 space-y-6">
              {/* Project Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Basic Info */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    Project Details
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Type:</span>
                      <span className="capitalize font-medium">{project.type}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Complexity:</span>
                      <span className="capitalize font-medium">{project.complexity}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Created:</span>
                      <span className="font-medium">{formatDate(project.createdAt)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Modified:</span>
                      <span className="font-medium">{formatDate(project.lastModified)}</span>
                    </div>
                  </div>
                </div>

                {/* Analytics */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                    <BarChart3 className="w-4 h-4" />
                    Analytics
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Views:</span>
                      <span className="font-medium">{analytics.viewCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Edits:</span>
                      <span className="font-medium">{analytics.editCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Time Spent:</span>
                      <span className="font-medium">{analytics.timeSpent}m</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Last Viewed:</span>
                      <span className="font-medium">{formatDate(analytics.lastViewed)}</span>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                    <Activity className="w-4 h-4" />
                    Quick Actions
                  </h4>
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onAction(project.id, 'star')}
                      leftIcon={<Star className="w-4 h-4" />}
                      className="w-full justify-start"
                    >
                      {project.isStarred ? 'Remove Star' : 'Add Star'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onAction(project.id, 'duplicate')}
                      leftIcon={<Copy className="w-4 h-4" />}
                      className="w-full justify-start"
                    >
                      Duplicate
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onAction(project.id, 'archive')}
                      leftIcon={<Archive className="w-4 h-4" />}
                      className="w-full justify-start"
                    >
                      {project.status === 'archived' ? 'Restore' : 'Archive'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onAction(project.id, 'delete')}
                      leftIcon={<Trash2 className="w-4 h-4" />}
                      className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </div>

              {/* Activity Timeline */}
              <div>
                <h4 className="font-semibold text-gray-900 flex items-center gap-2 mb-4">
                  <Clock className="w-4 h-4" />
                  Recent Activity
                </h4>
                <div className="space-y-3">
                  {activities.map((activity, index) => (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <Activity className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900">{activity.message}</p>
                        <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                          <User className="w-3 h-3" />
                          <span>{activity.user}</span>
                          <span>•</span>
                          <Calendar className="w-3 h-3" />
                          <span>{formatDate(activity.timestamp)}</span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default ProjectDetailView;