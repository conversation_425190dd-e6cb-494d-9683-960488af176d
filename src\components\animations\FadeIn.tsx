import React from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { useFadeIn, UseFadeInOptions } from '../../hooks/useFadeIn';

interface FadeInProps extends Omit<HTMLMotionProps<'div'>, 'variants' | 'animate' | 'initial'> {
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
  animationOptions?: UseFadeInOptions;
}

export const FadeIn: React.FC<FadeInProps> = ({
  children,
  as = 'div',
  animationOptions,
  ...props
}) => {
  const { ref, variants, animate } = useFadeIn(animationOptions);
  
  const MotionComponent = motion[as as keyof typeof motion] as any;

  return (
    <MotionComponent
      ref={ref}
      variants={variants}
      initial="initial"
      animate={animate}
      {...props}
    >
      {children}
    </MotionComponent>
  );
};

export default FadeIn;