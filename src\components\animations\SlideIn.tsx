import React from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { useSlideIn, UseSlideInOptions } from '../../hooks/useSlideIn';

interface SlideInProps extends Omit<HTMLMotionProps<'div'>, 'variants' | 'animate' | 'initial'> {
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
  animationOptions?: UseSlideInOptions;
}

export const SlideIn: React.FC<SlideInProps> = ({
  children,
  as = 'div',
  animationOptions,
  ...props
}) => {
  const { ref, variants, animate } = useSlideIn(animationOptions);
  
  const MotionComponent = motion[as as keyof typeof motion] as any;

  return (
    <MotionComponent
      ref={ref}
      variants={variants}
      initial="initial"
      animate={animate}
      {...props}
    >
      {children}
    </MotionComponent>
  );
};

export default SlideIn;