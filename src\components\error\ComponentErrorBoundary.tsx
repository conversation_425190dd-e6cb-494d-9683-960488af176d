import React, { ReactNode } from 'react';
import { BaseErrorBoundary } from './BaseErrorBoundary';
import { ErrorType } from '../../types/error';
import { AlertTriangle } from 'lucide-react';

interface Props {
  children: ReactNode;
  componentName?: string;
  inline?: boolean;
  showRetry?: boolean;
}

export const ComponentErrorBoundary: React.FC<Props> = ({ 
  children, 
  componentName = 'Component',
  inline = false,
  showRetry = true
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    console.warn(`Component error in ${componentName}:`, error, errorInfo);
  };

  const inlineFallback = (
    <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
      <AlertTriangle className="h-4 w-4 mr-2 flex-shrink-0" />
      <span>{componentName} failed to load</span>
    </div>
  );

  const blockFallback = (
    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
      <div className="flex items-center mb-2">
        <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
        <h4 className="font-medium text-red-800">
          {componentName} Error
        </h4>
      </div>
      <p className="text-red-700 text-sm mb-3">
        This component encountered an error and couldn't be displayed.
      </p>
      {showRetry && (
        <button
          onClick={() => window.location.reload()}
          className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
        >
          Reload Page
        </button>
      )}
    </div>
  );

  return (
    <BaseErrorBoundary
      errorType={ErrorType.COMPONENT_ERROR}
      onError={handleError}
      maxRetries={1}
      fallback={inline ? inlineFallback : blockFallback}
    >
      {children}
    </BaseErrorBoundary>
  );
};