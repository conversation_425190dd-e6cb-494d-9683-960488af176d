import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
    include: ['react', 'react-dom', 'framer-motion']
  },
  server: {
    port: 3000,
    open: true
  },
  build: {
    target: 'es2020',
    minify: 'terser',
    sourcemap: process.env.NODE_ENV === 'development',
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          // Core React libraries
          'react-vendor': ['react', 'react-dom'],

          // Router
          'router': ['react-router-dom'],

          // Animation libraries
          'animations': ['framer-motion'],

          // DnD libraries
          'dnd': ['@dnd-kit/core', '@dnd-kit/sortable', '@dnd-kit/utilities'],

          // UI libraries
          'ui-vendor': ['lucide-react'],

          // Heavy features
          'performance-demo': ['./src/components/MobilePerformanceDemo.tsx'],
          'memory-demo': ['./src/components/MemoryManagementDemo.tsx'],

          // Services
          'services': [
            './src/services/appGenerator.ts',
            './src/services/exportService.ts',
            './src/services/projectCRUD.ts',
          ],
        },
      },
    },
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: process.env.NODE_ENV === 'production',
      },
    },
  },
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
  },
  esbuild: {
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
  },
});
