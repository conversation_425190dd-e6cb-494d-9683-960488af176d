/**
 * Memory Management Utilities
 * Provides utilities for preventing memory leaks and managing resources efficiently
 */

// Types for memory management
export interface CleanupFunction {
  (): void;
}

export interface MemoryManager {
  addCleanup: (cleanup: CleanupFunction) => void;
  cleanup: () => void;
  isDestroyed: boolean;
}

export interface TimerHandle {
  id: number | NodeJS.Timeout;
  type: 'timeout' | 'interval' | 'animation';
  cleanup: () => void;
}

export interface EventListenerHandle {
  element: EventTarget;
  event: string;
  handler: EventListener;
  options?: AddEventListenerOptions;
  cleanup: () => void;
}

/**
 * Memory Manager Class
 * Centralized cleanup management for components and hooks
 */
export class ComponentMemoryManager implements MemoryManager {
  private cleanupFunctions: Set<CleanupFunction> = new Set();
  private timers: Set<TimerHandle> = new Set();
  private eventListeners: Set<EventListenerHandle> = new Set();
  private animationFrames: Set<number> = new Set();
  private observers: Set<{ observer: { disconnect: () => void }; cleanup: () => void }> = new Set();
  public isDestroyed = false;

  /**
   * Add a cleanup function to be called when component unmounts
   */
  addCleanup(cleanup: CleanupFunction): void {
    if (this.isDestroyed) {
      cleanup(); // Execute immediately if already destroyed
      return;
    }
    this.cleanupFunctions.add(cleanup);
  }

  /**
   * Add a timer with automatic cleanup
   */
  addTimer(
    callback: () => void,
    delay: number,
    type: 'timeout' | 'interval' = 'timeout'
  ): TimerHandle {
    const id = type === 'timeout' 
      ? setTimeout(callback, delay)
      : setInterval(callback, delay);

    const handle: TimerHandle = {
      id,
      type,
      cleanup: () => {
        if (type === 'timeout') {
          clearTimeout(id as NodeJS.Timeout);
        } else {
          clearInterval(id as NodeJS.Timeout);
        }
        this.timers.delete(handle);
      }
    };

    this.timers.add(handle);
    this.addCleanup(handle.cleanup);
    
    return handle;
  }

  /**
   * Add an animation frame with automatic cleanup
   */
  addAnimationFrame(callback: () => void): number {
    const id = requestAnimationFrame(() => {
      this.animationFrames.delete(id);
      callback();
    });

    this.animationFrames.add(id);
    this.addCleanup(() => {
      cancelAnimationFrame(id);
      this.animationFrames.delete(id);
    });

    return id;
  }

  /**
   * Add an event listener with automatic cleanup
   */
  addEventListener(
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ): EventListenerHandle {
    const handle: EventListenerHandle = {
      element,
      event,
      handler,
      options,
      cleanup: () => {
        element.removeEventListener(event, handler, options);
        this.eventListeners.delete(handle);
      }
    };

    element.addEventListener(event, handler, options);
    this.eventListeners.add(handle);
    this.addCleanup(handle.cleanup);

    return handle;
  }

  /**
   * Add an observer (IntersectionObserver, MutationObserver, etc.) with cleanup
   */
  addObserver<T extends { disconnect: () => void }>(
    observer: T,
    customCleanup?: () => void
  ): T {
    const cleanup = customCleanup || (() => observer.disconnect());
    
    this.observers.add({ observer, cleanup });
    this.addCleanup(cleanup);

    return observer;
  }

  /**
   * Clean up all resources
   */
  cleanup(): void {
    if (this.isDestroyed) return;

    // Clean up timers
    this.timers.forEach(timer => timer.cleanup());
    this.timers.clear();

    // Clean up event listeners
    this.eventListeners.forEach(listener => listener.cleanup());
    this.eventListeners.clear();

    // Clean up animation frames
    this.animationFrames.forEach(id => cancelAnimationFrame(id));
    this.animationFrames.clear();

    // Clean up observers
    this.observers.forEach(({ cleanup }) => cleanup());
    this.observers.clear();

    // Execute all cleanup functions
    this.cleanupFunctions.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        console.error('Error during cleanup:', error);
      }
    });
    this.cleanupFunctions.clear();

    this.isDestroyed = true;
  }

  /**
   * Get memory usage statistics
   */
  getStats() {
    return {
      cleanupFunctions: this.cleanupFunctions.size,
      timers: this.timers.size,
      eventListeners: this.eventListeners.size,
      animationFrames: this.animationFrames.size,
      observers: this.observers.size,
      isDestroyed: this.isDestroyed
    };
  }
}

/**
 * Weak reference cache for efficient data structures
 */
export class WeakCache<K extends object, V> {
  private cache = new WeakMap<K, V>();
  private keyRefs = new Set<WeakRef<K>>();
  private cleanupRegistry = new FinalizationRegistry<WeakRef<K>>((ref) => {
    this.keyRefs.delete(ref);
  });

  set(key: K, value: V): void {
    this.cache.set(key, value);
    
    const ref = new WeakRef(key);
    this.keyRefs.add(ref);
    this.cleanupRegistry.register(key, ref);
  }

  get(key: K): V | undefined {
    return this.cache.get(key);
  }

  has(key: K): boolean {
    return this.cache.has(key);
  }

  delete(key: K): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clean up dead references (called periodically)
   */
  cleanup(): void {
    const deadRefs: WeakRef<K>[] = [];
    
    this.keyRefs.forEach(ref => {
      if (ref.deref() === undefined) {
        deadRefs.push(ref);
      }
    });

    deadRefs.forEach(ref => this.keyRefs.delete(ref));
  }

  size(): number {
    return this.keyRefs.size;
  }
}

/**
 * Efficient list manager for large datasets
 */
export class VirtualListManager<T> {
  private items: T[] = [];
  private visibleRange = { start: 0, end: 0 };
  private itemHeight = 50;
  private containerHeight = 400;
  private scrollTop = 0;
  private overscan = 5;

  constructor(
    items: T[] = [],
    options: {
      itemHeight?: number;
      containerHeight?: number;
      overscan?: number;
    } = {}
  ) {
    this.items = items;
    this.itemHeight = options.itemHeight || 50;
    this.containerHeight = options.containerHeight || 400;
    this.overscan = options.overscan || 5;
    this.updateVisibleRange();
  }

  setItems(items: T[]): void {
    this.items = items;
    this.updateVisibleRange();
  }

  setScrollTop(scrollTop: number): void {
    this.scrollTop = scrollTop;
    this.updateVisibleRange();
  }

  private updateVisibleRange(): void {
    const visibleStart = Math.floor(this.scrollTop / this.itemHeight);
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(this.containerHeight / this.itemHeight),
      this.items.length - 1
    );

    this.visibleRange = {
      start: Math.max(0, visibleStart - this.overscan),
      end: Math.min(this.items.length - 1, visibleEnd + this.overscan)
    };
  }

  getVisibleItems(): { item: T; index: number }[] {
    return this.items
      .slice(this.visibleRange.start, this.visibleRange.end + 1)
      .map((item, i) => ({
        item,
        index: this.visibleRange.start + i
      }));
  }

  getTotalHeight(): number {
    return this.items.length * this.itemHeight;
  }

  getOffsetY(): number {
    return this.visibleRange.start * this.itemHeight;
  }

  getVisibleRange(): { start: number; end: number } {
    return { ...this.visibleRange };
  }
}

/**
 * Memory-efficient object pool
 */
export class ObjectPool<T> {
  private available: T[] = [];
  private inUse = new Set<T>();
  private factory: () => T;
  private reset: (obj: T) => void;
  private maxSize: number;

  constructor(
    factory: () => T,
    reset: (obj: T) => void = () => {},
    maxSize = 100
  ) {
    this.factory = factory;
    this.reset = reset;
    this.maxSize = maxSize;
  }

  acquire(): T {
    let obj = this.available.pop();
    
    if (!obj) {
      obj = this.factory();
    }

    this.inUse.add(obj);
    return obj;
  }

  release(obj: T): void {
    if (!this.inUse.has(obj)) {
      return; // Object not from this pool
    }

    this.inUse.delete(obj);
    this.reset(obj);

    if (this.available.length < this.maxSize) {
      this.available.push(obj);
    }
  }

  clear(): void {
    this.available.length = 0;
    this.inUse.clear();
  }

  getStats() {
    return {
      available: this.available.length,
      inUse: this.inUse.size,
      total: this.available.length + this.inUse.size
    };
  }
}

/**
 * Animation frame manager for preventing memory leaks in animations
 */
export class AnimationFrameManager {
  private activeFrames = new Set<number>();
  private frameCallbacks = new Map<number, () => void>();
  private isDestroyed = false;

  requestFrame(callback: () => void): number {
    if (this.isDestroyed) {
      return -1;
    }

    const frameId = requestAnimationFrame(() => {
      this.activeFrames.delete(frameId);
      this.frameCallbacks.delete(frameId);
      
      if (!this.isDestroyed) {
        callback();
      }
    });

    this.activeFrames.add(frameId);
    this.frameCallbacks.set(frameId, callback);
    
    return frameId;
  }

  cancelFrame(frameId: number): void {
    if (this.activeFrames.has(frameId)) {
      cancelAnimationFrame(frameId);
      this.activeFrames.delete(frameId);
      this.frameCallbacks.delete(frameId);
    }
  }

  cancelAllFrames(): void {
    this.activeFrames.forEach(frameId => {
      cancelAnimationFrame(frameId);
    });
    this.activeFrames.clear();
    this.frameCallbacks.clear();
  }

  destroy(): void {
    this.cancelAllFrames();
    this.isDestroyed = true;
  }

  getStats() {
    return {
      activeFrames: this.activeFrames.size,
      isDestroyed: this.isDestroyed
    };
  }
}

/**
 * Efficient data structure for managing large lists with memory optimization
 */
export class MemoryEfficientList<T> {
  private items: T[] = [];
  private visibleItems = new Map<number, T>();
  private itemPool: ObjectPool<HTMLElement>;
  private visibleRange = { start: 0, end: 0 };
  private itemHeight = 50;
  private containerHeight = 400;
  private overscan = 5;

  constructor(
    items: T[] = [],
    options: {
      itemHeight?: number;
      containerHeight?: number;
      overscan?: number;
      maxPoolSize?: number;
    } = {}
  ) {
    this.items = items;
    this.itemHeight = options.itemHeight || 50;
    this.containerHeight = options.containerHeight || 400;
    this.overscan = options.overscan || 5;

    // Create object pool for DOM elements
    this.itemPool = new ObjectPool(
      () => document.createElement('div'),
      (element) => {
        element.innerHTML = '';
        element.className = '';
        element.style.cssText = '';
      },
      options.maxPoolSize || 50
    );
  }

  setItems(items: T[]): void {
    this.items = items;
    this.visibleItems.clear();
    this.updateVisibleRange(0);
  }

  updateVisibleRange(scrollTop: number): void {
    const visibleStart = Math.floor(scrollTop / this.itemHeight);
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(this.containerHeight / this.itemHeight),
      this.items.length - 1
    );

    this.visibleRange = {
      start: Math.max(0, visibleStart - this.overscan),
      end: Math.min(this.items.length - 1, visibleEnd + this.overscan)
    };

    // Update visible items map
    this.visibleItems.clear();
    for (let i = this.visibleRange.start; i <= this.visibleRange.end; i++) {
      if (this.items[i]) {
        this.visibleItems.set(i, this.items[i]);
      }
    }
  }

  getVisibleItems(): Array<{ item: T; index: number }> {
    const result: Array<{ item: T; index: number }> = [];
    this.visibleItems.forEach((item, index) => {
      result.push({ item, index });
    });
    return result.sort((a, b) => a.index - b.index);
  }

  getTotalHeight(): number {
    return this.items.length * this.itemHeight;
  }

  getOffsetY(): number {
    return this.visibleRange.start * this.itemHeight;
  }

  acquireElement(): HTMLElement {
    return this.itemPool.acquire();
  }

  releaseElement(element: HTMLElement): void {
    this.itemPool.release(element);
  }

  cleanup(): void {
    this.visibleItems.clear();
    this.itemPool.clear();
  }

  getStats() {
    return {
      totalItems: this.items.length,
      visibleItems: this.visibleItems.size,
      visibleRange: this.visibleRange,
      poolStats: this.itemPool.getStats()
    };
  }
}

/**
 * Memory leak detector for development
 */
export class MemoryLeakDetector {
  private static instance: MemoryLeakDetector;
  private timers = new Set<number | NodeJS.Timeout>();
  private intervals = new Set<number | NodeJS.Timeout>();
  private listeners = new Set<{ element: EventTarget; event: string; handler: EventListener }>();
  private observers = new Set<{ disconnect: () => void }>();
  private animationFrames = new Set<number>();
  private isEnabled = process.env['NODE_ENV'] === 'development';

  static getInstance(): MemoryLeakDetector {
    if (!MemoryLeakDetector.instance) {
      MemoryLeakDetector.instance = new MemoryLeakDetector();
    }
    return MemoryLeakDetector.instance;
  }

  trackTimer(id: number | NodeJS.Timeout, type: 'timeout' | 'interval'): void {
    if (!this.isEnabled) return;
    
    if (type === 'timeout') {
      this.timers.add(id);
    } else {
      this.intervals.add(id);
    }
  }

  untrackTimer(id: number | NodeJS.Timeout, type: 'timeout' | 'interval'): void {
    if (!this.isEnabled) return;
    
    if (type === 'timeout') {
      this.timers.delete(id);
    } else {
      this.intervals.delete(id);
    }
  }

  trackEventListener(element: EventTarget, event: string, handler: EventListener): void {
    if (!this.isEnabled) return;
    this.listeners.add({ element, event, handler });
  }

  untrackEventListener(element: EventTarget, event: string, handler: EventListener): void {
    if (!this.isEnabled) return;
    this.listeners.forEach(listener => {
      if (listener.element === element && listener.event === event && listener.handler === handler) {
        this.listeners.delete(listener);
      }
    });
  }

  trackObserver(observer: { disconnect: () => void }): void {
    if (!this.isEnabled) return;
    this.observers.add(observer);
  }

  untrackObserver(observer: { disconnect: () => void }): void {
    if (!this.isEnabled) return;
    this.observers.delete(observer);
  }

  trackAnimationFrame(id: number): void {
    if (!this.isEnabled) return;
    this.animationFrames.add(id);
  }

  untrackAnimationFrame(id: number): void {
    if (!this.isEnabled) return;
    this.animationFrames.delete(id);
  }

  getLeakReport() {
    if (!this.isEnabled) return null;

    return {
      activeTimers: this.timers.size,
      activeIntervals: this.intervals.size,
      activeListeners: this.listeners.size,
      activeObservers: this.observers.size,
      activeAnimationFrames: this.animationFrames.size,
      timestamp: new Date().toISOString()
    };
  }

  logLeaks(): void {
    if (!this.isEnabled) return;

    const report = this.getLeakReport();
    if (report) {
      const hasLeaks = Object.values(report).some(value => 
        typeof value === 'number' && value > 0
      );

      if (hasLeaks) {
        console.warn('Potential memory leaks detected:', report);
      }
    }
  }

  cleanup(): void {
    this.timers.clear();
    this.intervals.clear();
    this.listeners.clear();
    this.observers.clear();
    this.animationFrames.clear();
  }
}

/**
 * Debounced function with automatic cleanup
 */
export function createDebouncedFunction<T extends (...args: unknown[]) => unknown>(
  func: T,
  delay: number,
  memoryManager?: ComponentMemoryManager
): T & { cancel: () => void; flush: () => void } {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastArgs: Parameters<T> | null = null;

  const debouncedFn = ((...args: Parameters<T>) => {
    lastArgs = args;
    
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      func(...args);
      timeoutId = null;
      lastArgs = null;
    }, delay);
  }) as T & { cancel: () => void; flush: () => void };

  debouncedFn.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
      lastArgs = null;
    }
  };

  debouncedFn.flush = () => {
    if (timeoutId && lastArgs) {
      clearTimeout(timeoutId);
      func(...lastArgs);
      timeoutId = null;
      lastArgs = null;
    }
  };

  // Register cleanup if memory manager provided
  if (memoryManager) {
    memoryManager.addCleanup(debouncedFn.cancel);
  }

  return debouncedFn;
}

/**
 * Throttled function with automatic cleanup
 */
export function createThrottledFunction<T extends (...args: unknown[]) => unknown>(
  func: T,
  delay: number,
  memoryManager?: ComponentMemoryManager
): T & { cancel: () => void } {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;

  const throttledFn = ((...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastExecTime >= delay) {
      func(...args);
      lastExecTime = now;
    } else if (!timeoutId) {
      timeoutId = setTimeout(() => {
        func(...args);
        lastExecTime = Date.now();
        timeoutId = null;
      }, delay - (now - lastExecTime));
    }
  }) as T & { cancel: () => void };

  throttledFn.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  // Register cleanup if memory manager provided
  if (memoryManager) {
    memoryManager.addCleanup(throttledFn.cancel);
  }

  return throttledFn;
}

// Define PerformanceMemory interface for better type safety
interface PerformanceMemory {
  readonly usedJSHeapSize: number;
  readonly totalJSHeapSize: number;
  readonly jsHeapSizeLimit: number;
}

/**
 * Global memory management utilities
 */
export const GlobalMemoryManager = {
  // Track global memory usage
  getMemoryUsage(): PerformanceMemory | null {
    if ('memory' in performance) {
      return (performance as Performance & { memory: PerformanceMemory }).memory;
    }
    return null;
  },

  // Force garbage collection (if available)
  forceGC(): void {
    if ('gc' in window) {
      (window as Window & { gc?: () => void }).gc?.();
    }
  },

  // Monitor memory usage
  startMemoryMonitoring(callback: (usage: PerformanceMemory) => void, interval = 5000): () => void {
    const monitor = () => {
      const usage = this.getMemoryUsage();
      if (usage) {
        callback(usage);
      }
    };

    const intervalId = setInterval(monitor, interval);
    
    return () => clearInterval(intervalId);
  },

  // Clean up global caches
  cleanupGlobalCaches(): void {
    // Clear any global caches you might have
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          if (name.includes('temp') || name.includes('cache')) {
            caches.delete(name);
          }
        });
      });
    }
  }
};