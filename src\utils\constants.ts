// Application constants and configuration

export const APP_CONFIG = {
  name: 'Rork',
  version: '1.0.0',
  description: 'AI-powered app generator',
  author: 'Rork Team',
} as const;

export const ROUTES = {
  HOME: '/',
  GENERATE: '/generate',
  BUILD: '/build',
  FORM_VALIDATION: '/form-validation',
  FORM_DEMO: '/form-demo',
} as const;

export const PROJECT_TYPES = {
  MOBILE: 'mobile',
  WEB: 'web',
  FORM: 'form',
  AI: 'ai',
  ECOMMERCE: 'ecommerce',
} as const;

export const PROJECT_STATUS = {
  ACTIVE: 'active',
  COMPLETED: 'completed',
  DRAFT: 'draft',
  ARCHIVED: 'archived',
  ERROR: 'error',
} as const;

export const PRIORITY_LEVELS = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low',
} as const;

export const COMPLEXITY_LEVELS = {
  SIMPLE: 'simple',
  MEDIUM: 'medium',
  COMPLEX: 'complex',
} as const;

export const QUICK_PROMPTS = [
  "Make a visual novel game",
  "Make an Airbnb-style app", 
  "Make an Instagram-style app",
  "Make a meditation timer",
  "Create a habit tracker",
  "Build a calorie tracker",
  "Create a todo list",
  "Design a weather dashboard",
  "Design a fitness tracker"
] as const;

export const TECH_STACK_OPTIONS = [
  'React Native',
  'TypeScript',
  'Expo',
  'React',
  'Next.js',
  'Node.js',
  'Express',
  'MongoDB',
  'PostgreSQL',
  'Firebase',
  'Supabase',
  'Tailwind CSS',
  'Styled Components',
  'Redux',
  'Zustand',
  'GraphQL',
  'REST API',
] as const;

export const ANIMATION_DURATIONS = {
  FAST: 200,
  NORMAL: 300,
  SLOW: 500,
  VERY_SLOW: 800,
} as const;

export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;