import React, { ReactNode } from 'react';
import { BaseErrorBoundary } from './BaseErrorBoundary';
import { ErrorType } from '../../types/error';

interface Props {
  children: ReactNode;
}

export const AppErrorBoundary: React.FC<Props> = ({ children }) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Log critical app-level errors
    console.error('Critical app error:', error, errorInfo);
    
    // In production, you might want to send this to an error tracking service
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { contexts: { react: errorInfo } });
    }
  };

  return (
    <BaseErrorBoundary
      errorType={ErrorType.CRITICAL_ERROR}
      onError={handleError}
      maxRetries={1}
      showErrorDetails={process.env.NODE_ENV === 'development'}
    >
      {children}
    </BaseErrorBoundary>
  );
};