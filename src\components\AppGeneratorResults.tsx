import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Download, Share, ExternalLink, Smartphone, Code2, FileText, Layers } from 'lucide-react';
import { useApp } from '../context/AppContext';

const AppGeneratorResults: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { projects } = useApp();

  const project = projects.find(p => p.id === projectId);

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-white text-xl mb-4">Project not found</h2>
          <button 
            onClick={() => navigate('/')}
            className="text-blue-400 hover:text-blue-300"
          >
            Go back to generator
          </button>
        </div>
      </div>
    );
  }

  const mockFiles = [
    { name: 'App.jsx', type: 'component', active: true },
    { name: 'components/Header.jsx', type: 'component' },
    { name: 'components/PropertyCard.jsx', type: 'component' },
    { name: 'components/SearchBar.jsx', type: 'component' },
    { name: 'styles/globals.css', type: 'style' },
    { name: 'utils/api.js', type: 'utility' },
    { name: 'package.json', type: 'config' }
  ];

  const mockCode = `import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import PropertyCard from './components/PropertyCard';
import SearchBar from './components/SearchBar';
import './styles/globals.css';

const App = () => {
  const [properties, setProperties] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Fetch properties from API
    fetchProperties();
  }, []);

  const fetchProperties = async () => {
    try {
      const response = await fetch('/api/properties');
      const data = await response.json();
      setProperties(data);
    } catch (error) {
      console.error('Error fetching properties:', error);
    }
  };

  const filteredProperties = properties.filter(property =>
    property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    property.location.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <SearchBar 
          value={searchQuery}
          onChange={setSearchQuery}
          placeholder="Search properties..."
        />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
          {filteredProperties.map(property => (
            <PropertyCard 
              key={property.id}
              property={property}
            />
          ))}
        </div>
      </main>
    </div>
  );
};

export default App;`;

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
        <div className="flex items-center gap-4">
          <button 
            onClick={() => navigate('/')}
            className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Generator</span>
          </button>
          <div className="h-4 w-px bg-gray-700"></div>
          <h1 className="text-white font-semibold">{project.name}</h1>
        </div>
        
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-3 py-1.5 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors">
            <Download className="w-4 h-4" />
            <span className="text-sm">Download</span>
          </button>
          <button className="flex items-center gap-2 px-3 py-1.5 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors">
            <Share className="w-4 h-4" />
            <span className="text-sm">Share</span>
          </button>
          <button className="flex items-center gap-2 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
            <ExternalLink className="w-4 h-4" />
            <span className="text-sm">Deploy</span>
          </button>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Left Side - Code Editor */}
        <div className="w-1/2 border-r border-gray-800 flex flex-col">
          {/* File Explorer */}
          <div className="border-b border-gray-800 p-4">
            <h3 className="text-white font-medium mb-3 flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Generated Files
            </h3>
            <div className="space-y-1">
              {mockFiles.map((file, index) => (
                <div 
                  key={index}
                  className={`flex items-center gap-2 px-2 py-1.5 rounded text-sm cursor-pointer transition-colors ${
                    file.active ? 'bg-gray-800 text-white' : 'text-gray-400 hover:text-gray-300 hover:bg-gray-800/50'
                  }`}
                >
                  {file.type === 'component' && <Code2 className="w-3 h-3" />}
                  {file.type === 'style' && <Layers className="w-3 h-3" />}
                  {file.type === 'utility' && <FileText className="w-3 h-3" />}
                  {file.type === 'config' && <FileText className="w-3 h-3" />}
                  <span>{file.name}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Code Viewer */}
          <div className="flex-1 p-4">
            <div className="bg-gray-950 rounded-lg p-4 h-full overflow-auto">
              <pre className="text-gray-300 text-sm leading-relaxed">
                <code>{mockCode}</code>
              </pre>
            </div>
          </div>
        </div>

        {/* Right Side - Preview */}
        <div className="w-1/2 p-8 flex flex-col items-center justify-center bg-gray-800/30">
          <div className="text-center mb-8">
            <h2 className="text-white text-xl font-semibold mb-2">
              <Smartphone className="w-5 h-5 inline mr-2" />
              Live Preview
            </h2>
            <p className="text-gray-400 text-sm">Test your app on mobile</p>
          </div>

          {/* Phone Mockup */}
          <div className="relative">
            <div className="w-64 h-[500px] bg-black rounded-[3rem] p-4 shadow-2xl">
              <div className="w-full h-full bg-white rounded-[2rem] overflow-hidden">
                {/* Status Bar */}
                <div className="flex justify-between items-center px-6 py-2 bg-white">
                  <span className="text-xs font-semibold">9:41</span>
                  <div className="flex items-center gap-1">
                    <div className="w-4 h-2 border border-black rounded-sm">
                      <div className="w-3 h-1 bg-black rounded-sm"></div>
                    </div>
                  </div>
                </div>

                {/* App Content */}
                <div className="p-4 h-full bg-gray-50">
                  <div className="flex items-center justify-between mb-4">
                    <h1 className="text-lg font-bold">Find Home</h1>
                    <div className="flex gap-2">
                      <div className="w-6 h-6 bg-gray-300 rounded"></div>
                      <div className="w-6 h-6 bg-gray-300 rounded"></div>
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-lg p-3 mb-4 shadow-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-gray-300 rounded"></div>
                      <div className="h-2 bg-gray-200 rounded flex-1"></div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                      <div className="h-32 bg-gradient-to-br from-orange-200 to-orange-300 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded mb-1"></div>
                      <div className="h-2 bg-gray-200 rounded w-2/3"></div>
                    </div>
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                      <div className="h-20 bg-gradient-to-br from-blue-200 to-blue-300 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded mb-1"></div>
                      <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* QR Code Section */}
          <div className="mt-8 text-center">
            <div className="w-24 h-24 bg-white rounded-lg mb-3 flex items-center justify-center mx-auto">
              <div className="w-20 h-20 bg-black rounded opacity-80" style={{
                backgroundImage: `url("data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='qr' patternUnits='userSpaceOnUse' width='10' height='10'%3e%3crect width='10' height='10' fill='white'/%3e%3crect width='5' height='5' fill='black'/%3e%3crect x='5' y='5' width='5' height='5' fill='black'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100' height='100' fill='url(%23qr)'/%3e%3c/svg%3e")`,
                backgroundSize: 'cover'
              }}></div>
            </div>
            <h3 className="text-white font-medium mb-1">Test on your phone</h3>
            <p className="text-gray-400 text-xs">Scan QR code to test</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppGeneratorResults; 