import React from 'react';
import { motion, HTMLMotionProps, Variants } from 'framer-motion';
import { animationVariants } from '../../utils/animations';

interface AnimatedWrapperProps extends Omit<HTMLMotionProps<'div'>, 'variants'> {
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
  variant?: keyof typeof animationVariants;
  customVariants?: Variants;
  delay?: number;
  duration?: number;
}

export const AnimatedWrapper: React.FC<AnimatedWrapperProps> = ({
  children,
  as = 'div',
  variant = 'fadeIn',
  customVariants,
  delay = 0,
  duration,
  ...props
}) => {
  const variants = customVariants || animationVariants[variant];
  
  // Apply custom timing if provided
  const finalVariants = duration || delay ? {
    ...variants,
    animate: {
      ...variants.animate,
      transition: {
        ...variants.animate?.transition,
        duration,
        delay
      }
    }
  } : variants;

  const MotionComponent = motion[as as keyof typeof motion] as any;

  return (
    <MotionComponent
      variants={finalVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      {...props}
    >
      {children}
    </MotionComponent>
  );
};

export default AnimatedWrapper;